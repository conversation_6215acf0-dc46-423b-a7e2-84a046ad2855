/**
 * DeepSeek API 代理模块
 * 封装所有与 DeepSeek API 进行通信的函数，实现 Token 消耗控制
 */

import axios from 'axios';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// 加载环境变量
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
config({ path: join(__dirname, '../.env.local') });

// DeepSeek API 配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_API_ENDPOINT = process.env.LLM_API_ENDPOINT || 'https://api.deepseek.com/v1';

if (!DEEPSEEK_API_KEY) {
    throw new Error('Missing DEEPSEEK_API_KEY environment variable');
}

// Token 限制配置
const DEFAULT_MAX_TOKENS = 1000; // 默认最大返回 Token 数
const MAX_CONTEXT_TOKENS = 8000; // 最大上下文 Token 数
const EMERGENCY_MAX_TOKENS = 2000; // 紧急情况下的最大 Token 数

/**
 * 估算文本的 Token 数量（粗略估算）
 * 中文：1个字符约等于1个Token
 * 英文：1个单词约等于1.3个Token
 */
export function estimateTokens(text) {
    if (!text) return 0;
    
    // 统计中文字符
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    
    // 统计英文单词
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    
    // 其他字符（标点、数字等）
    const otherChars = text.length - chineseChars - englishWords;
    
    return Math.ceil(chineseChars + englishWords * 1.3 + otherChars * 0.5);
}

/**
 * 截断文本以适应 Token 限制
 */
function truncateText(text, maxTokens) {
    const estimatedTokens = estimateTokens(text);
    
    if (estimatedTokens <= maxTokens) {
        return text;
    }
    
    // 按比例截断
    const ratio = maxTokens / estimatedTokens;
    const targetLength = Math.floor(text.length * ratio * 0.9); // 留10%余量
    
    return text.substring(0, targetLength) + '...';
}

/**
 * 构建 DeepSeek API 请求
 */
async function makeDeepSeekRequest(messages, options = {}) {
    const {
        model = 'deepseek-chat',
        temperature = 0.7,
        maxTokens = DEFAULT_MAX_TOKENS,
        stream = false
    } = options;
    
    try {
        const response = await axios.post(
            `${DEEPSEEK_API_ENDPOINT}/chat/completions`,
            {
                model,
                messages,
                temperature,
                max_tokens: Math.min(maxTokens, EMERGENCY_MAX_TOKENS),
                stream,
                top_p: 0.9,
                frequency_penalty: 0.1,
                presence_penalty: 0.1
            },
            {
                headers: {
                    'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                timeout: 90000 // 90秒超时，API实际需要30-60秒
            }
        );
        
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`DeepSeek API Error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`);
        } else if (error.request) {
            throw new Error('DeepSeek API: Network error or timeout');
        } else {
            throw new Error(`DeepSeek API: ${error.message}`);
        }
    }
}

/**
 * 管理对话上下文，防止 Token 超限
 */
function manageContext(messages, maxContextTokens = MAX_CONTEXT_TOKENS) {
    if (!Array.isArray(messages) || messages.length === 0) {
        return messages;
    }
    
    // 计算总 Token 数
    let totalTokens = 0;
    const managedMessages = [];
    
    // 从最新消息开始，向前累加
    for (let i = messages.length - 1; i >= 0; i--) {
        const message = messages[i];
        const messageTokens = estimateTokens(message.content);
        
        if (totalTokens + messageTokens > maxContextTokens) {
            // 如果是系统消息，尝试截断而不是丢弃
            if (message.role === 'system') {
                const remainingTokens = maxContextTokens - totalTokens;
                if (remainingTokens > 100) { // 至少保留100个Token的系统消息
                    managedMessages.unshift({
                        ...message,
                        content: truncateText(message.content, remainingTokens)
                    });
                }
            }
            break;
        }
        
        totalTokens += messageTokens;
        managedMessages.unshift(message);
    }
    
    return managedMessages;
}

/**
 * 核心 DeepSeek 调用函数
 */
export async function callDeepSeek(prompt, options = {}) {
    const {
        temperature = 0.7,
        maxTokens = DEFAULT_MAX_TOKENS,
        systemPrompt = null,
        contextMessages = [],
        traceId = `trace_${Date.now()}`,
        sessionId = 'unknown'
    } = options;

    const llmCallStart = Date.now();
    
    // 构建消息数组
    const messages = [];
    
    // 添加系统提示
    if (systemPrompt) {
        messages.push({
            role: 'system',
            content: systemPrompt
        });
    }
    
    // 添加上下文消息
    if (contextMessages && contextMessages.length > 0) {
        messages.push(...contextMessages);
    }
    
    // 添加用户提示
    messages.push({
        role: 'user',
        content: prompt
    });
    
    // 管理上下文长度
    console.log('Original messages:', messages);
    const managedMessages = manageContext(messages);
    console.log('Managed messages:', managedMessages);

    if (!managedMessages || managedMessages.length === 0) {
        throw new Error('Empty input messages after context management');
    }

    // 记录LLM调用开始日志
    console.log(JSON.stringify({
        timestamp: new Date().toISOString(),
        level: "INFO",
        traceId,
        sessionId,
        component: "deepseekAgent",
        event: "llm_call",
        message: "开始LLM调用",
        data: {
            llmDetails: {
                prompt: prompt.substring(0, 200) + (prompt.length > 200 ? "..." : ""),
                temperature,
                maxTokens
            }
        }
    }));

    // 调用 API
    const response = await makeDeepSeekRequest(managedMessages, {
        temperature,
        maxTokens
    });

    const llmCallTime = Date.now() - llmCallStart;

    // 提取响应内容和使用统计
    const content = response.choices?.[0]?.message?.content || '';
    const usage = response.usage || {};

    // 记录LLM调用完成日志
    console.log(JSON.stringify({
        timestamp: new Date().toISOString(),
        level: "INFO",
        traceId,
        sessionId,
        component: "deepseekAgent",
        event: "llm_call",
        message: "LLM调用完成",
        data: {
            timing: {
                llmCallTimeMs: llmCallTime
            },
            llmDetails: {
                response: content.substring(0, 100) + (content.length > 100 ? "..." : ""),
                tokensUsed: usage.total_tokens || 0,
                model: response.model || 'deepseek-chat'
            }
        }
    }));

    return {
        content: content.trim(),
        tokensUsed: usage.total_tokens || 0,
        promptTokens: usage.prompt_tokens || 0,
        completionTokens: usage.completion_tokens || 0,
        model: response.model || 'deepseek-chat'
    };
}

/**
 * 信息提取专用调用（优化版）
 */
export async function extractInformation(userMessage, extractionPrompt, options = {}) {
    // 支持自定义系统角色，默认使用合并的信息提取和意图识别角色
    const defaultSystemPrompt = `你是一个专业的对话分析助手。请从用户消息中提取关键信息，并识别其主要意图。`;
    const systemPrompt = options.systemPrompt || defaultSystemPrompt;

    const fullPrompt = `${extractionPrompt}

用户消息：${userMessage}

请提取相关信息并返回JSON格式的结果。`;

    return await callDeepSeek(fullPrompt, {
        ...options,
        systemPrompt,
        temperature: options.temperature || 0.2, // 优化：降低温度以确保结构化输出稳定性
        maxTokens: options.maxTokens || 600, // 增加token限制以支持合并输出
        traceId: options.traceId,
        sessionId: options.sessionId
    });
}

/**
 * 对话生成专用调用
 */
export async function generateResponse(userMessage, conversationContext, responsePrompt, options = {}) {
    const systemPrompt = `你是 Katrina，一位专业的AI猎头顾问。请根据对话上下文和指导原则，生成自然、专业的回复。`;
    
    const fullPrompt = `${responsePrompt}

对话上下文：
${conversationContext}

用户最新消息：${userMessage}

请生成 Katrina 的回复：`;
    
    return await callDeepSeek(fullPrompt, {
        ...options,
        systemPrompt,
        temperature: options.temperature || 0.8,
        maxTokens: options.maxTokens || 800,
        traceId: options.traceId,
        sessionId: options.sessionId
    });
}

/**
 * 意图识别专用调用
 */
export async function recognizeIntent(userMessage, intentPrompt, options = {}) {
    const systemPrompt = `你是一个意图识别专家。请分析用户消息的意图，返回结构化的结果。`;
    
    const fullPrompt = `${intentPrompt}

用户消息：${userMessage}

请识别用户意图并返回JSON格式的结果。`;
    
    return await callDeepSeek(fullPrompt, {
        ...options,
        systemPrompt,
        temperature: 0.2, // 意图识别使用很低的温度
        maxTokens: options.maxTokens || 300,
        traceId: options.traceId,
        sessionId: options.sessionId
    });
}

/**
 * 批量处理多个请求（控制并发）
 */
export async function batchProcess(requests, concurrency = 3) {
    const results = [];
    
    for (let i = 0; i < requests.length; i += concurrency) {
        const batch = requests.slice(i, i + concurrency);
        const batchPromises = batch.map(async (request) => {
            try {
                return await callDeepSeek(request.prompt, request.options);
            } catch (error) {
                return { error: error.message, request };
            }
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
    }
    
    return results;
}

/**
 * 获取 Token 使用统计
 */
export function getTokenStats() {
    return {
        defaultMaxTokens: DEFAULT_MAX_TOKENS,
        maxContextTokens: MAX_CONTEXT_TOKENS,
        emergencyMaxTokens: EMERGENCY_MAX_TOKENS,
        estimateTokens: estimateTokens
    };
}
