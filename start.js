#!/usr/bin/env node

/**
 * AI招聘助手系统启动脚本
 * 提供多种启动模式和环境检查
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync, existsSync } from 'fs';
import { createServer } from 'http';
import { parse } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ==================== 配置 ====================

const CONFIG = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || 'localhost',
  env: process.env.NODE_ENV || 'development'
};

// ==================== 环境检查 ====================

function checkEnvironment() {
  console.log('🔍 检查运行环境...');
  
  const requiredFiles = [
    'src/index.js',
    'src/core/message-processor.js',
    'src/core/database-manager.js',
    'package.json'
  ];

  const missingFiles = requiredFiles.filter(file => !existsSync(join(__dirname, file)));
  
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    return false;
  }

  // 检查环境变量
  const envFile = join(__dirname, '.env.local');
  if (!existsSync(envFile)) {
    console.warn('⚠️  未找到 .env.local 文件，某些功能可能无法正常工作');
    console.warn('   请参考 README.md 配置环境变量');
  }

  console.log('✅ 环境检查通过');
  return true;
}

// ==================== 静态文件服务 ====================

function serveStaticFile(res, filePath, contentType) {
  try {
    const content = readFileSync(filePath);
    res.writeHead(200, { 'Content-Type': contentType });
    res.end(content);
  } catch (error) {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('File not found');
  }
}

function getContentType(filePath) {
  const ext = filePath.split('.').pop().toLowerCase();
  const types = {
    'html': 'text/html; charset=utf-8',
    'js': 'application/javascript; charset=utf-8',
    'css': 'text/css; charset=utf-8',
    'json': 'application/json; charset=utf-8',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml'
  };
  return types[ext] || 'text/plain';
}

// ==================== HTTP服务器 ====================

async function createHttpServer() {
  // 动态导入系统模块
  let aiSystem = null;
  try {
    const systemModule = await import('./src/index.js');
    aiSystem = systemModule.default;
    console.log('✅ AI招聘助手系统模块加载成功');
  } catch (error) {
    console.warn('⚠️  AI系统模块加载失败，运行在静态模式');
    console.warn('   错误:', error.message);
  }

  const server = createServer(async (req, res) => {
    const parsedUrl = parse(req.url, true);
    const { pathname, query } = parsedUrl;

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    try {
      // API路由
      if (pathname.startsWith('/api/')) {
        if (!aiSystem) {
          res.writeHead(503, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'AI系统未初始化',
            code: 'SYSTEM_NOT_INITIALIZED'
          }));
          return;
        }

        // 处理API请求
        await handleApiRequest(req, res, aiSystem, pathname, query);
        return;
      }

      // 静态文件路由
      if (pathname === '/' || pathname === '/index.html') {
        serveStaticFile(res, join(__dirname, 'demo.html'), 'text/html; charset=utf-8');
      } else if (pathname === '/demo.html') {
        serveStaticFile(res, join(__dirname, 'demo.html'), 'text/html; charset=utf-8');
      } else if (pathname.startsWith('/src/')) {
        const filePath = join(__dirname, pathname);
        if (existsSync(filePath)) {
          serveStaticFile(res, filePath, getContentType(filePath));
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('File not found');
        }
      } else {
        // 404页面
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>404 - 页面未找到</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              h1 { color: #333; }
              a { color: #007bff; text-decoration: none; }
            </style>
          </head>
          <body>
            <h1>404 - 页面未找到</h1>
            <p>请求的页面不存在</p>
            <a href="/">返回首页</a>
          </body>
          </html>
        `);
      }
    } catch (error) {
      console.error('请求处理错误:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: '服务器内部错误',
        code: 'INTERNAL_SERVER_ERROR'
      }));
    }
  });

  return server;
}

// ==================== API请求处理 ====================

async function handleApiRequest(req, res, aiSystem, pathname, query) {
  const method = req.method;
  const route = `${method} ${pathname}`;

  // 读取请求体
  let body = '';
  if (method === 'POST' || method === 'PUT') {
    await new Promise((resolve) => {
      req.on('data', chunk => {
        body += chunk.toString();
      });
      req.on('end', resolve);
    });
  }

  let requestData = {};
  if (body) {
    try {
      requestData = JSON.parse(body);
    } catch (error) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: '无效的JSON格式',
        code: 'INVALID_JSON'
      }));
      return;
    }
  }

  let result;

  // 路由处理
  switch (route) {
    case 'POST /api/chat':
      result = await aiSystem.processMessage(
        requestData.message,
        requestData.sessionUuid
      );
      break;

    case 'GET /api/health':
      result = await aiSystem.performHealthCheck();
      break;

    case 'GET /api/status':
      result = {
        success: true,
        data: aiSystem.getSystemStatus()
      };
      break;

    default:
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: '接口不存在',
        code: 'NOT_FOUND'
      }));
      return;
  }

  // 返回结果
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(result));
}

// ==================== 主启动函数 ====================

async function start() {
  console.log('🚀 AI招聘助手系统启动中...');
  console.log(`📍 环境: ${CONFIG.env}`);
  console.log(`🌐 地址: http://${CONFIG.host}:${CONFIG.port}`);
  console.log('');

  // 环境检查
  if (!checkEnvironment()) {
    process.exit(1);
  }

  try {
    // 创建HTTP服务器
    const server = await createHttpServer();

    // 启动服务器
    server.listen(CONFIG.port, CONFIG.host, () => {
      console.log('✅ 服务器启动成功!');
      console.log('');
      console.log('📱 访问地址:');
      console.log(`   🏠 主页: http://${CONFIG.host}:${CONFIG.port}`);
      console.log(`   🤖 演示: http://${CONFIG.host}:${CONFIG.port}/demo.html`);
      console.log(`   ❤️  健康检查: http://${CONFIG.host}:${CONFIG.port}/api/health`);
      console.log('');
      console.log('💡 提示:');
      console.log('   - 按 Ctrl+C 停止服务器');
      console.log('   - 查看 README.md 了解更多功能');
      console.log('   - 配置 .env.local 启用完整功能');
      console.log('');
    });

    // 优雅关闭
    process.on('SIGINT', () => {
      console.log('\n🔄 正在关闭服务器...');
      server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

// ==================== 命令行参数处理 ====================

function showHelp() {
  console.log(`
AI招聘助手系统启动脚本

用法:
  node start.js [选项]

选项:
  --port <端口>     指定服务器端口 (默认: 3000)
  --host <主机>     指定服务器主机 (默认: localhost)
  --env <环境>      指定运行环境 (默认: development)
  --help           显示帮助信息

示例:
  node start.js                    # 使用默认配置启动
  node start.js --port 8080        # 在端口8080启动
  node start.js --host 0.0.0.0     # 监听所有网络接口
  node start.js --env production   # 生产环境模式

环境变量:
  PORT             服务器端口
  HOST             服务器主机
  NODE_ENV         运行环境
  SUPABASE_URL     Supabase数据库URL
  SUPABASE_ANON_KEY Supabase匿名密钥
`);
}

// 解析命令行参数
const args = process.argv.slice(2);
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--port':
      CONFIG.port = parseInt(args[++i]) || CONFIG.port;
      break;
    case '--host':
      CONFIG.host = args[++i] || CONFIG.host;
      break;
    case '--env':
      CONFIG.env = args[++i] || CONFIG.env;
      process.env.NODE_ENV = CONFIG.env;
      break;
    case '--help':
    case '-h':
      showHelp();
      process.exit(0);
      break;
    default:
      console.error(`❌ 未知参数: ${args[i]}`);
      console.error('使用 --help 查看帮助信息');
      process.exit(1);
  }
}

// 启动系统
start().catch(error => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});
