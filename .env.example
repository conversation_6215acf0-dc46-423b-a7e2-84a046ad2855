# AI招聘助手系统环境配置模板
# 复制此文件为 .env.local 并填入实际配置值

# ==================== 基础配置 ====================

# 运行环境 (development, production, test)
NODE_ENV=development

# 服务器配置
PORT=3000
HOST=localhost

# ==================== 数据库配置 ====================

# Supabase 数据库配置 (必需)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# 示例:
# SUPABASE_URL=https://your-project.supabase.co
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ==================== AI服务配置 ====================

# DeepSeek API配置 (可选，用于AI功能)
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_URL=https://api.deepseek.com

# OpenAI API配置 (可选，备用AI服务)
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1

# ==================== 功能开关 ====================

# 启用AI功能 (true/false)
ENABLE_AI_SERVICES=true

# 启用缓存 (true/false)
ENABLE_CACHE=true

# 启用日志记录 (true/false)
ENABLE_LOGGING=true

# 启用性能监控 (true/false)
ENABLE_MONITORING=true

# ==================== 安全配置 ====================

# JWT密钥 (用于用户认证)
JWT_SECRET=your_jwt_secret_key

# 会话密钥 (用于会话加密)
SESSION_SECRET=your_session_secret_key

# CORS允许的域名 (多个域名用逗号分隔)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# ==================== 缓存配置 ====================

# Redis配置 (可选，用于缓存)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# 内存缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ==================== 日志配置 ====================

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 日志文件路径
LOG_FILE_PATH=./logs/app.log

# 是否输出到控制台 (true/false)
LOG_TO_CONSOLE=true

# ==================== 推荐引擎配置 ====================

# 推荐缓存时间 (分钟)
RECOMMENDATION_CACHE_TTL=30

# 每个分类最大推荐数量
MAX_JOBS_PER_CATEGORY=4

# 启用替补机制 (true/false)
ENABLE_FALLBACK_MECHANISM=true

# ==================== 第三方服务配置 ====================

# 邮件服务配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# 短信服务配置 (可选)
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret

# ==================== 监控和分析 ====================

# Sentry错误监控 (可选)
SENTRY_DSN=your_sentry_dsn

# Google Analytics (可选)
GA_TRACKING_ID=your_ga_tracking_id

# ==================== 开发配置 ====================

# 开发模式下的调试选项
DEBUG_MODE=false
VERBOSE_LOGGING=false

# 测试数据库配置 (仅用于测试)
TEST_SUPABASE_URL=your_test_supabase_url
TEST_SUPABASE_ANON_KEY=your_test_supabase_anon_key

# ==================== 部署配置 ====================

# Vercel部署配置
VERCEL_URL=your_vercel_app_url

# Docker配置
DOCKER_IMAGE_TAG=latest

# ==================== 配置说明 ====================

# 必需配置项:
# - SUPABASE_URL: Supabase项目URL
# - SUPABASE_ANON_KEY: Supabase匿名访问密钥
# - SUPABASE_SERVICE_ROLE_KEY: Supabase服务角色密钥

# 推荐配置项:
# - DEEPSEEK_API_KEY: 用于AI对话功能
# - JWT_SECRET: 用于用户认证安全
# - SESSION_SECRET: 用于会话安全

# 可选配置项:
# - Redis相关: 用于提升性能
# - 邮件/短信: 用于通知功能
# - 监控相关: 用于生产环境监控

# ==================== 获取配置值的方法 ====================

# 1. Supabase配置:
#    - 访问 https://supabase.com
#    - 创建新项目或选择现有项目
#    - 在项目设置 > API 中找到URL和密钥

# 2. DeepSeek API:
#    - 访问 https://platform.deepseek.com
#    - 注册账号并获取API密钥

# 3. JWT和Session密钥:
#    - 可以使用在线生成器生成随机字符串
#    - 或使用命令: openssl rand -hex 32

# ==================== 安全提醒 ====================

# ⚠️ 重要安全提醒:
# 1. 不要将 .env.local 文件提交到版本控制系统
# 2. 生产环境使用强密码和复杂密钥
# 3. 定期轮换API密钥和访问令牌
# 4. 限制CORS域名为实际需要的域名
# 5. 在生产环境中禁用调试模式
