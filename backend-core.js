/**
 * Katrina AI 后端核心业务逻辑处理中心
 * 包含消息路由、12个对话处理器、业务流程编排
 */

import { createClient } from '@supabase/supabase-js';
import { callDeepSeek } from './lib/deepseekAgent.js';
import { userManager, sessionManager } from './user-management.js';
import { jobMatchingEngine, recommendationEngine, jobAnalyzer } from './job-engine.js';
import { contextAnalyzer, informationExtractor } from './ai-services.js';
import { validateChatMessage } from './validators.js';
import { APIError } from './error-handlers.js';
import { generateId, formatResponse } from './utilities.js';
import { 
  MESSAGE_TYPES, 
  PROCESSOR_TYPES, 
  INTENT_MAPPING,
  FIXED_RESPONSES,
  API_TIERS 
} from './config.js';

// 初始化数据库连接
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * 核心消息处理器类
 */
export class MessageProcessor {
  constructor() {
    this.processors = new Map();
    this.ambiguityStates = new Map(); // 歧义状态管理
    this.recommendationCache = new Map(); // 推荐缓存
    this.cacheExpiryTime = 30 * 60 * 1000; // 30分钟缓存
    this.initializeProcessors();
  }

  /**
   * 初始化12个对话处理器
   */
  initializeProcessors() {
    // 基础对话处理器
    this.processors.set('greeting', this.handleGreeting.bind(this));
    this.processors.set('job_inquiry', this.handleJobInquiry.bind(this));
    this.processors.set('resume_upload', this.handleResumeUpload.bind(this));
    this.processors.set('tech_discussion', this.handleTechDiscussion.bind(this));
    
    // 推荐相关处理器
    this.processors.set('job_recommendation', this.handleJobRecommendation.bind(this));
    this.processors.set('company_inquiry', this.handleCompanyInquiry.bind(this));
    this.processors.set('salary_discussion', this.handleSalaryDiscussion.bind(this));
    this.processors.set('career_advice', this.handleCareerAdvice.bind(this));
    
    // 高级功能处理器
    this.processors.set('interview_prep', this.handleInterviewPrep.bind(this));
    this.processors.set('skill_assessment', this.handleSkillAssessment.bind(this));
    this.processors.set('market_analysis', this.handleMarketAnalysis.bind(this));
    this.processors.set('followup', this.handleFollowup.bind(this));
  }

  /**
   * 主消息路由函数
   */
  async processMessage(message, userEmail, sessionId = null) {
    try {
      console.log(`🚀 开始处理消息: ${message.substring(0, 50)}...`);

      // 1. 验证输入
      const validation = validateChatMessage({ message, userEmail, sessionId });
      if (!validation.valid) {
        throw new APIError(validation.error, 'VALIDATION_ERROR', 400);
      }

      // 2. 获取或创建用户和会话
      const user = await userManager.getOrCreateUser(userEmail);
      const session = sessionId 
        ? await sessionManager.getSession(sessionId)
        : await sessionManager.createSession(userEmail);

      // 3. 保存用户消息
      const messageId = await this.saveMessage(session.id, MESSAGE_TYPES.USER, message);

      // 4. 获取对话历史
      const conversationHistory = await sessionManager.getChatHistory(session.id, { limit: 10 });

      // 5. 获取候选人档案
      const candidateInfo = await userManager.getCandidateProfile(userEmail) || {};

      // 6. 检查固定话术
      const fixedResponse = this.checkFixedResponses(message);
      if (fixedResponse) {
        const response = {
          reply: fixedResponse,
          tokensUsed: 0,
          apiTier: API_TIERS.TIER_1,
          recommendations: null
        };
        
        await this.saveMessage(session.id, MESSAGE_TYPES.ASSISTANT, response.reply, {
          tokensUsed: 0,
          apiTier: API_TIERS.TIER_1,
          handlerType: 'FixedResponseHandler'
        });

        return this.formatFinalResponse(response, session.id, messageId);
      }

      // 7. 上下文分析
      const contextAnalysis = await contextAnalyzer.analyzeContext(
        message, 
        conversationHistory.messages, 
        candidateInfo
      );

      console.log(`🧠 上下文分析结果:`, contextAnalysis);

      // 8. 智能路由选择处理器
      const handler = this.selectHandler(contextAnalysis, message);
      console.log(`🎯 选择处理器: ${handler.name}`);

      // 9. 执行处理
      const response = await handler.process(message, contextAnalysis, candidateInfo, user.id, session.id);

      // 10. 保存助手回复
      await this.saveMessage(session.id, MESSAGE_TYPES.ASSISTANT, response.reply, {
        tokensUsed: response.tokensUsed,
        apiTier: response.apiTier,
        hasRecommendations: !!response.recommendations,
        handlerType: handler.name,
        contextIntent: contextAnalysis.intent
      });

      // 11. 更新候选人档案（如果有新信息）
      if (response.candidateInfo) {
        await userManager.updateCandidateProfile(userEmail, response.candidateInfo);
      }

      return this.formatFinalResponse(response, session.id, messageId);

    } catch (error) {
      console.error('消息处理失败:', error);
      return this.generateErrorResponse(error);
    }
  }

  /**
   * 智能路由选择处理器
   */
  selectHandler(contextAnalysis, userMessage = '') {
    const { intent, context, needsRecommendation, hasRecommendedJobs, specificJobMentioned, isResumeUpload } = contextAnalysis;

    // 1. 可信度询问处理器（优先级最高）
    if (this.detectCredibilityInquiry(userMessage)) {
      return {
        name: 'CredibilityHandler',
        process: async () => ({
          reply: '明确表示是靠谱的，我们的同事每周更新一次职位。',
          tokensUsed: 0,
          apiTier: API_TIERS.TIER_1,
          recommendations: null
        })
      };
    }

    // 2. 简历上传处理器
    if (isResumeUpload || this.detectResumeUpload(userMessage)) {
      return {
        name: 'ResumeUploadHandler',
        process: this.handleResumeUpload.bind(this)
      };
    }

    // 3. 公司询问处理器
    if (intent === 'company_inquiry' || this.detectCompanyInquiry(userMessage)) {
      return {
        name: 'CompanyInquiryHandler',
        process: this.handleCompanyInquiry.bind(this)
      };
    }

    // 4. 职位推荐处理器
    if (needsRecommendation || intent === 'job_recommendation') {
      return {
        name: 'JobRecommendationHandler',
        process: this.handleJobRecommendation.bind(this)
      };
    }

    // 5. 职位问题处理器
    if (specificJobMentioned || intent === 'job_inquiry') {
      return {
        name: 'JobQuestionHandler',
        process: this.handleJobInquiry.bind(this)
      };
    }

    // 6. 偏好表达处理器
    if (intent === 'preference_expression') {
      return {
        name: 'PreferenceHandler',
        process: this.handlePreferenceExpression.bind(this)
      };
    }

    // 7. 信息收集处理器
    if (intent === 'info_collection') {
      return {
        name: 'InfoCollectionHandler',
        process: this.handleInfoCollection.bind(this)
      };
    }

    // 8. 技术讨论处理器
    if (intent === 'tech_discussion') {
      return {
        name: 'TechDiscussionHandler',
        process: this.handleTechDiscussion.bind(this)
      };
    }

    // 9. 薪资讨论处理器
    if (intent === 'salary_discussion') {
      return {
        name: 'SalaryDiscussionHandler',
        process: this.handleSalaryDiscussion.bind(this)
      };
    }

    // 10. 职业建议处理器
    if (intent === 'career_advice') {
      return {
        name: 'CareerAdviceHandler',
        process: this.handleCareerAdvice.bind(this)
      };
    }

    // 11. 面试准备处理器
    if (intent === 'interview_prep') {
      return {
        name: 'InterviewPrepHandler',
        process: this.handleInterviewPrep.bind(this)
      };
    }

    // 12. 技能评估处理器
    if (intent === 'skill_assessment') {
      return {
        name: 'SkillAssessmentHandler',
        process: this.handleSkillAssessment.bind(this)
      };
    }

    // 13. 市场分析处理器
    if (intent === 'market_analysis') {
      return {
        name: 'MarketAnalysisHandler',
        process: this.handleMarketAnalysis.bind(this)
      };
    }

    // 14. 跟进处理器
    if (intent === 'followup') {
      return {
        name: 'FollowupHandler',
        process: this.handleFollowup.bind(this)
      };
    }

    // 默认：通用对话处理器
    return {
      name: 'GeneralChatHandler',
      process: this.handleGeneralChat.bind(this)
    };
  }

  // ==================== 12个对话处理器 ====================

  async handleGreeting(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('🤝 处理问候消息');

    // 检查是否是新用户
    const isNewUser = !candidateInfo || Object.keys(candidateInfo).length === 0;

    let reply;
    if (isNewUser) {
      reply = `您好！我是Katrina，您的AI招聘顾问。很高兴为您服务！

为了给您推荐最合适的职位，我想了解一下您的背景：
• 您目前从事什么技术方向？
• 有多少年工作经验？
• 期望的工作地点是哪里？

请随时告诉我您的情况，我会为您匹配最适合的机会。`;
    } else {
      const { tech_direction, experience_years, preferred_location } = candidateInfo;
      reply = `欢迎回来！我是Katrina，您的AI招聘顾问。

根据您的档案，您是${tech_direction || '技术'}方向，${experience_years || '若干'}年经验的候选人。

今天我可以为您：
• 推荐最新的职位机会
• 分析市场趋势
• 提供面试指导
• 解答职业发展问题

有什么我可以帮助您的吗？`;
    }

    return {
      reply,
      tokensUsed: 0,
      apiTier: API_TIERS.TIER_1,
      recommendations: null
    };
  }

  async handleJobInquiry(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('💼 处理职位咨询');

    try {
      // 提取职位相关信息
      const extractedInfo = await informationExtractor.extractJobInquiryInfo(message);
      
      // 如果提到具体公司或职位，进行详细分析
      if (extractedInfo.companyName || extractedInfo.jobTitle) {
        const analysisPrompt = `用户询问关于${extractedInfo.companyName || '某公司'}的${extractedInfo.jobTitle || '职位'}信息。
用户消息：${message}
候选人背景：${JSON.stringify(candidateInfo)}

请提供详细的职位分析和建议，包括：
1. 职位要求分析
2. 匹配度评估
3. 发展前景
4. 注意事项`;

        const response = await callDeepSeek(analysisPrompt, {
          temperature: 0.3,
          maxTokens: 500
        });

        return {
          reply: response.content,
          tokensUsed: response.tokensUsed,
          apiTier: API_TIERS.TIER_3,
          recommendations: null
        };
      }

      // 通用职位咨询回复
      const reply = `我来为您分析职位信息！

基于您的背景，我可以帮您：
• 分析具体职位的要求和匹配度
• 提供该职位的市场薪资范围
• 评估职位的发展前景
• 给出面试准备建议

请告诉我您感兴趣的具体职位或公司，我会为您做详细分析。`;

      return {
        reply,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };

    } catch (error) {
      console.error('职位咨询处理失败:', error);
      return {
        reply: '抱歉，处理您的职位咨询时遇到了问题。请稍后再试或换个方式描述您的需求。',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleResumeUpload(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('📄 处理简历上传');

    try {
      // 检查消息中是否包含简历内容
      const resumeContent = await informationExtractor.extractResumeContent(message);
      
      if (resumeContent && resumeContent.length > 100) {
        // 分析简历内容
        const analysisPrompt = `请分析以下简历内容，提取关键信息：

简历内容：
${resumeContent}

请提取并结构化以下信息：
1. 技术方向和技能
2. 工作经验年限
3. 教育背景
4. 项目经验
5. 期望薪资（如有）
6. 期望地点（如有）

并给出简历优化建议。`;

        const response = await callDeepSeek(analysisPrompt, {
          temperature: 0.2,
          maxTokens: 600
        });

        // 提取结构化信息更新候选人档案
        const extractedProfile = await informationExtractor.extractCandidateProfile(response.content);
        
        return {
          reply: `简历已收到并分析完成！

${response.content}

基于您的简历，我已经更新了您的候选人档案。现在我可以为您推荐更精准的职位机会。

需要我立即为您推荐合适的职位吗？`,
          tokensUsed: response.tokensUsed,
          apiTier: API_TIERS.TIER_3,
          recommendations: null,
          candidateInfo: extractedProfile
        };
      }

      // 如果没有检测到简历内容
      const reply = `我来帮您处理简历！

您可以：
• 直接粘贴简历文本内容
• 描述您的工作经历和技能
• 告诉我您的教育背景和项目经验

我会分析您的背景，提取关键信息，并为您推荐最合适的职位机会。

请分享您的简历信息吧！`;

      return {
        reply,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };

    } catch (error) {
      console.error('简历上传处理失败:', error);
      return {
        reply: '抱歉，处理您的简历时遇到了问题。请稍后再试或直接告诉我您的工作背景。',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleTechDiscussion(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('💻 处理技术讨论');

    try {
      const techPrompt = `用户想讨论技术相关话题：${message}

候选人技术背景：${candidateInfo.tech_direction || '未知'}
工作经验：${candidateInfo.experience_years || '未知'}年

请作为专业的技术招聘顾问，从以下角度回应：
1. 技术发展趋势分析
2. 职业发展建议
3. 技能提升方向
4. 相关职位机会

保持专业、实用的建议。`;

      const response = await callDeepSeek(techPrompt, {
        temperature: 0.4,
        maxTokens: 500
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('技术讨论处理失败:', error);
      return {
        reply: '让我们聊聊技术发展！基于您的背景，我建议关注当前热门的技术趋势，这些都有很好的职业发展前景。有什么具体的技术方向您想了解的吗？',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleJobRecommendation(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('🎯 处理职位推荐');

    try {
      // 检查缓存
      const cacheKey = `${userId}_recommendations`;
      const cached = this.recommendationCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiryTime) {
        console.log('使用缓存的推荐结果');
        return {
          reply: `基于您的背景，我为您推荐以下职位：\n\n${cached.recommendations.map(job => 
            `• ${job.title} - ${job.company_name}\n  ${job.salary_range} | ${job.location}`
          ).join('\n\n')}`,
          tokensUsed: 0,
          apiTier: API_TIERS.TIER_1,
          recommendations: cached.recommendations
        };
      }

      // 生成新推荐
      const recommendations = await recommendationEngine.generateRecommendations(candidateInfo, {
        limit: 4,
        includeAnalysis: true
      });

      if (!recommendations || recommendations.length === 0) {
        return {
          reply: '抱歉，暂时没有找到完全匹配的职位。请告诉我更多关于您的技能和期望，我会为您寻找更合适的机会。',
          tokensUsed: 0,
          apiTier: API_TIERS.TIER_1,
          recommendations: null
        };
      }

      // 缓存推荐结果
      this.recommendationCache.set(cacheKey, {
        recommendations,
        timestamp: Date.now(),
        candidateProfile: candidateInfo
      });

      const reply = `基于您的背景，我为您精选了以下职位机会：

${recommendations.map((job, index) => 
        `${index + 1}. **${job.title}** - ${job.company_name}
   💰 ${job.salary_range}
   📍 ${job.location}
   🎯 匹配度：${job.match_score}%
   
   ${job.match_reasons?.slice(0, 2).join('；') || '符合您的技术背景'}`
      ).join('\n\n')}

这些职位都很适合您的背景。您对哪个职位感兴趣？我可以为您提供更详细的信息和面试建议。`;

      return {
        reply,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_2,
        recommendations
      };

    } catch (error) {
      console.error('职位推荐处理失败:', error);
      return {
        reply: '我正在为您寻找最合适的职位机会。请稍等片刻，或者告诉我您的具体期望，这样我能为您提供更精准的推荐。',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleCompanyInquiry(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('🏢 处理公司咨询');

    try {
      // 提取公司名称
      const companyInfo = await informationExtractor.extractCompanyInfo(message);
      
      if (companyInfo.companyName) {
        // 查询公司信息
        const companyDetails = await jobAnalyzer.getCompanyDetails(companyInfo.companyName);
        
        if (companyDetails) {
          const reply = `关于 **${companyDetails.name}** 的信息：

🏢 **公司概况**
• 规模：${companyDetails.size || '未知'}
• 行业：${companyDetails.industry || '未知'}
• 地点：${companyDetails.locations?.join('、') || '未知'}

💼 **在招职位**
${companyDetails.openPositions?.slice(0, 3).map(job => 
  `• ${job.title} - ${job.salary_range}`
).join('\n') || '暂无在招职位'}

📈 **发展前景**
${companyDetails.prospects || '该公司在行业内有良好的发展前景'}

您对这家公司的哪个方面特别感兴趣？我可以为您提供更详细的信息。`;

          return {
            reply,
            tokensUsed: 0,
            apiTier: API_TIERS.TIER_2,
            recommendations: companyDetails.openPositions?.slice(0, 3) || null
          };
        }
      }

      // 通用公司咨询回复
      const reply = `我来为您查询公司信息！

请告诉我您想了解的公司名称，我可以为您提供：
• 公司基本信息和规模
• 在招职位和薪资范围
• 公司文化和发展前景
• 面试流程和注意事项

您想了解哪家公司呢？`;

      return {
        reply,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };

    } catch (error) {
      console.error('公司咨询处理失败:', error);
      return {
        reply: '我来帮您了解公司信息。请告诉我您想了解的公司名称，我会为您提供详细的分析。',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleSalaryDiscussion(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('💰 处理薪资讨论');

    try {
      const salaryPrompt = `用户询问薪资相关问题：${message}

候选人背景：
- 技术方向：${candidateInfo.tech_direction || '未知'}
- 工作经验：${candidateInfo.experience_years || '未知'}年
- 期望薪资：${candidateInfo.expected_salary || '未提及'}
- 工作地点：${candidateInfo.preferred_location || '未知'}

请作为专业招聘顾问，提供：
1. 该技术方向的市场薪资范围
2. 不同经验级别的薪资对比
3. 薪资谈判建议
4. 影响薪资的关键因素

保持客观、专业的分析。`;

      const response = await callDeepSeek(salaryPrompt, {
        temperature: 0.3,
        maxTokens: 500
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('薪资讨论处理失败:', error);
      return {
        reply: `关于薪资，我可以为您提供市场参考：

基于您的${candidateInfo.tech_direction || '技术'}背景和${candidateInfo.experience_years || '若干'}年经验，市场薪资范围通常在合理区间内。

具体薪资会受到以下因素影响：
• 技术能力和项目经验
• 公司规模和行业
• 工作地点
• 市场供需情况

您有具体的薪资期望吗？我可以帮您评估是否合理。`,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleCareerAdvice(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('🎯 处理职业建议');

    try {
      const advicePrompt = `用户寻求职业发展建议：${message}

候选人背景：
- 技术方向：${candidateInfo.tech_direction || '未知'}
- 工作经验：${candidateInfo.experience_years || '未知'}年
- 教育背景：${candidateInfo.education || '未知'}
- 当前状态：${candidateInfo.current_status || '未知'}

请提供专业的职业发展建议，包括：
1. 职业发展路径分析
2. 技能提升建议
3. 行业趋势和机会
4. 具体行动计划

建议要实用、可操作。`;

      const response = await callDeepSeek(advicePrompt, {
        temperature: 0.4,
        maxTokens: 600
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('职业建议处理失败:', error);
      return {
        reply: `关于职业发展，我建议您：

📈 **技能提升**
• 持续学习新技术和工具
• 关注行业发展趋势
• 积累项目经验

🎯 **职业规划**
• 明确短期和长期目标
• 建立个人技术品牌
• 扩展职业网络

💼 **机会把握**
• 关注优质公司的职位机会
• 提升面试和沟通能力
• 保持开放的心态

有什么具体的职业困惑，我可以为您详细分析。`,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleInterviewPrep(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('🎤 处理面试准备');

    try {
      const interviewPrompt = `用户需要面试准备帮助：${message}

候选人背景：
- 技术方向：${candidateInfo.tech_direction || '未知'}
- 工作经验：${candidateInfo.experience_years || '未知'}年

请提供面试准备指导，包括：
1. 常见面试问题和回答思路
2. 技术面试重点
3. 行为面试技巧
4. 面试前的准备工作

建议要具体、实用。`;

      const response = await callDeepSeek(interviewPrompt, {
        temperature: 0.3,
        maxTokens: 600
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('面试准备处理失败:', error);
      return {
        reply: `面试准备建议：

🎯 **技术准备**
• 复习核心技术知识点
• 准备项目经验介绍
• 练习编程题和算法

💬 **沟通准备**
• 准备自我介绍
• 思考职业规划问题
• 准备提问环节的问题

📋 **材料准备**
• 更新简历
• 准备作品集
• 了解目标公司

您是准备哪家公司的面试？我可以提供更针对性的建议。`,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleSkillAssessment(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('📊 处理技能评估');

    try {
      const assessmentPrompt = `用户需要技能评估：${message}

候选人背景：
- 技术方向：${candidateInfo.tech_direction || '未知'}
- 工作经验：${candidateInfo.experience_years || '未知'}年
- 技能列表：${candidateInfo.skills?.join('、') || '未知'}

请提供技能评估分析：
1. 技能水平评估
2. 技能缺口分析
3. 学习建议
4. 市场竞争力分析

评估要客观、建设性。`;

      const response = await callDeepSeek(assessmentPrompt, {
        temperature: 0.3,
        maxTokens: 500
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('技能评估处理失败:', error);
      return {
        reply: `技能评估分析：

基于您的${candidateInfo.tech_direction || '技术'}背景和${candidateInfo.experience_years || '若干'}年经验：

✅ **优势技能**
• 核心技术能力扎实
• 项目经验丰富

📈 **提升空间**
• 关注新兴技术趋势
• 加强软技能培养

🎯 **建议方向**
• 深化专业技能
• 拓展相关技术栈
• 提升架构设计能力

您想重点评估哪个技能领域？`,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleMarketAnalysis(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('📈 处理市场分析');

    try {
      const marketPrompt = `用户询问市场分析：${message}

候选人技术方向：${candidateInfo.tech_direction || '未知'}
工作地点偏好：${candidateInfo.preferred_location || '未知'}

请提供市场分析，包括：
1. 该技术方向的市场需求
2. 薪资趋势分析
3. 热门公司和职位
4. 未来发展前景

分析要基于实际市场情况。`;

      const response = await callDeepSeek(marketPrompt, {
        temperature: 0.3,
        maxTokens: 500
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_3,
        recommendations: null
      };

    } catch (error) {
      console.error('市场分析处理失败:', error);
      return {
        reply: `市场趋势分析：

📊 **${candidateInfo.tech_direction || '技术'}方向市场概况**
• 需求量：持续增长
• 竞争程度：中等偏高
• 薪资水平：稳步上升

🔥 **热门领域**
• AI/机器学习
• 云计算和微服务
• 前端框架技术

🎯 **机会分析**
• 大厂持续招聘
• 创业公司需求旺盛
• 远程工作机会增加

您想了解哪个具体方向的市场情况？`,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleFollowup(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('📞 处理跟进服务');

    try {
      // 检查之前的推荐记录
      const previousRecommendations = await this.getPreviousRecommendations(userId);
      
      if (previousRecommendations && previousRecommendations.length > 0) {
        const followupPrompt = `用户进行跟进咨询：${message}

之前推荐的职位：
${previousRecommendations.map(job => `• ${job.title} - ${job.company_name}`).join('\n')}

候选人背景：${JSON.stringify(candidateInfo)}

请提供跟进服务，包括：
1. 推荐职位的最新状态
2. 新的职位机会
3. 申请建议
4. 后续行动计划`;

        const response = await callDeepSeek(followupPrompt, {
          temperature: 0.3,
          maxTokens: 400
        });

        return {
          reply: response.content,
          tokensUsed: response.tokensUsed,
          apiTier: API_TIERS.TIER_3,
          recommendations: null
        };
      }

      // 通用跟进回复
      const reply = `感谢您的跟进！

我来为您提供后续服务：

📋 **状态更新**
• 检查之前推荐职位的最新状态
• 关注新发布的相关职位

🎯 **持续服务**
• 定期为您推荐新机会
• 提供申请进度跟踪
• 面试结果分析和建议

💬 **随时沟通**
• 有任何问题随时咨询
• 职业发展规划讨论
• 市场动态分享

有什么具体需要跟进的事项吗？`;

      return {
        reply,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };

    } catch (error) {
      console.error('跟进服务处理失败:', error);
      return {
        reply: '我会持续为您关注合适的职位机会。有任何新的需求或问题，随时告诉我！',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  async handleGeneralChat(message, contextAnalysis, candidateInfo, userId, sessionId) {
    console.log('💬 处理通用对话');

    try {
      const chatPrompt = `用户发送了通用消息：${message}

作为专业的AI招聘顾问Katrina，请友好、专业地回应，并尝试引导到招聘相关话题。

候选人背景：${JSON.stringify(candidateInfo)}

回复要：
1. 友好自然
2. 专业可信
3. 适当引导到职业相关话题
4. 保持招聘顾问的角色`;

      const response = await callDeepSeek(chatPrompt, {
        temperature: 0.5,
        maxTokens: 300
      });

      return {
        reply: response.content,
        tokensUsed: response.tokensUsed,
        apiTier: API_TIERS.TIER_2,
        recommendations: null
      };

    } catch (error) {
      console.error('通用对话处理失败:', error);
      return {
        reply: '感谢您的消息！作为您的AI招聘顾问，我随时准备为您提供职业发展方面的帮助。有什么职位相关的问题吗？',
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1,
        recommendations: null
      };
    }
  }

  // ==================== 辅助方法 ====================

  async saveMessage(sessionId, role, content, metadata = {}) {
    try {
      const messageData = {
        session_id: sessionId,
        role,
        content,
        metadata,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('chat_messages')
        .insert([messageData])
        .select()
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('保存消息失败:', error);
      throw error;
    }
  }

  checkFixedResponses(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    for (const [pattern, response] of Object.entries(FIXED_RESPONSES)) {
      if (normalizedMessage.includes(pattern)) {
        return response;
      }
    }
    
    return null;
  }

  detectCredibilityInquiry(message) {
    const credibilityKeywords = ['靠谱', '可信', '真实', '假的', '骗人', '可靠'];
    const normalizedMessage = message.toLowerCase();
    return credibilityKeywords.some(keyword => normalizedMessage.includes(keyword));
  }

  detectResumeUpload(message) {
    const resumeKeywords = ['简历', '履历', 'cv', 'resume', '工作经历'];
    const normalizedMessage = message.toLowerCase();
    return resumeKeywords.some(keyword => normalizedMessage.includes(keyword));
  }

  detectCompanyInquiry(message) {
    const companyKeywords = ['公司', '企业', '公司怎么样', '了解', '字节', '腾讯', '阿里', '百度'];
    const normalizedMessage = message.toLowerCase();
    return companyKeywords.some(keyword => normalizedMessage.includes(keyword));
  }

  async getPreviousRecommendations(userId) {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('metadata')
        .eq('role', 'assistant')
        .contains('metadata', { hasRecommendations: true })
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      return data[0]?.metadata?.recommendations || [];
    } catch (error) {
      console.error('获取历史推荐失败:', error);
      return [];
    }
  }

  formatFinalResponse(response, sessionId, messageId) {
    return {
      response: response.reply,
      sessionId,
      messageId,
      context: {
        tokensUsed: response.tokensUsed,
        apiTier: response.apiTier,
        hasRecommendations: !!response.recommendations
      },
      recommendations: response.recommendations
    };
  }

  generateErrorResponse(error) {
    return {
      response: '抱歉，处理您的请求时遇到了问题，请稍后再试。',
      sessionId: null,
      messageId: null,
      context: {
        error: error.message,
        tokensUsed: 0,
        apiTier: API_TIERS.TIER_1
      },
      recommendations: null
    };
  }
}

// 导出单例实例
export const messageProcessor = new MessageProcessor();

// 导出类供测试使用
export { MessageProcessor };
