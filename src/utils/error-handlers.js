/**
 * Katrina AI 错误处理和日志模块
 * 提供统一的错误处理、日志记录、异常监控
 */

// ==================== 错误类型定义 ====================

export class KatrinaError extends Error {
  constructor(message, code, details = {}) {
    super(message);
    this.name = 'KatrinaError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

export class ValidationError extends KatrinaError {
  constructor(message, field, value) {
    super(message, 'VALIDATION_ERROR', { field, value });
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends KatrinaError {
  constructor(message, operation, table) {
    super(message, 'DATABASE_ERROR', { operation, table });
    this.name = 'DatabaseError';
  }
}

export class AIServiceError extends KatrinaError {
  constructor(message, service, statusCode) {
    super(message, 'AI_SERVICE_ERROR', { service, statusCode });
    this.name = 'AIServiceError';
  }
}

// ==================== 错误处理器 ====================

export class ErrorHandler {
  constructor() {
    this.errorCounts = new Map();
    this.lastErrors = [];
    this.maxLastErrors = 100;
  }

  /**
   * 处理错误的主入口
   */
  handle(error, context = {}) {
    // 记录错误
    this.logError(error, context);
    
    // 更新错误统计
    this.updateErrorStats(error);
    
    // 生成用户友好的响应
    return this.generateUserResponse(error);
  }

  /**
   * 记录错误日志
   */
  logError(error, context) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        code: error.code,
        stack: error.stack
      },
      context,
      traceId: context.traceId || this.generateTraceId()
    };

    // 添加到最近错误列表
    this.lastErrors.unshift(logEntry);
    if (this.lastErrors.length > this.maxLastErrors) {
      this.lastErrors.pop();
    }

    // 控制台输出
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 Katrina Error:', logEntry);
    }
  }

  /**
   * 更新错误统计
   */
  updateErrorStats(error) {
    const errorType = error.name || 'UnknownError';
    const count = this.errorCounts.get(errorType) || 0;
    this.errorCounts.set(errorType, count + 1);
  }

  /**
   * 生成用户友好的错误响应
   */
  generateUserResponse(error) {
    const baseResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      traceId: this.generateTraceId()
    };

    switch (error.name) {
      case 'ValidationError':
        return {
          ...baseResponse,
          error: {
            type: 'validation',
            message: '输入数据格式不正确',
            details: error.message
          }
        };

      case 'DatabaseError':
        return {
          ...baseResponse,
          error: {
            type: 'database',
            message: '数据处理时遇到问题，请稍后再试'
          }
        };

      case 'AIServiceError':
        return {
          ...baseResponse,
          error: {
            type: 'ai_service',
            message: 'AI服务暂时不可用，请稍后再试'
          }
        };

      default:
        return {
          ...baseResponse,
          error: {
            type: 'unknown',
            message: '系统遇到未知问题，请联系技术支持'
          }
        };
    }
  }

  /**
   * 生成追踪ID
   */
  generateTraceId() {
    return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      totalErrors: Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0),
      errorsByType: Object.fromEntries(this.errorCounts),
      recentErrors: this.lastErrors.slice(0, 10)
    };
  }
}

// ==================== 全局错误处理器实例 ====================

export const globalErrorHandler = new ErrorHandler();

// ==================== 异步错误包装器 ====================

export function asyncErrorWrapper(fn) {
  return async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      return globalErrorHandler.handle(error, {
        function: fn.name,
        args: args.length
      });
    }
  };
}

// ==================== Express错误中间件 ====================

export function expressErrorMiddleware(err, req, res, next) {
  const context = {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip
  };

  const response = globalErrorHandler.handle(err, context);
  
  // 根据错误类型设置HTTP状态码
  let statusCode = 500;
  if (err.name === 'ValidationError') statusCode = 400;
  if (err.name === 'DatabaseError') statusCode = 503;
  if (err.name === 'AIServiceError') statusCode = 502;

  res.status(statusCode).json(response);
}

// ==================== 导出所有错误处理工具 ====================

export default {
  KatrinaError,
  ValidationError,
  DatabaseError,
  AIServiceError,
  ErrorHandler,
  globalErrorHandler,
  asyncErrorWrapper,
  expressErrorMiddleware
};