/**
 * Katrina AI 系统配置中心
 * 包含所有配置参数、常量、映射表
 */

// ==================== 应用配置 ====================

export const APP_CONFIG = {
  name: 'Katrina AI',
  version: '2.0.0',
  environment: process.env.NODE_ENV || 'development',
  debug: process.env.NODE_ENV === 'development'
};

// ==================== 数据库配置 ====================

export const DATABASE_CONFIG = {
  supabase: {
    url: process.env.SUPABASE_URL,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  },
  tables: {
    chatSessions: 'chat_sessions',
    chatMessages: 'chat_messages',
    candidates: 'candidates',
    companies: 'companies',
    jobListings: 'job_listings',
    techTree: 'tech_tree',
    businessScenarios: 'business_scenarios',
    resumeAnalysis: 'resume_analysis'
  }
};

// ==================== AI服务配置 ====================

export const AI_CONFIG = {
  deepseek: {
    apiKey: process.env.DEEPSEEK_API_KEY,
    endpoint: process.env.LLM_API_ENDPOINT || 'https://api.deepseek.com/v1',
    model: 'deepseek-chat',
    maxTokens: 4000,
    temperature: 0.7
  }
};

// ==================== 系统常量 ====================

export const SYSTEM_CONSTANTS = {
  MAX_MESSAGE_LENGTH: 2000,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30分钟
  MAX_CACHE_SIZE: 1000,
  CACHE_TTL: 5 * 60 * 1000, // 5分钟
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000
};

// ==================== 消息类型 ====================

export const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  ERROR: 'error'
};

// ==================== 技术方向映射 ====================

export const TECH_DIRECTIONS = {
  'AI算法': {
    'LLM算法': ['大模型算法', 'GPT', 'BERT', '语言模型'],
    'NLP算法': ['自然语言处理', '文本分析', '语义理解'],
    'CV算法': ['计算机视觉', '图像识别', '目标检测'],
    '推荐算法': ['协同过滤', '内容推荐', '个性化推荐'],
    '搜索算法': ['信息检索', '搜索引擎', '排序算法']
  },
  '工程技术': {
    '后端开发': ['Java', 'Python', 'Go', 'Node.js'],
    '前端开发': ['React', 'Vue', 'Angular', 'TypeScript'],
    '移动开发': ['iOS', 'Android', 'Flutter'],
    '数据工程': ['大数据', 'Spark', 'Hadoop', 'ETL']
  }
};

// ==================== 业务场景映射 ====================

export const BUSINESS_SCENARIOS = {
  '互联网': {
    '电商零售': ['推荐系统', '搜索优化', '用户画像'],
    '内容平台': ['内容推荐', 'AIGC', '审核算法'],
    '社交网络': ['社交推荐', '内容分发', '用户关系']
  },
  '金融科技': {
    '银行业务': ['风控算法', '信贷评估', '反欺诈'],
    '投资理财': ['量化交易', '智能投顾', '风险管理']
  }
};

// ==================== 固定话术库 ====================

export const FIXED_RESPONSES = {
  greeting: [
    '您好！我是Katrina，您的AI招聘顾问。',
    '欢迎来到Katrina AI！很高兴为您服务。',
    '您好！我可以帮您找到理想的工作机会。'
  ],
  
  jobInquiry: [
    '让我为您查找合适的职位机会...',
    '基于您的背景，我来推荐一些职位...',
    '我正在分析最适合您的工作机会...'
  ],
  
  error: [
    '抱歉，处理您的请求时遇到了问题，请稍后再试。',
    '系统暂时繁忙，请您稍等片刻。',
    '遇到了一些技术问题，我们正在处理中。'
  ]
};