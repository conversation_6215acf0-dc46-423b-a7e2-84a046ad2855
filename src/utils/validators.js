/**
 * Katrina AI 数据验证和清理模块
 * 提供输入验证、数据清理、格式检查功能
 */

import { SYSTEM_CONSTANTS } from './config.js';

// ==================== 基础验证函数 ====================

export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return { valid: false, error: '邮箱不能为空' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { valid: false, error: '邮箱格式不正确' };
  }
  
  return { valid: true, value: email.toLowerCase().trim() };
}

export function validateMessage(message) {
  if (!message || typeof message !== 'string') {
    return { valid: false, error: '消息不能为空' };
  }
  
  const trimmed = message.trim();
  if (trimmed.length === 0) {
    return { valid: false, error: '消息不能为空' };
  }
  
  if (trimmed.length > SYSTEM_CONSTANTS.MAX_MESSAGE_LENGTH) {
    return { valid: false, error: `消息长度不能超过${SYSTEM_CONSTANTS.MAX_MESSAGE_LENGTH}字符` };
  }
  
  return { valid: true, value: trimmed };
}

export function validateSessionId(sessionId) {
  if (!sessionId || typeof sessionId !== 'string') {
    return { valid: false, error: '会话ID不能为空' };
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(sessionId)) {
    return { valid: false, error: '会话ID格式不正确' };
  }
  
  return { valid: true, value: sessionId };
}

// ==================== 候选人数据验证 ====================

export function validateCandidateProfile(profile) {
  const errors = [];
  const cleaned = {};
  
  // 验证姓名
  if (!profile.name || typeof profile.name !== 'string') {
    errors.push('姓名不能为空');
  } else {
    cleaned.name = profile.name.trim();
    if (cleaned.name.length < 2 || cleaned.name.length > 50) {
      errors.push('姓名长度应在2-50字符之间');
    }
  }
  
  // 验证邮箱
  const emailValidation = validateEmail(profile.email);
  if (!emailValidation.valid) {
    errors.push(emailValidation.error);
  } else {
    cleaned.email = emailValidation.value;
  }
  
  // 验证工作年限
  if (profile.experience !== undefined) {
    const exp = parseInt(profile.experience);
    if (isNaN(exp) || exp < 0 || exp > 50) {
      errors.push('工作年限应为0-50之间的数字');
    } else {
      cleaned.experience = exp;
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    data: cleaned
  };
}

// ==================== 文件上传验证 ====================

export function validateUploadedFile(file) {
  const errors = [];
  
  // 检查文件大小 (5MB限制)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('文件大小不能超过5MB');
  }
  
  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    errors.push('只支持PDF、DOC、DOCX格式的文件');
  }
  
  // 检查文件名
  if (!file.name || file.name.length > 255) {
    errors.push('文件名不能为空且不能超过255字符');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// ==================== 数据清理函数 ====================

export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // 移除HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, ''); // 移除事件处理器
}

export function cleanPhoneNumber(phone) {
  if (!phone) return '';
  return phone.replace(/[^\d+\-\s()]/g, '').trim();
}

export function normalizeCompanyName(name) {
  if (!name) return '';
  return name
    .trim()
    .replace(/\s+/g, ' ') // 合并多个空格
    .replace(/[^\w\s\u4e00-\u9fff]/g, ''); // 只保留字母、数字、空格和中文
}

// ==================== 批量验证 ====================

export function validateBatch(items, validator) {
  const results = [];
  const errors = [];
  
  for (let i = 0; i < items.length; i++) {
    const result = validator(items[i]);
    if (result.valid) {
      results.push(result.data || result.value);
    } else {
      errors.push({
        index: i,
        item: items[i],
        errors: result.errors || [result.error]
      });
    }
  }
  
  return {
    valid: errors.length === 0,
    results,
    errors
  };
}

// ==================== 导出验证器集合 ====================

export const validators = {
  email: validateEmail,
  message: validateMessage,
  sessionId: validateSessionId,
  candidateProfile: validateCandidateProfile,
  uploadedFile: validateUploadedFile
};

export const cleaners = {
  input: sanitizeInput,
  phone: cleanPhoneNumber,
  companyName: normalizeCompanyName
};