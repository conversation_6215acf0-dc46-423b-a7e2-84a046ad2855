/**
 * Katrina AI 通用工具函数库
 * 提供系统级别的基础工具和辅助函数
 */

// ==================== 字符串处理工具 ====================

export function sanitizeString(str) {
  if (!str || typeof str !== 'string') return '';
  return str.trim().replace(/[<>\"'&]/g, '');
}

export function extractEmailUsername(email) {
  if (!email || !email.includes('@')) return '';
  return email.split('@')[0].replace(/[^a-zA-Z0-9]/g, '');
}

export function generateSafeFileName(originalName, prefix = '') {
  const timestamp = Date.now();
  const ext = originalName.split('.').pop();
  const safeName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  return `${prefix}_${safeName}_${timestamp}.${ext}`;
}

// ==================== 数据验证工具 ====================

export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidSessionId(sessionId) {
  return sessionId && typeof sessionId === 'string' && sessionId.length > 0;
}

export function generateTraceId(prefix = 'trace') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `${prefix}_${timestamp}_${random}`;
}

// ==================== 数据结构工具 ====================

export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  
  const cloned = {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}

export function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON解析失败:', error.message);
    return defaultValue;
  }
}

// ==================== 缓存工具 ====================

export class SimpleCache {
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  set(key, value, ttl = 300000) { // 默认5分钟
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    const expireTime = Date.now() + ttl;
    this.cache.set(key, { value, expireTime });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expireTime) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  clear() {
    this.cache.clear();
  }
}

// ==================== 重试机制工具 ====================

export async function retryAsync(fn, maxRetries = 3) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries) {
        const delay = 1000 * Math.pow(2, i);
        await sleep(delay);
      }
    }
  }
  
  throw lastError;
}

export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ==================== 性能监控工具 ====================

export class PerformanceTimer {
  constructor(name) {
    this.name = name;
    this.startTime = Date.now();
  }

  end() {
    const duration = Date.now() - this.startTime;
    console.log(`⏱️ ${this.name}: ${duration}ms`);
    return duration;
  }
}