/**
 * API路由定义
 * 包含所有RESTful API端点的定义和处理逻辑
 */

import { messageProcessor } from '../core/message-processor.js';
import { databaseManager } from '../core/database-manager.js';
import { validateUserMessage, validateSessionUuid } from '../config/validators.js';
import { generateUUID } from '../config/utilities.js';

// ==================== 主要API端点 ====================

/**
 * 处理聊天消息
 * POST /api/chat
 * 对应业务逻辑: handleChatMessage(request)
 */
export async function handleChatMessage(request) {
  try {
    const { message, sessionUuid } = request.body;

    // 1. 验证输入
    const messageValidation = validateUserMessage(message);
    if (!messageValidation.valid) {
      return {
        success: false,
        error: messageValidation.error,
        code: messageValidation.code
      };
    }

    let validSessionUuid = sessionUuid;
    if (!validSessionUuid) {
      validSessionUuid = generateUUID();
    } else {
      const sessionValidation = validateSessionUuid(sessionUuid);
      if (!sessionValidation.valid) {
        return {
          success: false,
          error: sessionValidation.error,
          code: sessionValidation.code
        };
      }
      validSessionUuid = sessionValidation.cleaned;
    }

    // 2. 处理消息
    const result = await messageProcessor.processMessage(
      messageValidation.cleaned,
      validSessionUuid
    );

    // 3. 返回结果
    return {
      success: true,
      data: {
        reply: result.reply,
        sessionUuid: validSessionUuid,
        metadata: {
          tokensUsed: result.tokensUsed,
          apiTier: result.apiTier,
          timestamp: new Date().toISOString(),
          ...result.metadata
        }
      }
    };
  } catch (error) {
    console.error('处理聊天消息失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 获取对话历史
 * GET /api/chat/history/:sessionUuid
 * 对应业务逻辑: getChatHistory(request)
 */
export async function getChatHistory(request) {
  try {
    const { sessionUuid } = request.params;
    const { limit = 20 } = request.query;

    // 验证会话UUID
    const sessionValidation = validateSessionUuid(sessionUuid);
    if (!sessionValidation.valid) {
      return {
        success: false,
        error: sessionValidation.error,
        code: sessionValidation.code
      };
    }

    // 获取对话历史
    const history = await databaseManager.getConversationHistory(
      sessionValidation.cleaned,
      parseInt(limit)
    );

    return {
      success: true,
      data: {
        sessionUuid: sessionValidation.cleaned,
        messages: history,
        count: history.length
      }
    };
  } catch (error) {
    console.error('获取对话历史失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 获取候选人信息
 * GET /api/candidate/:userId
 * 对应业务逻辑: getCandidateProfile(request)
 */
export async function getCandidateProfile(request) {
  try {
    const { userId } = request.params;

    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        code: 'MISSING_USER_ID'
      };
    }

    // 获取候选人信息
    const { user, candidate } = await databaseManager.getCandidateInfo(userId);

    return {
      success: true,
      data: {
        user: user,
        candidate: candidate
      }
    };
  } catch (error) {
    console.error('获取候选人信息失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 更新候选人档案
 * PUT /api/candidate/:userId
 * 对应业务逻辑: updateCandidateProfile(request)
 */
export async function updateCandidateProfile(request) {
  try {
    const { userId } = request.params;
    const updateData = request.body;

    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        code: 'MISSING_USER_ID'
      };
    }

    // 更新候选人档案
    const updatedCandidate = await databaseManager.updateCandidateProfile(userId, updateData);

    return {
      success: true,
      data: {
        candidate: updatedCandidate
      }
    };
  } catch (error) {
    console.error('更新候选人档案失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 搜索职位
 * GET /api/jobs/search
 * 对应业务逻辑: searchJobs(request)
 */
export async function searchJobs(request) {
  try {
    const {
      techDirection,
      location,
      salaryMin,
      salaryMax,
      companyType,
      limit = 20,
      offset = 0
    } = request.query;

    // 构建搜索条件
    const searchCriteria = {
      primary_tech_direction_id: techDirection,
      desired_location_raw: location,
      expected_compensation_min: salaryMin,
      expected_compensation_max: salaryMax,
      company_type: companyType
    };

    // 查询职位
    const jobs = await databaseManager.queryMatchingJobs(searchCriteria, 'search');

    // 分页处理
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedJobs = jobs.slice(startIndex, endIndex);

    return {
      success: true,
      data: {
        jobs: paginatedJobs,
        total: jobs.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    };
  } catch (error) {
    console.error('搜索职位失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 获取职位详情
 * GET /api/jobs/:jobId
 * 对应业务逻辑: getJobDetails(request)
 */
export async function getJobDetails(request) {
  try {
    const { jobId } = request.params;

    if (!jobId) {
      return {
        success: false,
        error: '职位ID不能为空',
        code: 'MISSING_JOB_ID'
      };
    }

    // 获取职位详情
    const jobDetails = await messageProcessor.getDetailedJobInfo(parseInt(jobId));

    if (!jobDetails) {
      return {
        success: false,
        error: '职位不存在',
        code: 'JOB_NOT_FOUND'
      };
    }

    return {
      success: true,
      data: {
        job: jobDetails
      }
    };
  } catch (error) {
    console.error('获取职位详情失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 获取公司信息
 * GET /api/companies/search
 * 对应业务逻辑: searchCompanies(request)
 */
export async function searchCompanies(request) {
  try {
    const { name, limit = 10 } = request.query;

    if (!name) {
      return {
        success: false,
        error: '公司名称不能为空',
        code: 'MISSING_COMPANY_NAME'
      };
    }

    // 搜索公司
    const companies = await databaseManager.getCompanyInfo(name);

    return {
      success: true,
      data: {
        companies: companies.slice(0, parseInt(limit)),
        total: companies.length
      }
    };
  } catch (error) {
    console.error('搜索公司失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

/**
 * 获取技术方向列表
 * GET /api/tech-directions
 * 对应业务逻辑: getTechDirections(request)
 */
export async function getTechDirections(request) {
  try {
    const { search, limit = 50 } = request.query;

    let techDirections;
    if (search) {
      techDirections = await databaseManager.getTechDirectionInfo(search);
    } else {
      // 获取所有一级技术方向
      const { data } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('*')
        .is('parent_id', null)
        .limit(parseInt(limit));
      
      techDirections = data || [];
    }

    return {
      success: true,
      data: {
        techDirections: techDirections,
        total: techDirections.length
      }
    };
  } catch (error) {
    console.error('获取技术方向失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

// ==================== 健康检查和状态 ====================

/**
 * 健康检查
 * GET /api/health
 * 对应业务逻辑: healthCheck(request)
 */
export async function healthCheck(request) {
  try {
    // 检查数据库连接
    const dbConnected = await databaseManager.checkConnection();
    
    const status = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: dbConnected ? 'connected' : 'disconnected',
        messageProcessor: 'running',
        aiServices: 'running'
      }
    };

    return {
      success: true,
      data: status
    };
  } catch (error) {
    console.error('健康检查失败:', error);
    return {
      success: false,
      error: '服务不可用',
      code: 'SERVICE_UNAVAILABLE'
    };
  }
}

/**
 * 获取系统状态
 * GET /api/status
 * 对应业务逻辑: getSystemStatus(request)
 */
export async function getSystemStatus(request) {
  try {
    const status = {
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    return {
      success: true,
      data: status
    };
  } catch (error) {
    console.error('获取系统状态失败:', error);
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    };
  }
}

// ==================== 错误处理 ====================

/**
 * 404处理器
 * 对应业务逻辑: handle404(request)
 */
export function handle404(request) {
  return {
    success: false,
    error: '接口不存在',
    code: 'NOT_FOUND',
    path: request.path || request.url
  };
}

/**
 * 通用错误处理器
 * 对应业务逻辑: handleError(error, request)
 */
export function handleError(error, request) {
  console.error('API错误:', error);
  
  return {
    success: false,
    error: '服务器内部错误',
    code: 'INTERNAL_SERVER_ERROR',
    timestamp: new Date().toISOString()
  };
}

// ==================== 路由映射 ====================

/**
 * API路由映射表
 * 对应业务逻辑: API_ROUTES
 */
export const API_ROUTES = {
  // 聊天相关
  'POST /api/chat': handleChatMessage,
  'GET /api/chat/history/:sessionUuid': getChatHistory,
  
  // 候选人相关
  'GET /api/candidate/:userId': getCandidateProfile,
  'PUT /api/candidate/:userId': updateCandidateProfile,
  
  // 职位相关
  'GET /api/jobs/search': searchJobs,
  'GET /api/jobs/:jobId': getJobDetails,
  
  // 公司相关
  'GET /api/companies/search': searchCompanies,
  
  // 技术方向相关
  'GET /api/tech-directions': getTechDirections,
  
  // 系统相关
  'GET /api/health': healthCheck,
  'GET /api/status': getSystemStatus
};

// ==================== 中间件 ====================

/**
 * CORS中间件
 * 对应业务逻辑: corsMiddleware(request, response)
 */
export function corsMiddleware(request, response) {
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (request.method === 'OPTIONS') {
    response.statusCode = 200;
    response.end();
    return true; // 表示已处理
  }
  
  return false; // 继续处理
}

/**
 * 请求日志中间件
 * 对应业务逻辑: requestLogMiddleware(request, response)
 */
export function requestLogMiddleware(request, response) {
  const startTime = Date.now();
  const { method, url } = request;
  
  console.log(`[${new Date().toISOString()}] ${method} ${url} - 开始处理`);
  
  // 记录响应时间
  const originalEnd = response.end;
  response.end = function(...args) {
    const duration = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] ${method} ${url} - 完成 (${duration}ms)`);
    originalEnd.apply(this, args);
  };
}

/**
 * 错误处理中间件
 * 对应业务逻辑: errorHandlingMiddleware(error, request, response)
 */
export function errorHandlingMiddleware(error, request, response) {
  const errorResponse = handleError(error, request);
  
  response.statusCode = 500;
  response.setHeader('Content-Type', 'application/json');
  response.end(JSON.stringify(errorResponse));
}
