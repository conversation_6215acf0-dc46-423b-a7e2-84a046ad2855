/**
 * Katrina AI RESTful API接口层
 * 统一API接口、请求处理、响应格式化
 */

import express from "express";
import cors from "cors";
import {
  validateRequest,
  validateChatMessage,
  validateCandidateProfile,
} from "../utils/validators.js";
import { APIError, handleAPIError } from "../utils/error-handlers.js";
import { generateId, formatResponse, logRequest } from "../utils/utilities.js";
import { API_CONFIG, RATE_LIMITS, CORS_CONFIG } from "../utils/config.js";

// 导入业务模块
import { messageProcessor } from "../core/backend-core.js";
import { userManager, sessionManager } from "../services/user-management.js";
import {
  jobMatchingEngine,
  recommendationEngine,
  jobAnalyzer,
} from "../core/job-engine.js";
import { contextAnalyzer, informationExtractor } from "../core/ai-services.js";

// ==================== Express应用初始化 ====================

const app = express();

// 中间件配置
app.use(cors(CORS_CONFIG));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  req.requestId = generateId();
  logRequest(req);
  next();
});

// 速率限制中间件
app.use("/api/", createRateLimiter());

// ==================== 核心聊天API ====================

/**
 * 主聊天接口
 */
app.post("/api/chat", async (req, res) => {
  try {
    const { message, userEmail, sessionId } = req.body;

    // 请求验证
    const validation = validateChatMessage({ message, userEmail, sessionId });
    if (!validation.valid) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: validation.error,
          code: "VALIDATION_ERROR",
        })
      );
    }

    // 处理消息
    const result = await messageProcessor.processMessage(
      message,
      userEmail,
      sessionId
    );

    // 返回响应
    res.json(
      formatResponse({
        success: true,
        data: {
          response: result.response,
          sessionId: result.sessionId,
          messageId: result.messageId,
          context: result.context,
          recommendations: result.recommendations || null,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 获取聊天历史
 */
app.get("/api/chat/history/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { limit = 20, offset = 0 } = req.query;

    const history = await sessionManager.getChatHistory(sessionId, {
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    res.json(
      formatResponse({
        success: true,
        data: {
          messages: history.messages,
          total: history.total,
          hasMore: history.hasMore,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 创建新会话
 */
app.post("/api/chat/session", async (req, res) => {
  try {
    const { userEmail } = req.body;

    if (!userEmail) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "用户邮箱不能为空",
          code: "MISSING_EMAIL",
        })
      );
    }

    const session = await sessionManager.createSession(userEmail);

    res.json(
      formatResponse({
        success: true,
        data: {
          sessionId: session.id,
          createdAt: session.created_at,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 用户管理API ====================

/**
 * 获取用户信息
 */
app.get("/api/user/:email", async (req, res) => {
  try {
    const { email } = req.params;

    const user = await userManager.getUserByEmail(email);
    if (!user) {
      return res.status(404).json(
        formatResponse({
          success: false,
          error: "用户不存在",
          code: "USER_NOT_FOUND",
        })
      );
    }

    res.json(
      formatResponse({
        success: true,
        data: {
          user: {
            email: user.email,
            name: user.name,
            createdAt: user.created_at,
            lastActiveAt: user.last_active_at,
          },
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 更新用户信息
 */
app.put("/api/user/:email", async (req, res) => {
  try {
    const { email } = req.params;
    const updateData = req.body;

    // 验证更新数据
    const validation = validateCandidateProfile(updateData);
    if (!validation.valid) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: validation.error,
          code: "VALIDATION_ERROR",
        })
      );
    }

    const updatedUser = await userManager.updateUser(email, updateData);

    res.json(
      formatResponse({
        success: true,
        data: {
          user: updatedUser,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 获取候选人档案
 */
app.get("/api/candidate/:email", async (req, res) => {
  try {
    const { email } = req.params;

    const candidate = await userManager.getCandidateProfile(email);
    if (!candidate) {
      return res.status(404).json(
        formatResponse({
          success: false,
          error: "候选人档案不存在",
          code: "CANDIDATE_NOT_FOUND",
        })
      );
    }

    res.json(
      formatResponse({
        success: true,
        data: {
          candidate,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 更新候选人档案
 */
app.put("/api/candidate/:email", async (req, res) => {
  try {
    const { email } = req.params;
    const profileData = req.body;

    const updatedProfile = await userManager.updateCandidateProfile(
      email,
      profileData
    );

    res.json(
      formatResponse({
        success: true,
        data: {
          candidate: updatedProfile,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 职位推荐API ====================

/**
 * 获取职位推荐
 */
app.post("/api/recommendations", async (req, res) => {
  try {
    const { candidateEmail, options = {} } = req.body;

    if (!candidateEmail) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "候选人邮箱不能为空",
          code: "MISSING_EMAIL",
        })
      );
    }

    // 获取候选人档案
    const candidateProfile = await userManager.getCandidateProfile(
      candidateEmail
    );
    if (!candidateProfile) {
      return res.status(404).json(
        formatResponse({
          success: false,
          error: "候选人档案不存在",
          code: "CANDIDATE_NOT_FOUND",
        })
      );
    }

    // 生成推荐
    const recommendations = await recommendationEngine.generateRecommendations(
      candidateProfile,
      options
    );

    res.json(
      formatResponse({
        success: true,
        data: recommendations,
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 计算职位匹配度
 */
app.post("/api/job/match", async (req, res) => {
  try {
    const { candidateEmail, jobId } = req.body;

    if (!candidateEmail || !jobId) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "候选人邮箱和职位ID不能为空",
          code: "MISSING_PARAMETERS",
        })
      );
    }

    // 获取候选人档案
    const candidateProfile = await userManager.getCandidateProfile(
      candidateEmail
    );
    if (!candidateProfile) {
      return res.status(404).json(
        formatResponse({
          success: false,
          error: "候选人档案不存在",
          code: "CANDIDATE_NOT_FOUND",
        })
      );
    }

    // 获取职位信息
    const job = await jobRepo.getJobById(jobId);
    if (!job) {
      return res.status(404).json(
        formatResponse({
          success: false,
          error: "职位不存在",
          code: "JOB_NOT_FOUND",
        })
      );
    }

    // 计算匹配度
    const matchResult = jobMatchingEngine.calculateJobMatch(
      candidateProfile,
      job
    );

    res.json(
      formatResponse({
        success: true,
        data: {
          match: matchResult,
          job: {
            id: job.id,
            title: job.title,
            company: job.company_name,
          },
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 分析职位详情
 */
app.get("/api/job/:jobId/analysis", async (req, res) => {
  try {
    const { jobId } = req.params;

    const analysis = await jobAnalyzer.analyzeJobDetails(jobId);

    res.json(
      formatResponse({
        success: true,
        data: {
          analysis,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 信息提取API ====================

/**
 * 提取用户消息中的信息
 */
app.post("/api/extract", async (req, res) => {
  try {
    const { message, existingInfo = {} } = req.body;

    if (!message) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "消息内容不能为空",
          code: "MISSING_MESSAGE",
        })
      );
    }

    const extractedInfo = await informationExtractor.extractInformation(
      message,
      existingInfo
    );

    res.json(
      formatResponse({
        success: true,
        data: {
          extracted: extractedInfo,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

/**
 * 分析消息上下文
 */
app.post("/api/analyze", async (req, res) => {
  try {
    const { message, conversationHistory = [], candidateInfo = {} } = req.body;

    if (!message) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "消息内容不能为空",
          code: "MISSING_MESSAGE",
        })
      );
    }

    const analysis = await contextAnalyzer.analyzeContext(
      message,
      conversationHistory,
      candidateInfo
    );

    res.json(
      formatResponse({
        success: true,
        data: {
          analysis,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 文件上传API ====================

/**
 * 简历上传
 */
app.post("/api/upload/resume", async (req, res) => {
  try {
    const { userEmail } = req.body;
    const file = req.file;

    if (!userEmail || !file) {
      return res.status(400).json(
        formatResponse({
          success: false,
          error: "用户邮箱和文件不能为空",
          code: "MISSING_PARAMETERS",
        })
      );
    }

    // 处理简历上传（这里需要实现文件处理逻辑）
    const result = await processResumeUpload(file, userEmail);

    res.json(
      formatResponse({
        success: true,
        data: {
          fileId: result.fileId,
          extractedInfo: result.extractedInfo,
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 系统状态API ====================

/**
 * 健康检查
 */
app.get("/api/health", (req, res) => {
  res.json(
    formatResponse({
      success: true,
      data: {
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: API_CONFIG.version,
      },
    })
  );
});

/**
 * 系统状态
 */
app.get("/api/status", async (req, res) => {
  try {
    // 检查各个服务状态
    const status = {
      database: await checkDatabaseStatus(),
      ai_service: await checkAIServiceStatus(),
      cache: await checkCacheStatus(),
    };

    const allHealthy = Object.values(status).every(
      (s) => s.status === "healthy"
    );

    res.json(
      formatResponse({
        success: true,
        data: {
          overall: allHealthy ? "healthy" : "degraded",
          services: status,
          timestamp: new Date().toISOString(),
        },
      })
    );
  } catch (error) {
    handleAPIError(error, res, req.requestId);
  }
});

// ==================== 错误处理中间件 ====================

app.use((err, req, res, next) => {
  console.error("未处理的错误:", err);

  res.status(500).json(
    formatResponse({
      success: false,
      error: "服务器内部错误",
      code: "INTERNAL_ERROR",
      requestId: req.requestId,
    })
  );
});

// 404处理
app.use((req, res) => {
  res.status(404).json(
    formatResponse({
      success: false,
      error: "接口不存在",
      code: "NOT_FOUND",
      path: req.path,
    })
  );
});

// ==================== 工具函数 ====================

/**
 * 创建速率限制器
 */
function createRateLimiter() {
  const requests = new Map();

  return (req, res, next) => {
    const clientId = req.ip || "unknown";
    const now = Date.now();
    const windowStart = now - RATE_LIMITS.windowMs;

    // 清理过期记录
    if (!requests.has(clientId)) {
      requests.set(clientId, []);
    }

    const clientRequests = requests.get(clientId);
    const validRequests = clientRequests.filter((time) => time > windowStart);

    if (validRequests.length >= RATE_LIMITS.maxRequests) {
      return res.status(429).json(
        formatResponse({
          success: false,
          error: "请求过于频繁，请稍后再试",
          code: "RATE_LIMIT_EXCEEDED",
        })
      );
    }

    validRequests.push(now);
    requests.set(clientId, validRequests);
    next();
  };
}

/**
 * 检查数据库状态
 */
async function checkDatabaseStatus() {
  try {
    // 这里应该实现实际的数据库连接检查
    return { status: "healthy", responseTime: 50 };
  } catch (error) {
    return { status: "unhealthy", error: error.message };
  }
}

/**
 * 检查AI服务状态
 */
async function checkAIServiceStatus() {
  try {
    // 这里应该实现实际的AI服务检查
    return { status: "healthy", responseTime: 200 };
  } catch (error) {
    return { status: "unhealthy", error: error.message };
  }
}

/**
 * 检查缓存状态
 */
async function checkCacheStatus() {
  try {
    // 这里应该实现实际的缓存检查
    return { status: "healthy", size: 1024 };
  } catch (error) {
    return { status: "unhealthy", error: error.message };
  }
}

/**
 * 处理简历上传
 */
async function processResumeUpload(file, userEmail) {
  // 这里应该实现实际的简历处理逻辑
  return {
    fileId: generateId(),
    extractedInfo: {},
  };
}

// ==================== 导出 ====================

export default app;

export { app as apiServer };
