/**
 * 用户管理器
 * 负责用户认证、会话管理、权限控制等功能
 */

import { databaseManager } from '../core/database-manager.js';
import { validateSessionUuid, validateUserName, validateEmail } from '../config/validators.js';
import { generateUUID, generateRandomId } from '../config/utilities.js';

// ==================== 用户管理器 ====================

export class UserManager {
  constructor() {
    this.databaseManager = databaseManager;
    this.activeSessions = new Map(); // 活跃会话缓存
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24小时会话超时
  }

  // ==================== 用户认证 ====================

  /**
   * 创建匿名用户
   * 对应业务逻辑: createAnonymousUser(sessionUuid)
   */
  async createAnonymousUser(sessionUuid) {
    try {
      const userId = generateUUID();
      
      // 创建用户记录
      const { data: user, error: userError } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .insert({
          id: userId,
          user_type: 'anonymous',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (userError) throw userError;

      // 创建候选人档案
      const { data: candidate, error: candidateError } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.candidates)
        .insert({
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (candidateError) throw candidateError;

      // 更新会话关联
      await this.updateSessionUser(sessionUuid, userId);

      return {
        success: true,
        user: user,
        candidate: candidate
      };
    } catch (error) {
      console.error('创建匿名用户失败:', error);
      return {
        success: false,
        error: '创建用户失败'
      };
    }
  }

  /**
   * 用户注册
   * 对应业务逻辑: registerUser(userInfo, sessionUuid)
   */
  async registerUser(userInfo, sessionUuid) {
    try {
      // 验证用户信息
      const validation = this.validateUserInfo(userInfo);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // 检查邮箱是否已存在
      if (userInfo.email) {
        const existingUser = await this.findUserByEmail(userInfo.email);
        if (existingUser) {
          return {
            success: false,
            error: '邮箱已被注册'
          };
        }
      }

      const userId = generateUUID();
      
      // 创建用户记录
      const { data: user, error: userError } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .insert({
          id: userId,
          email: userInfo.email,
          name: userInfo.name,
          phone: userInfo.phone,
          user_type: 'registered',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (userError) throw userError;

      // 创建或更新候选人档案
      const candidateData = {
        user_id: userId,
        candidate_tech_direction_raw: userInfo.techDirection,
        expected_compensation_raw: userInfo.expectedSalary,
        desired_location_raw: userInfo.location,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: candidate, error: candidateError } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.candidates)
        .insert(candidateData)
        .select()
        .single();

      if (candidateError) throw candidateError;

      // 更新会话关联
      await this.updateSessionUser(sessionUuid, userId);

      return {
        success: true,
        user: user,
        candidate: candidate
      };
    } catch (error) {
      console.error('用户注册失败:', error);
      return {
        success: false,
        error: '注册失败'
      };
    }
  }

  /**
   * 用户登录
   * 对应业务逻辑: loginUser(credentials, sessionUuid)
   */
  async loginUser(credentials, sessionUuid) {
    try {
      const { email, phone } = credentials;
      
      let user = null;
      if (email) {
        user = await this.findUserByEmail(email);
      } else if (phone) {
        user = await this.findUserByPhone(phone);
      }

      if (!user) {
        return {
          success: false,
          error: '用户不存在'
        };
      }

      // 获取候选人档案
      const { candidate } = await this.databaseManager.getCandidateInfo(user.id);

      // 更新会话关联
      await this.updateSessionUser(sessionUuid, user.id);

      // 创建会话token
      const sessionToken = this.createSessionToken(user.id, sessionUuid);

      return {
        success: true,
        user: user,
        candidate: candidate,
        sessionToken: sessionToken
      };
    } catch (error) {
      console.error('用户登录失败:', error);
      return {
        success: false,
        error: '登录失败'
      };
    }
  }

  // ==================== 会话管理 ====================

  /**
   * 创建会话token
   * 对应业务逻辑: createSessionToken(userId, sessionUuid)
   */
  createSessionToken(userId, sessionUuid) {
    const token = generateRandomId(32);
    const expiresAt = Date.now() + this.sessionTimeout;
    
    // 缓存会话信息
    this.activeSessions.set(token, {
      userId: userId,
      sessionUuid: sessionUuid,
      createdAt: Date.now(),
      expiresAt: expiresAt
    });

    return token;
  }

  /**
   * 验证会话token
   * 对应业务逻辑: validateSessionToken(token)
   */
  validateSessionToken(token) {
    if (!token) {
      return { valid: false, error: '缺少会话token' };
    }

    const session = this.activeSessions.get(token);
    if (!session) {
      return { valid: false, error: '会话不存在' };
    }

    if (Date.now() > session.expiresAt) {
      this.activeSessions.delete(token);
      return { valid: false, error: '会话已过期' };
    }

    return {
      valid: true,
      session: session
    };
  }

  /**
   * 更新会话用户关联
   * 对应业务逻辑: updateSessionUser(sessionUuid, userId)
   */
  async updateSessionUser(sessionUuid, userId) {
    try {
      const { error } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.chatSessions)
        .update({
          user_id: userId,
          updated_at: new Date().toISOString()
        })
        .eq('session_uuid', sessionUuid);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('更新会话用户关联失败:', error);
      return false;
    }
  }

  /**
   * 清理过期会话
   * 对应业务逻辑: cleanupExpiredSessions()
   */
  cleanupExpiredSessions() {
    const now = Date.now();
    for (const [token, session] of this.activeSessions.entries()) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(token);
      }
    }
  }

  // ==================== 用户查询 ====================

  /**
   * 根据邮箱查找用户
   * 对应业务逻辑: findUserByEmail(email)
   */
  async findUserByEmail(email) {
    try {
      const { data, error } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .select('*')
        .eq('email', email)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data;
    } catch (error) {
      console.error('根据邮箱查找用户失败:', error);
      return null;
    }
  }

  /**
   * 根据手机号查找用户
   * 对应业务逻辑: findUserByPhone(phone)
   */
  async findUserByPhone(phone) {
    try {
      const { data, error } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .select('*')
        .eq('phone', phone)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('根据手机号查找用户失败:', error);
      return null;
    }
  }

  /**
   * 根据用户ID获取完整信息
   * 对应业务逻辑: getUserById(userId)
   */
  async getUserById(userId) {
    try {
      return await this.databaseManager.getCandidateInfo(userId);
    } catch (error) {
      console.error('根据ID获取用户失败:', error);
      return null;
    }
  }

  // ==================== 用户档案管理 ====================

  /**
   * 更新用户基本信息
   * 对应业务逻辑: updateUserInfo(userId, userInfo)
   */
  async updateUserInfo(userId, userInfo) {
    try {
      // 验证用户信息
      const validation = this.validateUserInfo(userInfo);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // 更新用户表
      const { data: user, error: userError } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .update({
          name: userInfo.name,
          email: userInfo.email,
          phone: userInfo.phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (userError) throw userError;

      return {
        success: true,
        user: user
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return {
        success: false,
        error: '更新失败'
      };
    }
  }

  /**
   * 删除用户账户
   * 对应业务逻辑: deleteUser(userId)
   */
  async deleteUser(userId) {
    try {
      // 删除候选人档案
      await this.databaseManager.supabase
        .from(this.databaseManager.tables.candidates)
        .delete()
        .eq('user_id', userId);

      // 删除聊天记录
      const { data: sessions } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.chatSessions)
        .select('id')
        .eq('user_id', userId);

      if (sessions && sessions.length > 0) {
        const sessionIds = sessions.map(s => s.id);
        
        await this.databaseManager.supabase
          .from(this.databaseManager.tables.chatMessages)
          .delete()
          .in('session_id', sessionIds);

        await this.databaseManager.supabase
          .from(this.databaseManager.tables.chatSessions)
          .delete()
          .eq('user_id', userId);
      }

      // 删除用户记录
      const { error } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.users)
        .delete()
        .eq('id', userId);

      if (error) throw error;

      return {
        success: true,
        message: '用户账户已删除'
      };
    } catch (error) {
      console.error('删除用户失败:', error);
      return {
        success: false,
        error: '删除失败'
      };
    }
  }

  // ==================== 验证工具 ====================

  /**
   * 验证用户信息
   * 对应业务逻辑: validateUserInfo(userInfo)
   */
  validateUserInfo(userInfo) {
    const errors = [];

    if (userInfo.name) {
      const nameValidation = validateUserName(userInfo.name);
      if (!nameValidation.valid) {
        errors.push(nameValidation.error);
      }
    }

    if (userInfo.email) {
      const emailValidation = validateEmail(userInfo.email);
      if (!emailValidation.valid) {
        errors.push(emailValidation.error);
      }
    }

    return {
      valid: errors.length === 0,
      error: errors.join('; ')
    };
  }

  // ==================== 统计信息 ====================

  /**
   * 获取用户统计信息
   * 对应业务逻辑: getUserStats(userId)
   */
  async getUserStats(userId) {
    try {
      // 获取会话数量
      const { count: sessionCount } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.chatSessions)
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // 获取消息数量
      const { data: sessions } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.chatSessions)
        .select('id')
        .eq('user_id', userId);

      let messageCount = 0;
      if (sessions && sessions.length > 0) {
        const sessionIds = sessions.map(s => s.id);
        const { count } = await this.databaseManager.supabase
          .from(this.databaseManager.tables.chatMessages)
          .select('*', { count: 'exact', head: true })
          .in('session_id', sessionIds);
        messageCount = count || 0;
      }

      return {
        success: true,
        stats: {
          sessionCount: sessionCount || 0,
          messageCount: messageCount,
          registrationDate: null // 需要从用户记录获取
        }
      };
    } catch (error) {
      console.error('获取用户统计失败:', error);
      return {
        success: false,
        error: '获取统计信息失败'
      };
    }
  }
}

// 导出单例实例
export const userManager = new UserManager();
