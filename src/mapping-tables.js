/**
 * 映射表和常量 - 业务常量定义
 * 
 * 核心职责：
 * - 业务常量定义
 * - 映射关系管理
 * - 枚举值维护
 * - 配置数据存储
 * 
 * 主要功能模块：
 * - 意图类型映射
 * - 技术方向映射
 * - 公司类型映射
 * - 地理位置映射
 * - 薪资关键词映射
 */

// ==================== 意图类型映射 ====================

/**
 * 意图类型常量
 */
const INTENT_TYPES = {
    JOB_RECOMMENDATION: 'job_recommendation',
    PROFILE_UPDATE: 'profile_update',
    COMPANY_INQUIRY: 'company_inquiry',
    THIRD_PARTY_RECOMMENDATION: 'third_party_recommendation',
    AMBIGUITY_RESOLUTION: 'ambiguity_resolution',
    DETAILED_JD_REQUEST: 'detailed_jd_request',
    SECOND_RECOMMENDATION: 'second_recommendation',
    SALARY_STRUCTURE_INQUIRY: 'salary_structure_inquiry',
    CREDIBILITY_INQUIRY: 'credibility_inquiry',
    GENERAL_CONVERSATION: 'general_conversation',
    OPENING_MESSAGE: 'opening_message',
    INFORMATION_COLLECTION: 'information_collection',
    UNKNOWN: 'unknown'
};

/**
 * 意图识别关键词映射
 */
const INTENT_KEYWORDS = {
    [INTENT_TYPES.JOB_RECOMMENDATION]: [
        '推荐', '职位', '工作', '岗位', '机会', '找工作', '求职',
        '有什么', '适合', '匹配', '合适', '推荐一些'
    ],
    [INTENT_TYPES.PROFILE_UPDATE]: [
        '我是', '我的', '更新', '修改', '个人信息', '简历',
        '经验', '技能', '学历', '工作经历'
    ],
    [INTENT_TYPES.COMPANY_INQUIRY]: [
        '公司', '企业', '怎么样', '如何', '待遇', '文化',
        '发展', '前景', '规模', '背景'
    ],
    [INTENT_TYPES.SALARY_STRUCTURE_INQUIRY]: [
        '薪资', '工资', '薪水', '收入', '待遇', '薪酬',
        '多少钱', '年薪', '月薪', '奖金'
    ]
};

/**
 * 获取意图类型描述
 * @param {string} intentType - 意图类型
 * @returns {string} 意图描述
 */
function getIntentDescription(intentType) {
    const descriptions = {
        [INTENT_TYPES.JOB_RECOMMENDATION]: '职位推荐请求',
        [INTENT_TYPES.PROFILE_UPDATE]: '档案更新请求',
        [INTENT_TYPES.COMPANY_INQUIRY]: '公司询问请求',
        [INTENT_TYPES.THIRD_PARTY_RECOMMENDATION]: '第三方推荐请求',
        [INTENT_TYPES.AMBIGUITY_RESOLUTION]: '歧义澄清请求',
        [INTENT_TYPES.DETAILED_JD_REQUEST]: '详细JD请求',
        [INTENT_TYPES.SECOND_RECOMMENDATION]: '第二次推荐请求',
        [INTENT_TYPES.SALARY_STRUCTURE_INQUIRY]: '薪资结构询问',
        [INTENT_TYPES.CREDIBILITY_INQUIRY]: '可信度询问',
        [INTENT_TYPES.GENERAL_CONVERSATION]: '一般对话',
        [INTENT_TYPES.OPENING_MESSAGE]: '开场白',
        [INTENT_TYPES.INFORMATION_COLLECTION]: '信息收集',
        [INTENT_TYPES.UNKNOWN]: '未知意图'
    };
    return descriptions[intentType] || '未知意图';
}

// ==================== 技术方向映射 ====================

/**
 * 技术方向分类常量
 */
const TECH_CATEGORIES = {
    FRONTEND: 'frontend',
    BACKEND: 'backend',
    MOBILE: 'mobile',
    DEVOPS: 'devops',
    DATA: 'data',
    AI_ML: 'ai_ml',
    GAME: 'game',
    BLOCKCHAIN: 'blockchain',
    SECURITY: 'security',
    TESTING: 'testing',
    DESIGN: 'design',
    PRODUCT: 'product'
};

/**
 * 技术方向关键词映射
 */
const TECH_KEYWORDS = {
    [TECH_CATEGORIES.FRONTEND]: [
        'React', 'Vue', 'Angular', 'JavaScript', 'TypeScript',
        'HTML', 'CSS', 'Webpack', '前端', 'UI'
    ],
    [TECH_CATEGORIES.BACKEND]: [
        'Java', 'Python', 'Node.js', 'Go', 'C++',
        'Spring', 'Django', 'Express', '后端', '服务端'
    ],
    [TECH_CATEGORIES.MOBILE]: [
        'iOS', 'Android', 'React Native', 'Flutter',
        'Swift', 'Kotlin', '移动端', '手机'
    ],
    [TECH_CATEGORIES.DATA]: [
        'SQL', 'MySQL', 'PostgreSQL', 'MongoDB',
        '数据库', '数据分析', '大数据', 'Hadoop'
    ]
};

/**
 * 获取技术方向名称
 * @param {string} techCategory - 技术分类
 * @returns {string} 技术方向名称
 */
function getTechCategoryName(techCategory) {
    const names = {
        [TECH_CATEGORIES.FRONTEND]: '前端开发',
        [TECH_CATEGORIES.BACKEND]: '后端开发',
        [TECH_CATEGORIES.MOBILE]: '移动开发',
        [TECH_CATEGORIES.DEVOPS]: '运维开发',
        [TECH_CATEGORIES.DATA]: '数据开发',
        [TECH_CATEGORIES.AI_ML]: '人工智能/机器学习',
        [TECH_CATEGORIES.GAME]: '游戏开发',
        [TECH_CATEGORIES.BLOCKCHAIN]: '区块链',
        [TECH_CATEGORIES.SECURITY]: '信息安全',
        [TECH_CATEGORIES.TESTING]: '测试开发',
        [TECH_CATEGORIES.DESIGN]: '设计',
        [TECH_CATEGORIES.PRODUCT]: '产品'
    };
    return names[techCategory] || '未知技术方向';
}

// ==================== 公司类型映射 ====================

/**
 * 公司类型常量
 */
const COMPANY_TYPES = {
    BIG_TECH: 'big_tech',
    STATE_OWNED: 'state_owned',
    MEDIUM: 'medium',
    STARTUP: 'startup',
    FOREIGN: 'foreign',
    UNICORN: 'unicorn'
};

/**
 * 公司类型关键词映射
 */
const COMPANY_TYPE_KEYWORDS = {
    [COMPANY_TYPES.BIG_TECH]: [
        '大厂', '头部', '互联网大厂', 'BAT', 'TMD',
        '阿里', '腾讯', '百度', '字节', '美团'
    ],
    [COMPANY_TYPES.STATE_OWNED]: [
        '国企', '央企', '事业单位', '国有',
        '中国移动', '中国电信', '国家电网'
    ],
    [COMPANY_TYPES.MEDIUM]: [
        '中型', '中等', '规模适中', '稳定',
        '传统企业', '制造业'
    ],
    [COMPANY_TYPES.STARTUP]: [
        '创业', '初创', '小公司', '新兴',
        '创业公司', '初创企业'
    ]
};

/**
 * 获取公司类型名称
 * @param {string} companyType - 公司类型
 * @returns {string} 公司类型名称
 */
function getCompanyTypeName(companyType) {
    const names = {
        [COMPANY_TYPES.BIG_TECH]: '头部大厂',
        [COMPANY_TYPES.STATE_OWNED]: '国企央企',
        [COMPANY_TYPES.MEDIUM]: '中型企业',
        [COMPANY_TYPES.STARTUP]: '创业公司',
        [COMPANY_TYPES.FOREIGN]: '外企',
        [COMPANY_TYPES.UNICORN]: '独角兽'
    };
    return names[companyType] || '未知公司类型';
}

/**
 * 获取公司类型优势描述
 * @param {string} companyType - 公司类型
 * @returns {Array} 优势描述列表
 */
function getCompanyTypeAdvantages(companyType) {
    const advantages = {
        [COMPANY_TYPES.BIG_TECH]: [
            '技术实力强', '平台大', '发展机会多',
            '薪资待遇好', '品牌知名度高'
        ],
        [COMPANY_TYPES.STATE_OWNED]: [
            '工作稳定', '福利完善', '社会地位高',
            '工作压力相对较小', '退休保障好'
        ],
        [COMPANY_TYPES.MEDIUM]: [
            '发展空间大', '工作内容丰富', '学习机会多',
            '团队氛围好', '晋升机会多'
        ],
        [COMPANY_TYPES.STARTUP]: [
            '成长空间大', '股权激励', '技术挑战性强',
            '决策效率高', '创新氛围浓厚'
        ]
    };
    return advantages[companyType] || [];
}

// ==================== 地理位置映射 ====================

/**
 * 城市等级常量
 */
const CITY_LEVELS = {
    TIER_1: 'tier_1',
    TIER_2: 'tier_2',
    TIER_3: 'tier_3',
    TIER_4: 'tier_4'
};

/**
 * 城市分级映射
 */
const CITY_TIER_MAPPING = {
    [CITY_LEVELS.TIER_1]: [
        '北京', '上海', '广州', '深圳'
    ],
    [CITY_LEVELS.TIER_2]: [
        '杭州', '南京', '武汉', '成都', '重庆',
        '天津', '苏州', '西安', '长沙', '沈阳'
    ],
    [CITY_LEVELS.TIER_3]: [
        '无锡', '佛山', '合肥', '大连', '福州',
        '厦门', '哈尔滨', '济南', '温州', '长春'
    ]
};

/**
 * 地理位置关键词映射
 */
const LOCATION_KEYWORDS = {
    '北京': ['北京', '帝都', 'BJ', '京城'],
    '上海': ['上海', '魔都', 'SH', '沪'],
    '深圳': ['深圳', 'SZ', '鹏城', '特区'],
    '杭州': ['杭州', 'HZ', '西湖', '阿里总部'],
    '广州': ['广州', 'GZ', '羊城', '花城']
};

/**
 * 获取城市等级
 * @param {string} cityName - 城市名称
 * @returns {string} 城市等级
 */
function getCityTier(cityName) {
    for (const [tier, cities] of Object.entries(CITY_TIER_MAPPING)) {
        if (cities.includes(cityName)) {
            return tier;
        }
    }
    return CITY_LEVELS.TIER_4;
}

/**
 * 获取城市等级名称
 * @param {string} cityTier - 城市等级
 * @returns {string} 等级名称
 */
function getCityTierName(cityTier) {
    const names = {
        [CITY_LEVELS.TIER_1]: '一线城市',
        [CITY_LEVELS.TIER_2]: '二线城市',
        [CITY_LEVELS.TIER_3]: '三线城市',
        [CITY_LEVELS.TIER_4]: '其他城市'
    };
    return names[cityTier] || '未知等级';
}

// ==================== 薪资关键词映射 ====================

/**
 * 薪资范围常量
 */
const SALARY_RANGES = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    VERY_HIGH: 'very_high'
};

/**
 * 薪资关键词映射
 */
const SALARY_KEYWORDS = {
    [SALARY_RANGES.LOW]: [
        '5k', '6k', '7k', '8k', '9k', '10k',
        '5000', '6000', '7000', '8000', '9000', '10000'
    ],
    [SALARY_RANGES.MEDIUM]: [
        '15k', '20k', '25k', '30k',
        '15000', '20000', '25000', '30000'
    ],
    [SALARY_RANGES.HIGH]: [
        '35k', '40k', '45k', '50k',
        '35000', '40000', '45000', '50000'
    ],
    [SALARY_RANGES.VERY_HIGH]: [
        '60k', '70k', '80k', '100k',
        '60000', '70000', '80000', '100000'
    ]
};

/**
 * 获取薪资范围描述
 * @param {string} salaryRange - 薪资范围
 * @returns {string} 范围描述
 */
function getSalaryRangeDescription(salaryRange) {
    const descriptions = {
        [SALARY_RANGES.LOW]: '入门级薪资',
        [SALARY_RANGES.MEDIUM]: '中等薪资',
        [SALARY_RANGES.HIGH]: '高薪',
        [SALARY_RANGES.VERY_HIGH]: '顶级薪资'
    };
    return descriptions[salaryRange] || '未知薪资范围';
}

/**
 * 根据薪资金额获取薪资范围
 * @param {number} salary - 薪资金额
 * @returns {string} 薪资范围
 */
function getSalaryRange(salary) {
    if (salary <= 10000) return SALARY_RANGES.LOW;
    if (salary <= 30000) return SALARY_RANGES.MEDIUM;
    if (salary <= 50000) return SALARY_RANGES.HIGH;
    return SALARY_RANGES.VERY_HIGH;
}

// ==================== 职位级别映射 ====================

/**
 * 职位级别常量
 */
const JOB_LEVELS = {
    JUNIOR: 'junior',
    MIDDLE: 'middle',
    SENIOR: 'senior',
    LEAD: 'lead',
    MANAGER: 'manager',
    DIRECTOR: 'director'
};

/**
 * 职位级别关键词映射
 */
const JOB_LEVEL_KEYWORDS = {
    [JOB_LEVELS.JUNIOR]: [
        '初级', '入门', '新手', '应届', '1-3年'
    ],
    [JOB_LEVELS.MIDDLE]: [
        '中级', '中等', '3-5年', '有经验'
    ],
    [JOB_LEVELS.SENIOR]: [
        '高级', '资深', '5-8年', '专家'
    ],
    [JOB_LEVELS.LEAD]: [
        '技术负责人', 'Tech Lead', '架构师', '8年以上'
    ]
};

/**
 * 获取职位级别名称
 * @param {string} jobLevel - 职位级别
 * @returns {string} 级别名称
 */
function getJobLevelName(jobLevel) {
    const names = {
        [JOB_LEVELS.JUNIOR]: '初级',
        [JOB_LEVELS.MIDDLE]: '中级',
        [JOB_LEVELS.SENIOR]: '高级',
        [JOB_LEVELS.LEAD]: '技术负责人',
        [JOB_LEVELS.MANAGER]: '管理岗',
        [JOB_LEVELS.DIRECTOR]: '总监级'
    };
    return names[jobLevel] || '未知级别';
}

// ==================== 导出模块 ====================

module.exports = {
    // 意图类型映射
    INTENT_TYPES,
    INTENT_KEYWORDS,
    getIntentDescription,
    
    // 技术方向映射
    TECH_CATEGORIES,
    TECH_KEYWORDS,
    getTechCategoryName,
    
    // 公司类型映射
    COMPANY_TYPES,
    COMPANY_TYPE_KEYWORDS,
    getCompanyTypeName,
    getCompanyTypeAdvantages,
    
    // 地理位置映射
    CITY_LEVELS,
    CITY_TIER_MAPPING,
    LOCATION_KEYWORDS,
    getCityTier,
    getCityTierName,
    
    // 薪资关键词映射
    SALARY_RANGES,
    SALARY_KEYWORDS,
    getSalaryRangeDescription,
    getSalaryRange,
    
    // 职位级别映射
    JOB_LEVELS,
    JOB_LEVEL_KEYWORDS,
    getJobLevelName
};
