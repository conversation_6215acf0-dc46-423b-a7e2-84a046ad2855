/**
 * Katrina AI 用户和会话管理
 * 处理用户状态、会话生命周期、消息流转
 */

import { candidateRepo, conversationRepo } from "../data/database.js";
import { validateEmail, validateMessage } from "../utils/validators.js";
import { UserError, SessionError } from "../utils/error-handlers.js";
import { generateId, formatTimestamp } from "../utils/utilities.js";
import { SESSION_CONFIG, MESSAGE_TYPES } from "../utils/config.js";

// ==================== 会话管理器 ====================

export class SessionManager {
  constructor() {
    this.activeSessions = new Map();
    this.sessionTimeouts = new Map();
  }

  /**
   * 创建新会话
   */
  async createSession(email) {
    try {
      const validation = validateEmail(email);
      if (!validation.valid) {
        throw new UserError(`邮箱格式错误: ${validation.error}`);
      }

      const sessionId = generateId("session");
      const session = {
        id: sessionId,
        email: email.toLowerCase(),
        startTime: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        status: "active",
        context: {
          currentIntent: null,
          ambiguityState: null,
          lastRecommendations: [],
          conversationStage: "greeting",
        },
      };

      // 保存到内存
      this.activeSessions.set(sessionId, session);

      // 设置超时清理
      this.setSessionTimeout(sessionId);

      console.log(`✅ 创建会话: ${sessionId} for ${email}`);
      return session;
    } catch (error) {
      throw new SessionError(`创建会话失败: ${error.message}`);
    }
  }

  /**
   * 获取会话
   */
  getSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new SessionError(`会话不存在: ${sessionId}`);
    }

    // 检查会话是否过期
    const now = new Date();
    const lastActivity = new Date(session.lastActivity);
    const timeDiff = now - lastActivity;

    if (timeDiff > SESSION_CONFIG.TIMEOUT) {
      this.destroySession(sessionId);
      throw new SessionError(`会话已过期: ${sessionId}`);
    }

    return session;
  }

  /**
   * 更新会话活动时间
   */
  updateSessionActivity(sessionId, context = {}) {
    const session = this.getSession(sessionId);

    session.lastActivity = new Date();
    session.messageCount += 1;

    // 更新上下文
    if (context.intent) {
      session.context.currentIntent = context.intent;
    }
    if (context.ambiguityState) {
      session.context.ambiguityState = context.ambiguityState;
    }
    if (context.recommendations) {
      session.context.lastRecommendations = context.recommendations;
    }
    if (context.stage) {
      session.context.conversationStage = context.stage;
    }

    // 重新设置超时
    this.setSessionTimeout(sessionId);

    return session;
  }

  /**
   * 设置会话超时
   */
  setSessionTimeout(sessionId) {
    // 清除旧的超时
    if (this.sessionTimeouts.has(sessionId)) {
      clearTimeout(this.sessionTimeouts.get(sessionId));
    }

    // 设置新的超时
    const timeoutId = setTimeout(() => {
      this.destroySession(sessionId);
    }, SESSION_CONFIG.TIMEOUT);

    this.sessionTimeouts.set(sessionId, timeoutId);
  }

  /**
   * 销毁会话
   */
  destroySession(sessionId) {
    // 清除超时
    if (this.sessionTimeouts.has(sessionId)) {
      clearTimeout(this.sessionTimeouts.get(sessionId));
      this.sessionTimeouts.delete(sessionId);
    }

    // 移除会话
    this.activeSessions.delete(sessionId);

    console.log(`🗑️ 销毁会话: ${sessionId}`);
  }

  /**
   * 获取会话统计
   */
  getSessionStats() {
    return {
      activeSessionCount: this.activeSessions.size,
      totalSessions: this.activeSessions.size,
      averageMessageCount:
        Array.from(this.activeSessions.values()).reduce(
          (sum, session) => sum + session.messageCount,
          0
        ) / this.activeSessions.size || 0,
    };
  }
}

// ==================== 用户管理器 ====================

export class UserManager {
  constructor() {
    this.sessionManager = new SessionManager();
  }

  /**
   * 用户登录/注册
   */
  async authenticateUser(email) {
    try {
      const validation = validateEmail(email);
      if (!validation.valid) {
        throw new UserError(`邮箱格式错误: ${validation.error}`);
      }

      const normalizedEmail = email.toLowerCase();

      // 检查用户是否存在
      let candidate = await candidateRepo.getCandidateByEmail(normalizedEmail);

      if (!candidate) {
        // 创建新用户
        candidate = await candidateRepo.upsertCandidate(normalizedEmail, {
          email: normalizedEmail,
          status: "new",
          created_at: new Date().toISOString(),
        });
        console.log(`👤 创建新用户: ${normalizedEmail}`);
      } else {
        console.log(`👤 用户登录: ${normalizedEmail}`);
      }

      // 创建会话
      const session = await this.sessionManager.createSession(normalizedEmail);

      return {
        user: candidate,
        session: session,
      };
    } catch (error) {
      throw new UserError(`用户认证失败: ${error.message}`);
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(sessionId, message, metadata = {}) {
    try {
      // 验证消息
      const messageValidation = validateMessage(message);
      if (!messageValidation.valid) {
        throw new UserError(`消息格式错误: ${messageValidation.error}`);
      }

      // 获取会话
      const session = this.sessionManager.getSession(sessionId);

      // 更新会话活动
      this.sessionManager.updateSessionActivity(sessionId, {
        intent: metadata.intent,
        stage: metadata.stage,
      });

      // 构造消息对象
      const messageObj = {
        id: generateId("msg"),
        sessionId: sessionId,
        email: session.email,
        content: messageValidation.value,
        type: MESSAGE_TYPES.USER,
        timestamp: new Date(),
        metadata: {
          ...metadata,
          messageCount: session.messageCount,
        },
      };

      return messageObj;
    } catch (error) {
      throw new UserError(`发送消息失败: ${error.message}`);
    }
  }

  /**
   * 保存对话记录
   */
  async saveConversation(sessionId, userMessage, botResponse, metadata = {}) {
    try {
      const session = this.sessionManager.getSession(sessionId);

      await conversationRepo.saveConversation(
        sessionId,
        session.email,
        userMessage,
        botResponse,
        {
          messageType: metadata.messageType || "chat",
          intent: metadata.intent,
          recommendations: metadata.recommendations || [],
          tokensUsed: metadata.tokensUsed || 0,
          responseTime: metadata.responseTime || 0,
          conversationStage: session.context.conversationStage,
        }
      );

      console.log(`💾 保存对话: ${sessionId}`);
    } catch (error) {
      throw new UserError(`保存对话失败: ${error.message}`);
    }
  }

  /**
   * 获取用户对话历史
   */
  async getUserHistory(email, limit = 20) {
    try {
      const validation = validateEmail(email);
      if (!validation.valid) {
        throw new UserError(`邮箱格式错误: ${validation.error}`);
      }

      const history = await conversationRepo.getConversationHistory(
        email.toLowerCase(),
        limit
      );

      return history.map((record) => ({
        userMessage: record.user_message,
        botResponse: record.bot_response,
        timestamp: record.created_at,
        intent: record.intent_detected,
        recommendations: record.recommendations_shown || [],
      }));
    } catch (error) {
      throw new UserError(`获取历史记录失败: ${error.message}`);
    }
  }

  /**
   * 更新用户档案
   */
  async updateUserProfile(email, profileData) {
    try {
      const validation = validateEmail(email);
      if (!validation.valid) {
        throw new UserError(`邮箱格式错误: ${validation.error}`);
      }

      const updatedProfile = await candidateRepo.upsertCandidate(
        email.toLowerCase(),
        profileData
      );

      console.log(`📝 更新用户档案: ${email}`);
      return updatedProfile;
    } catch (error) {
      throw new UserError(`更新档案失败: ${error.message}`);
    }
  }

  /**
   * 获取用户档案
   */
  async getUserProfile(email) {
    try {
      const validation = validateEmail(email);
      if (!validation.valid) {
        throw new UserError(`邮箱格式错误: ${validation.error}`);
      }

      const profile = await candidateRepo.getCandidateByEmail(
        email.toLowerCase()
      );

      return profile;
    } catch (error) {
      throw new UserError(`获取档案失败: ${error.message}`);
    }
  }

  /**
   * 获取会话信息
   */
  getSessionInfo(sessionId) {
    try {
      return this.sessionManager.getSession(sessionId);
    } catch (error) {
      throw new UserError(`获取会话信息失败: ${error.message}`);
    }
  }

  /**
   * 结束会话
   */
  endSession(sessionId) {
    try {
      this.sessionManager.destroySession(sessionId);
      console.log(`🔚 结束会话: ${sessionId}`);
    } catch (error) {
      throw new UserError(`结束会话失败: ${error.message}`);
    }
  }

  /**
   * 获取系统统计
   */
  getSystemStats() {
    return {
      sessions: this.sessionManager.getSessionStats(),
      timestamp: new Date().toISOString(),
    };
  }
}

// ==================== 消息处理器 ====================

export class MessageProcessor {
  constructor(userManager) {
    this.userManager = userManager;
  }

  /**
   * 处理入站消息
   */
  async processIncomingMessage(sessionId, message, metadata = {}) {
    try {
      // 发送消息
      const messageObj = await this.userManager.sendMessage(
        sessionId,
        message,
        metadata
      );

      // 获取会话上下文
      const session = this.userManager.getSessionInfo(sessionId);

      return {
        message: messageObj,
        session: session,
        context: session.context,
      };
    } catch (error) {
      throw new UserError(`处理消息失败: ${error.message}`);
    }
  }

  /**
   * 处理出站响应
   */
  async processOutgoingResponse(
    sessionId,
    userMessage,
    botResponse,
    metadata = {}
  ) {
    try {
      // 保存对话记录
      await this.userManager.saveConversation(
        sessionId,
        userMessage,
        botResponse,
        metadata
      );

      // 更新会话上下文
      this.userManager.sessionManager.updateSessionActivity(sessionId, {
        intent: metadata.intent,
        recommendations: metadata.recommendations,
        stage: metadata.stage,
      });

      return {
        success: true,
        sessionId: sessionId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new UserError(`处理响应失败: ${error.message}`);
    }
  }
}

// ==================== 导出实例 ====================

export const userManager = new UserManager();
export const messageProcessor = new MessageProcessor(userManager);

export default {
  UserManager,
  SessionManager,
  MessageProcessor,
  userManager,
  messageProcessor,
};
