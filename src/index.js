/**
 * AI招聘助手系统主入口文件
 * 负责系统初始化和模块协调
 */

// ==================== 核心模块导入 ====================

// 核心业务逻辑
import { messageProcessor } from "./core/message-processor.js";
import { databaseManager } from "./core/database-manager.js";
import { aiServices } from "./core/ai-services.js";
import { passiveRecommender } from "./core/passive-recommender.js";
import { activeRecommender } from "./core/active-recommender.js";
import { techMapper } from "./core/tech-mapper.js";

// API和用户管理
import { userManager } from "./api/user-manager.js";
import * as apiRoutes from "./api/api-routes.js";

// 前端界面
import ChatInterface from "./frontend/chat-interface.js";
import { addStyles, getDefaultStyles } from "./frontend/ui-components.js";

// 配置和工具
import { APP_CONFIG, ENV_CONFIG } from "./config/app-config.js";
import { validateUserMessage } from "./config/validators.js";

// ==================== 系统主类 ====================

class AIRecruitmentSystem {
  constructor() {
    this.isInitialized = false;
    this.components = {
      messageProcessor,
      databaseManager,
      aiServices,
      passiveRecommender,
      activeRecommender,
      techMapper,
      userManager,
    };

    this.chatInterface = null;
    this.config = APP_CONFIG;
  }

  // ==================== 系统初始化 ====================

  /**
   * 初始化整个系统
   * 对应业务逻辑: initialize(options)
   */
  async initialize(options = {}) {
    try {
      console.log("🚀 AI招聘助手系统启动中...");

      // 1. 验证环境配置
      this.validateEnvironment();

      // 2. 初始化数据库连接
      await this.initializeDatabase();

      // 3. 初始化AI服务
      await this.initializeAIServices();

      // 4. 初始化前端界面（如果在浏览器环境）
      if (typeof window !== "undefined") {
        await this.initializeFrontend(options);
      }

      // 5. 启动系统监控
      this.startSystemMonitoring();

      this.isInitialized = true;
      console.log("✅ AI招聘助手系统启动成功");

      return {
        success: true,
        message: "系统初始化成功",
        version: this.config.version,
      };
    } catch (error) {
      console.error("❌ 系统初始化失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 验证环境配置
   * 对应业务逻辑: validateEnvironment()
   */
  validateEnvironment() {
    const requiredEnvVars = ["SUPABASE_URL", "SUPABASE_ANON_KEY"];

    const missingVars = requiredEnvVars.filter(
      (varName) => !ENV_CONFIG[varName]
    );

    if (missingVars.length > 0) {
      throw new Error(`缺少必要的环境变量: ${missingVars.join(", ")}`);
    }

    console.log("✅ 环境配置验证通过");
  }

  /**
   * 初始化数据库连接
   * 对应业务逻辑: initializeDatabase()
   */
  async initializeDatabase() {
    try {
      const isConnected =
        await this.components.databaseManager.checkConnection();

      if (!isConnected) {
        throw new Error("数据库连接失败");
      }

      console.log("✅ 数据库连接成功");
    } catch (error) {
      console.error("❌ 数据库初始化失败:", error);
      throw error;
    }
  }

  /**
   * 初始化AI服务
   * 对应业务逻辑: initializeAIServices()
   */
  async initializeAIServices() {
    try {
      // 这里可以添加AI服务的初始化逻辑
      // 比如验证API密钥、测试连接等

      console.log("✅ AI服务初始化成功");
    } catch (error) {
      console.error("❌ AI服务初始化失败:", error);
      throw error;
    }
  }

  /**
   * 初始化前端界面
   * 对应业务逻辑: initializeFrontend(options)
   */
  async initializeFrontend(options = {}) {
    try {
      const { containerId = "chat-container", enableStyles = true } = options;

      // 添加默认样式
      if (enableStyles) {
        addStyles(getDefaultStyles());
        addStyles(this.getChatInterfaceStyles());
      }

      // 初始化聊天界面
      this.chatInterface = new ChatInterface();

      // 等待DOM加载完成
      if (document.readyState === "loading") {
        await new Promise((resolve) => {
          document.addEventListener("DOMContentLoaded", resolve);
        });
      }

      // 初始化界面
      this.chatInterface.initialize(containerId);

      console.log("✅ 前端界面初始化成功");
    } catch (error) {
      console.error("❌ 前端界面初始化失败:", error);
      throw error;
    }
  }

  /**
   * 启动系统监控
   * 对应业务逻辑: startSystemMonitoring()
   */
  startSystemMonitoring() {
    // 定期清理过期会话
    setInterval(() => {
      this.components.userManager.cleanupExpiredSessions();
    }, 30 * 60 * 1000); // 每30分钟清理一次

    // 系统健康检查
    setInterval(async () => {
      await this.performHealthCheck();
    }, 5 * 60 * 1000); // 每5分钟检查一次

    console.log("✅ 系统监控启动成功");
  }

  // ==================== 核心API ====================

  /**
   * 处理聊天消息 - 主要API入口
   * 对应业务逻辑: processMessage(message, sessionUuid)
   */
  async processMessage(message, sessionUuid) {
    try {
      if (!this.isInitialized) {
        throw new Error("系统未初始化");
      }

      // 验证消息
      const validation = validateUserMessage(message);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // 处理消息
      const result = await this.components.messageProcessor.processMessage(
        validation.cleaned,
        sessionUuid
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error("处理消息失败:", error);
      return {
        success: false,
        error: "处理消息时发生错误",
      };
    }
  }

  /**
   * 获取候选人信息
   * 对应业务逻辑: getCandidateInfo(userId)
   */
  async getCandidateInfo(userId) {
    try {
      if (!this.isInitialized) {
        throw new Error("系统未初始化");
      }

      const result = await this.components.databaseManager.getCandidateInfo(
        userId
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error("获取候选人信息失败:", error);
      return {
        success: false,
        error: "获取候选人信息时发生错误",
      };
    }
  }

  /**
   * 搜索职位
   * 对应业务逻辑: searchJobs(criteria)
   */
  async searchJobs(criteria) {
    try {
      if (!this.isInitialized) {
        throw new Error("系统未初始化");
      }

      const jobs = await this.components.databaseManager.queryMatchingJobs(
        criteria,
        "search"
      );

      return {
        success: true,
        data: {
          jobs: jobs,
          total: jobs.length,
        },
      };
    } catch (error) {
      console.error("搜索职位失败:", error);
      return {
        success: false,
        error: "搜索职位时发生错误",
      };
    }
  }

  // ==================== 系统管理 ====================

  /**
   * 系统健康检查
   * 对应业务逻辑: performHealthCheck()
   */
  async performHealthCheck() {
    try {
      const checks = {
        database: await this.components.databaseManager.checkConnection(),
        messageProcessor: true, // 消息处理器状态检查
        aiServices: true, // AI服务状态检查
        timestamp: new Date().toISOString(),
      };

      const allHealthy = Object.values(checks).every((status) =>
        typeof status === "boolean" ? status : true
      );

      if (!allHealthy) {
        console.warn("⚠️ 系统健康检查发现问题:", checks);
      }

      return {
        success: allHealthy,
        checks: checks,
      };
    } catch (error) {
      console.error("❌ 系统健康检查失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 获取系统状态
   * 对应业务逻辑: getSystemStatus()
   */
  getSystemStatus() {
    return {
      initialized: this.isInitialized,
      version: this.config.version,
      environment: ENV_CONFIG.NODE_ENV || "development",
      uptime: process?.uptime?.() || 0,
      components: Object.keys(this.components),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 关闭系统
   * 对应业务逻辑: shutdown()
   */
  async shutdown() {
    try {
      console.log("🔄 系统关闭中...");

      // 清理前端界面
      if (this.chatInterface) {
        this.chatInterface.destroy();
      }

      // 清理用户会话
      this.components.userManager.cleanupExpiredSessions();

      this.isInitialized = false;
      console.log("✅ 系统已安全关闭");

      return {
        success: true,
        message: "系统已关闭",
      };
    } catch (error) {
      console.error("❌ 系统关闭失败:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 样式定义 ====================

  /**
   * 获取聊天界面样式
   * 对应业务逻辑: getChatInterfaceStyles()
   */
  getChatInterfaceStyles() {
    return `
      .chat-interface {
        display: flex;
        flex-direction: column;
        height: 600px;
        max-width: 800px;
        margin: 0 auto;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .chat-header {
        padding: 16px;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chat-header h3 {
        margin: 0;
        color: #333;
        font-size: 18px;
      }

      .chat-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #666;
      }

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #28a745;
      }

      .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background: #f8f9fa;
      }

      .message {
        margin-bottom: 16px;
        max-width: 80%;
      }

      .user-message {
        margin-left: auto;
      }

      .bot-message {
        margin-right: auto;
      }

      .message-content {
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
      }

      .user-message .message-content {
        background: #007bff;
        color: white;
      }

      .bot-message .message-content {
        background: white;
        border: 1px solid #ddd;
        color: #333;
      }

      .message-time {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        text-align: right;
      }

      .bot-message .message-time {
        text-align: left;
      }

      .chat-input-container {
        border-top: 1px solid #eee;
        padding: 16px;
        background: white;
      }

      .loading-indicator {
        display: none;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        color: #666;
        font-size: 14px;
      }

      .typing-dots {
        display: flex;
        gap: 4px;
      }

      .typing-dots span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #007bff;
        animation: typing 1.4s infinite ease-in-out;
      }

      .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
      .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

      @keyframes typing {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
      }

      .chat-input-wrapper {
        display: flex;
        gap: 8px;
        align-items: flex-end;
      }

      .message-input {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 12px 16px;
        resize: none;
        outline: none;
        font-family: inherit;
        font-size: 14px;
        line-height: 1.4;
        max-height: 120px;
      }

      .message-input:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .send-button {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: #007bff;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
      }

      .send-button:hover {
        background: #0056b3;
      }

      .send-button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .input-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 12px;
        color: #666;
      }

      .recommendation-category {
        color: #007bff;
        font-weight: bold;
      }

      .job-item {
        margin: 8px 0;
        padding: 8px;
        background: #f0f8ff;
        border-radius: 4px;
        border-left: 3px solid #007bff;
      }

      .ambiguity-option {
        color: #007bff;
        cursor: pointer;
        text-decoration: underline;
      }

      .ambiguity-option:hover {
        background: #f0f8ff;
      }
    `;
  }
}

// ==================== 全局实例和导出 ====================

// 创建全局系统实例
const aiRecruitmentSystem = new AIRecruitmentSystem();

// 浏览器环境自动初始化
if (typeof window !== "undefined") {
  window.AIRecruitmentSystem = aiRecruitmentSystem;

  // DOM加载完成后自动初始化
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      aiRecruitmentSystem.initialize();
    });
  } else {
    aiRecruitmentSystem.initialize();
  }
}

// 导出系统类和实例
export { AIRecruitmentSystem };
export default aiRecruitmentSystem;
