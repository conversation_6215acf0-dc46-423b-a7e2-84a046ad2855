/**
 * 被动推荐引擎
 * 负责基于候选人信息的主动推荐，包括4x4分类推荐
 */

import { databaseManager } from './database-manager.js';
import { JOB_CATEGORIES, COMPANY_TYPE_MAP, RECOMMENDATION_CONFIG } from '../config/mapping-tables.js';

// ==================== 被动推荐引擎 ====================

export class PassiveRecommender {
  constructor() {
    this.config = RECOMMENDATION_CONFIG;
    this.recommendationCache = new Map();
    this.cacheExpiryTime = 30 * 60 * 1000; // 30分钟
  }

  // ==================== 主推荐生成 ====================

  /**
   * 生成被动推荐
   * 对应业务逻辑: generateRecommendations(candidateInfo, context)
   */
  async generateRecommendations(candidateInfo, context = {}) {
    try {
      // 1. 检查缓存
      const cachedRecommendations = this.getCachedRecommendations(candidateInfo.user_id, candidateInfo);
      if (cachedRecommendations) {
        return cachedRecommendations;
      }

      // 2. 查询匹配职位
      const allJobs = await databaseManager.queryMatchingJobs(candidateInfo, 'passive');
      
      if (!allJobs || allJobs.length === 0) {
        return this.getEmptyRecommendations();
      }

      // 3. 获取已推荐的职位，用于排除
      const excludeCompanies = await this.getRecentRecommendedJobs(candidateInfo.user_id);

      // 4. 过滤已推荐的公司
      const filteredJobs = allJobs.filter(job => 
        !excludeCompanies.includes(job.companies?.company_name)
      );

      // 5. 按4x4规则分类职位
      const categorizedJobs = this.categorizeJobsBy4x4Rule(filteredJobs, context.userPreference);

      // 6. 应用替补机制确保推荐数量
      const finalCategories = this.applyFallbackMechanism(categorizedJobs, filteredJobs);

      // 7. 缓存推荐结果
      this.cacheRecommendations(candidateInfo.user_id, finalCategories, candidateInfo);

      return finalCategories;
    } catch (error) {
      console.error('被动推荐生成失败:', error);
      return this.getEmptyRecommendations();
    }
  }

  /**
   * 生成4x4分类推荐
   * 对应业务逻辑: generate4x4Recommendations(candidateInfo)
   */
  async generate4x4Recommendations(candidateInfo) {
    try {
      const allJobs = await databaseManager.queryMatchingJobs(candidateInfo, 'passive');
      return this.categorizeJobsBy4x4Rule(allJobs, null);
    } catch (error) {
      console.error('4x4推荐生成失败:', error);
      return this.getEmptyRecommendations();
    }
  }

  /**
   * 生成上下文推荐
   * 对应业务逻辑: generateContextualRecommendations(candidateInfo, context)
   */
  async generateContextualRecommendations(candidateInfo, context) {
    try {
      // 基于对话上下文调整推荐策略
      let adjustedCandidateInfo = { ...candidateInfo };

      // 如果上下文中有特定偏好，调整查询条件
      if (context.preferredCompanyType) {
        adjustedCandidateInfo.preferredCompanyType = context.preferredCompanyType;
      }

      if (context.preferredLocation) {
        adjustedCandidateInfo.desired_location_raw = context.preferredLocation;
      }

      const jobs = await databaseManager.queryMatchingJobs(adjustedCandidateInfo, 'contextual');
      return this.categorizeJobsBy4x4Rule(jobs, context.userPreference);
    } catch (error) {
      console.error('上下文推荐生成失败:', error);
      return this.getEmptyRecommendations();
    }
  }

  // ==================== 4x4分类逻辑 ====================

  /**
   * 按4x4规则分类职位
   * 对应业务逻辑: categorizeJobsBy4x4Rule(jobs, userPreference)
   */
  categorizeJobsBy4x4Rule(jobs, userPreference = null) {
    const categories = {
      [JOB_CATEGORIES.BIG_TECH]: [],
      [JOB_CATEGORIES.STATE_OWNED]: [],
      [JOB_CATEGORIES.MEDIUM]: [],
      [JOB_CATEGORIES.STARTUP]: []
    };

    if (!jobs || jobs.length === 0) {
      return categories;
    }

    // 按公司类型分类
    for (const job of jobs) {
      const companyType = this.determineCompanyType(job);
      const categoryName = this.mapCompanyTypeToCategory(companyType);
      
      if (categories[categoryName] && categories[categoryName].length < this.config.matrix4x4.maxJobsPerCategory) {
        categories[categoryName].push(job);
      }
    }

    // 如果有用户偏好，优先推荐偏好类型
    if (userPreference) {
      return this.prioritizeByPreference(categories, userPreference);
    }

    return categories;
  }

  /**
   * 确定公司类型
   * 对应业务逻辑: determineCompanyType(job)
   */
  determineCompanyType(job) {
    if (!job.companies) return 'medium';

    const companyName = job.companies.company_name?.toLowerCase() || '';
    const companyType = job.companies.company_type?.toLowerCase() || '';
    const companySize = job.companies.company_size || 0;

    // 头部大厂判断
    const bigTechKeywords = ['阿里', '腾讯', '字节', '美团', '百度', '京东', '滴滴', '小米'];
    if (bigTechKeywords.some(keyword => companyName.includes(keyword)) || companySize > 10000) {
      return 'bigTech';
    }

    // 国企判断
    const stateOwnedKeywords = ['国企', '央企', '中国', '国家', '中央'];
    if (stateOwnedKeywords.some(keyword => companyName.includes(keyword)) || companyType.includes('国企')) {
      return 'stateOwned';
    }

    // 创业公司判断
    if (companySize < 500 || companyType.includes('创业')) {
      return 'startup';
    }

    // 默认为中型公司
    return 'medium';
  }

  /**
   * 映射公司类型到分类名称
   * 对应业务逻辑: mapCompanyTypeToCategory(companyType)
   */
  mapCompanyTypeToCategory(companyType) {
    const mapping = {
      'bigTech': JOB_CATEGORIES.BIG_TECH,
      'stateOwned': JOB_CATEGORIES.STATE_OWNED,
      'medium': JOB_CATEGORIES.MEDIUM,
      'startup': JOB_CATEGORIES.STARTUP
    };

    return mapping[companyType] || JOB_CATEGORIES.MEDIUM;
  }

  /**
   * 根据用户偏好优先排序
   * 对应业务逻辑: prioritizeByPreference(categories, userPreference)
   */
  prioritizeByPreference(categories, userPreference) {
    if (!userPreference) return categories;

    const preferenceMap = {
      'bigTech': JOB_CATEGORIES.BIG_TECH,
      'stateOwned': JOB_CATEGORIES.STATE_OWNED,
      'medium': JOB_CATEGORIES.MEDIUM,
      'startup': JOB_CATEGORIES.STARTUP
    };

    const preferredCategory = preferenceMap[userPreference];
    if (preferredCategory && categories[preferredCategory]) {
      // 将偏好类型的职位排在前面
      const sortedCategories = { ...categories };
      const preferredJobs = sortedCategories[preferredCategory];
      
      // 可以在这里实现更复杂的排序逻辑
      sortedCategories[preferredCategory] = preferredJobs.sort((a, b) => {
        // 按薪资排序，薪资高的在前
        return (b.salary_max || 0) - (a.salary_max || 0);
      });

      return sortedCategories;
    }

    return categories;
  }

  // ==================== 替补机制 ====================

  /**
   * 应用替补机制确保推荐数量
   * 对应业务逻辑: applyFallbackMechanism(categorizedJobs, allJobs)
   */
  applyFallbackMechanism(categorizedJobs, allJobs) {
    if (!this.config.matrix4x4.fallbackEnabled) {
      return categorizedJobs;
    }

    const result = { ...categorizedJobs };
    const maxPerCategory = this.config.matrix4x4.maxJobsPerCategory;

    // 收集未分类的职位
    const usedJobIds = new Set();
    Object.values(result).forEach(jobs => {
      jobs.forEach(job => usedJobIds.add(job.id));
    });

    const unusedJobs = allJobs.filter(job => !usedJobIds.has(job.id));

    // 为职位数量不足的分类补充职位
    for (const [categoryName, jobs] of Object.entries(result)) {
      if (jobs.length < maxPerCategory && unusedJobs.length > 0) {
        const needed = maxPerCategory - jobs.length;
        const supplementJobs = unusedJobs.splice(0, needed);
        result[categoryName] = [...jobs, ...supplementJobs];
      }
    }

    return result;
  }

  // ==================== 缓存管理 ====================

  /**
   * 获取缓存的推荐结果
   * 对应业务逻辑: getCachedRecommendations(userId, candidateInfo)
   */
  getCachedRecommendations(userId, candidateInfo) {
    try {
      const cacheKey = this.generateCacheKey(userId, candidateInfo);
      const cached = this.recommendationCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiryTime) {
        return cached.data;
      }
      
      // 清理过期缓存
      this.recommendationCache.delete(cacheKey);
      return null;
    } catch (error) {
      console.error('获取缓存推荐失败:', error);
      return null;
    }
  }

  /**
   * 缓存推荐结果
   * 对应业务逻辑: cacheRecommendations(userId, recommendations, candidateInfo)
   */
  cacheRecommendations(userId, recommendations, candidateInfo) {
    try {
      const cacheKey = this.generateCacheKey(userId, candidateInfo);
      this.recommendationCache.set(cacheKey, {
        data: recommendations,
        timestamp: Date.now(),
        candidateInfo: candidateInfo
      });
    } catch (error) {
      console.error('缓存推荐结果失败:', error);
    }
  }

  /**
   * 生成缓存键
   * 对应业务逻辑: generateCacheKey(userId, candidateInfo)
   */
  generateCacheKey(userId, candidateInfo) {
    const keyParts = [
      userId,
      candidateInfo.primary_tech_direction_id || 'no_tech',
      candidateInfo.expected_compensation_min || 'no_salary',
      candidateInfo.desired_location_raw || 'no_location'
    ];
    return keyParts.join('_');
  }

  /**
   * 获取最近推荐的职位公司列表
   * 对应业务逻辑: getRecentRecommendedJobs(userId)
   */
  async getRecentRecommendedJobs(userId) {
    try {
      // 查询最近7天的推荐记录
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // 先获取用户的会话
      const { data: sessions } = await databaseManager.supabase
        .from(databaseManager.tables.chatSessions)
        .select("id")
        .eq("user_id", userId);

      if (!sessions || sessions.length === 0) {
        return [];
      }

      const sessionIds = sessions.map(s => s.id);

      const { data } = await databaseManager.supabase
        .from(databaseManager.tables.chatMessages)
        .select("metadata_json")
        .eq("message_type", "assistant")
        .in("session_id", sessionIds)
        .gte("timestamp", sevenDaysAgo.toISOString())
        .not("metadata_json->hasRecommendations", "is", null);

      const recommendedCompanies = new Set();

      if (data) {
        for (const message of data) {
          if (message.metadata_json?.recommendations) {
            // 从推荐中提取公司名称
            for (const category of Object.values(message.metadata_json.recommendations)) {
              if (Array.isArray(category)) {
                for (const job of category) {
                  if (job.companies?.company_name) {
                    recommendedCompanies.add(job.companies.company_name);
                  }
                }
              }
            }
          }
        }
      }

      return Array.from(recommendedCompanies);
    } catch (error) {
      console.error("获取推荐历史失败:", error);
      return [];
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取空的推荐结果
   */
  getEmptyRecommendations() {
    return {
      [JOB_CATEGORIES.BIG_TECH]: [],
      [JOB_CATEGORIES.STATE_OWNED]: [],
      [JOB_CATEGORIES.MEDIUM]: [],
      [JOB_CATEGORIES.STARTUP]: []
    };
  }
}

// 导出单例实例
export const passiveRecommender = new PassiveRecommender();
