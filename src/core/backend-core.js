/**
 * Katrina AI 后端核心业务逻辑处理中心
 * 包含消息路由、12个对话处理器、业务流程编排
 */

import { createClient } from "@supabase/supabase-js";
import { callDeepSeek } from "../../lib/deepseekAgent.js";

// 初始化数据库连接
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * 核心消息处理器类
 * 实现72个业务逻辑函数中的主要消息路由和处理器
 */
export class MessageProcessor {
  constructor() {
    this.processors = new Map();
    this.ambiguityStates = new Map(); // 歧义状态管理
    this.ambiguityStateCache = new Map(); // 第三方歧义状态缓存
    this.recommendationCache = new Map(); // 推荐缓存
    this.cacheExpiryTime = 30 * 60 * 1000; // 30分钟缓存

    // API分层策略配置
    this.apiTiers = {
      TIER_1: "rules_engine", // 规则引擎
      TIER_2: "deepseek_light", // DeepSeek 轻量任务
      TIER_3: "deepseek_medium", // DeepSeek 中等任务
      TIER_4: "deepseek_heavy", // DeepSeek 重型任务
    };

    // 用户偏好到公司类型映射
    this.preferenceMap = {
      bigTech: "头部大厂",
      stateOwned: "国企",
      medium: "中型公司",
      startup: "创业型公司",
    };

    // 固定话术映射
    this.fixedResponses = {
      你是ai吗:
        "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
      你是机器人吗:
        "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
      你是人工智能吗:
        "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
      你能做什么:
        "我是专业的AI猎头顾问，可以为您推荐合适的技术职位，分析职业发展路径。",
      你的功能: "我专注于技术人才的职位推荐和职业咨询服务。",
    };

    // 薪资职级映射表
    this.salaryLevelMap = {
      "80万+": "P8+/T8+",
      "60-80万": "P7/T7",
      "45-60万": "P6/T6",
      "30-45万": "P5/T5",
      "20-30万": "P4/T4",
      "20万以下": "P3-P4/T3-T4",
    };

    // 噪声词过滤数组
    this.noiseWords = ["算法", "方向", "领域", "技术", "专业", "做", "从事"];

    // 停用词数组
    this.stopWords = ["的", "和", "与", "或", "算法", "技术", "方向", "领域"];

    // 技术方向硬编码映射表（简化版本）
    this.techHierarchyMap = {
      // 一级技术方向
      推荐算法: 725,
      广告算法: 726,
      搜索算法: 727,
      "CV算法（计算机视觉）": 728,
      "NLP算法（自然语言处理）": 729,
      多模态算法: 730,
      "大模型（LLM）算法": 731,
      语音算法: 732,
      视频算法: 733,
      "通用机器学习 / 深度学习算法": 734,

      // 二级技术方向（映射到对应的一级ID）
      召回策略: 725,
      排序优化: 725,
      特征工程与Embedding建模: 725,
      图像识别: 728,
      目标检测: 728,
      图像分割: 728,
      文本分类: 729,
      情感分析: 729,
      命名实体识别: 729,
      语音识别: 732,
      语音合成: 732,
      视频理解: 733,
      视频生成: 733,
    };

    // 语义映射规则
    this.semanticMappings = {
      // CV相关
      图像识别: "CV算法（计算机视觉）",
      计算机视觉: "CV算法（计算机视觉）",
      cv: "CV算法（计算机视觉）",

      // NLP相关
      自然语言处理: "NLP算法（自然语言处理）",
      nlp: "NLP算法（自然语言处理）",

      // 大模型相关
      大模型: "大模型（LLM）算法",
      llm: "大模型（LLM）算法",
      gpt: "大模型（LLM）算法",

      // 推荐相关
      推荐系统: "推荐算法",
      推荐: "推荐算法",

      // 搜索相关
      搜索: "搜索算法",
      搜索引擎: "搜索算法",

      // 广告相关
      广告: "广告算法",
      广告投放: "广告算法",
    };

    // 地理位置映射
    this.locationMap = {
      北京: ["北京", "北京市", "朝阳", "海淀", "西城", "东城"],
      上海: ["上海", "上海市", "浦东", "徐汇", "黄浦", "静安"],
      深圳: ["深圳", "深圳市", "南山", "福田", "罗湖", "宝安"],
      杭州: ["杭州", "杭州市", "西湖", "滨江", "余杭", "萧山"],
      广州: ["广州", "广州市", "天河", "越秀", "海珠", "番禺"],
      成都: ["成都", "成都市", "锦江", "青羊", "金牛", "武侯"],
      南京: ["南京", "南京市", "玄武", "秦淮", "建邺", "鼓楼"],
      武汉: ["武汉", "武汉市", "江岸", "江汉", "硚口", "汉阳"],
      西安: ["西安", "西安市", "新城", "碑林", "莲湖", "灞桥"],
      苏州: ["苏州", "苏州市", "姑苏", "虎丘", "吴中", "相城"],
    };

    this.initializeProcessors();
  }

  /**
   * 主消息处理入口 - 协调整个业务流程
   * 对应业务逻辑: processMessage(userMessage, userEmail, sessionId)
   */
  async processMessage(userMessage, userEmail, sessionId) {
    try {
      // 1. 获取用户信息和对话历史
      const candidateInfo = await this.getCandidateInfo(userEmail);
      const conversationHistory = await this.getConversationHistory(sessionId);

      // 2. AI驱动的上下文分析
      const contextAnalysis = await this.analyzeContext(
        userMessage,
        conversationHistory,
        candidateInfo
      );

      // 3. 智能路由选择
      const handler = this.selectHandler(contextAnalysis, userMessage);

      // 4. 执行对应处理器
      const response = await handler(
        userMessage,
        contextAnalysis,
        candidateInfo,
        userEmail
      );

      // 5. 保存对话记录
      await this.saveConversation(sessionId, userMessage, response);

      return response;
    } catch (error) {
      console.error("消息处理失败:", error);
      return { error: "处理失败，请稍后重试" };
    }
  }

  /**
   * AI驱动的用户意图分析和上下文理解
   * 对应业务逻辑: analyzeContext(userMessage, conversationHistory, candidateInfo)
   */
  async analyzeContext(userMessage, conversationHistory, candidateInfo) {
    try {
      // 构建上下文分析提示词
      const contextPrompt = this.buildContextAnalysisPrompt(
        userMessage,
        conversationHistory,
        candidateInfo
      );

      // 调用DeepSeek进行意图分析
      const response = await callDeepSeek(contextPrompt, {
        temperature: 0.3,
        maxTokens: 300,
      });

      // 解析AI返回的分析结果
      const analysis = this.parseContextAnalysis(response.content);

      // 添加额外的上下文信息
      analysis.tokensUsed = response.tokensUsed || 200;
      analysis.apiTier = "deepseek_medium";
      analysis.timestamp = new Date().toISOString();

      return analysis;
    } catch (error) {
      console.error("上下文分析失败:", error);
      // 回退到规则引擎分析
      return this.fallbackContextAnalysis(
        userMessage,
        conversationHistory,
        candidateInfo
      );
    }
  }

  /**
   * 构建上下文分析提示词
   * 对应业务逻辑: buildContextAnalysisPrompt(userMessage, conversationHistory, candidateInfo)
   */
  buildContextAnalysisPrompt(userMessage, conversationHistory, candidateInfo) {
    const historyContext = this.formatHistoryForContext(conversationHistory);
    const candidateContext = this.formatCandidateInfo(candidateInfo);

    return `
作为AI招聘助手的上下文分析引擎，请分析用户消息的意图和上下文。

用户消息: "${userMessage}"

对话历史:
${historyContext}

候选人信息:
${candidateContext}

请返回JSON格式的分析结果：
{
  "intent": "主要意图类型",
  "confidence": 0.0-1.0,
  "entities": {
    "techDirection": "技术方向或null",
    "company": "公司名称或null",
    "salary": "薪资信息或null",
    "location": "地点信息或null",
    "preference": "偏好类型或null"
  },
  "context": {
    "isThirdParty": false,
    "needsRecommendation": false,
    "hasAmbiguity": false,
    "conversationStage": "阶段描述"
  },
  "thirdPartyInfo": {
    "targetPerson": "目标人员或null",
    "relationship": "关系或null",
    "techDirection": "技术方向或null"
  }
}

意图类型包括：
- job_recommendation: 职位推荐请求
- job_question: 职位相关询问
- preference_expression: 偏好表达
- info_collection: 信息收集
- third_party_inquiry: 第三方询问
- user_background_update: 背景更新
- resume_upload: 简历上传
- general_chat: 通用对话
- job_related_inquiry: 职位相关询问
- company_inquiry: 公司询问
- detailed_jd_inquiry: 详细JD询问
- second_recommendation: 二次推荐
`;
  }

  /**
   * 解析AI返回的上下文分析结果
   * 对应业务逻辑: parseContextAnalysis(aiResponse)
   */
  parseContextAnalysis(aiResponse) {
    try {
      const analysis = JSON.parse(aiResponse);

      // 验证和标准化分析结果
      return {
        intent: analysis.intent || "general_chat",
        confidence: Math.max(0, Math.min(1, analysis.confidence || 0.5)),
        entities: analysis.entities || {},
        context: analysis.context || {},
        thirdPartyInfo: analysis.thirdPartyInfo || {},
        rawResponse: aiResponse,
      };
    } catch (error) {
      console.error("解析上下文分析结果失败:", error);
      return this.getDefaultContextAnalysis();
    }
  }

  /**
   * 回退上下文分析 - 基于规则引擎
   * 对应业务逻辑: fallbackContextAnalysis(userMessage, conversationHistory, candidateInfo)
   */
  fallbackContextAnalysis(userMessage, conversationHistory, candidateInfo) {
    const analysis = {
      intent: "general_chat",
      confidence: 0.6,
      entities: {},
      context: {},
      thirdPartyInfo: {},
      apiTier: "rules_engine",
    };

    // 基于关键词的意图识别
    if (this.detectJobInquiry(userMessage)) {
      analysis.intent = "job_recommendation";
      analysis.confidence = 0.8;
    } else if (this.isCompanyInquiry(userMessage)) {
      analysis.intent = "company_inquiry";
      analysis.confidence = 0.7;
    } else if (this.detectUserPreference(userMessage)) {
      analysis.intent = "preference_expression";
      analysis.confidence = 0.7;
    }

    // 检测第三方询问
    const thirdPartyPatterns = [
      /我(朋友|同事|同学|室友)/,
      /帮.*?(朋友|同事|同学|室友)/,
      /有个(朋友|同事|同学|室友)/,
    ];

    if (thirdPartyPatterns.some((pattern) => pattern.test(userMessage))) {
      analysis.intent = "third_party_inquiry";
      analysis.context.isThirdParty = true;
    }

    return analysis;
  }

  /**
   * 获取默认上下文分析结果
   * 对应业务逻辑: getDefaultContextAnalysis()
   */
  getDefaultContextAnalysis() {
    return {
      intent: "general_chat",
      confidence: 0.5,
      entities: {},
      context: {},
      thirdPartyInfo: {},
      apiTier: "rules_engine",
    };
  }

  /**
   * 格式化对话历史用于上下文分析
   * 对应业务逻辑: formatHistoryForContext(conversationHistory)
   */
  formatHistoryForContext(conversationHistory) {
    if (!conversationHistory || conversationHistory.length === 0) {
      return "无对话历史";
    }

    // 只取最近5轮对话
    const recentHistory = conversationHistory.slice(-10);

    return recentHistory
      .map((msg) => {
        const role = msg.message_type === "user" ? "用户" : "AI";
        return `${role}: ${msg.message_content}`;
      })
      .join("\n");
  }

  /**
   * 格式化候选人信息用于上下文分析
   * 对应业务逻辑: formatCandidateInfo(candidateInfo)
   */
  formatCandidateInfo(candidateInfo) {
    if (!candidateInfo) {
      return "无候选人信息";
    }

    const info = [];
    if (candidateInfo.candidate_tech_direction_raw) {
      info.push(`技术方向: ${candidateInfo.candidate_tech_direction_raw}`);
    }
    if (candidateInfo.current_company_name_raw) {
      info.push(`当前公司: ${candidateInfo.current_company_name_raw}`);
    }
    if (candidateInfo.candidate_level_raw) {
      info.push(`职级: ${candidateInfo.candidate_level_raw}`);
    }
    if (candidateInfo.expected_compensation_raw) {
      info.push(`期望薪资: ${candidateInfo.expected_compensation_raw}`);
    }
    if (candidateInfo.desired_location_raw) {
      info.push(`期望地点: ${candidateInfo.desired_location_raw}`);
    }

    return info.length > 0 ? info.join(", ") : "候选人信息不完整";
  }

  /**
   * 智能路由选择 - 根据意图分析结果选择对应处理器
   * 对应业务逻辑: selectHandler(contextAnalysis, userMessage)
   */
  selectHandler(contextAnalysis, userMessage) {
    const intent = contextAnalysis.intent;

    // 根据意图选择处理器
    switch (intent) {
      case "job_recommendation":
        return this.handleJobRecommendation.bind(this);
      case "job_question":
        return this.handleJobQuestion.bind(this);
      case "preference_expression":
        return this.handlePreferenceExpression.bind(this);
      case "info_collection":
        return this.handleInfoCollection.bind(this);
      case "third_party_inquiry":
        return this.handleThirdPartyInquiry.bind(this);
      case "background_update":
        return this.handleUserBackgroundUpdate.bind(this);
      case "resume_upload":
        return this.handleResumeUpload.bind(this);
      case "company_inquiry":
        return this.handleCompanyInquiry.bind(this);
      case "detailed_jd_inquiry":
        return this.handleDetailedJDInquiry.bind(this);
      case "second_recommendation":
        return this.handleSecondRecommendationInquiry.bind(this);
      default:
        return this.handleGeneralChat.bind(this);
    }
  }

  /**
   * 检查对话历史中是否已存在推荐记录
   * 对应业务逻辑: checkRecommendationsInHistory(conversationHistory)
   */
  checkRecommendationsInHistory(conversationHistory) {
    return conversationHistory.some(
      (msg) =>
        msg.type === "bot_response" &&
        msg.content.includes("推荐") &&
        msg.content.includes("职位")
    );
  }

  /**
   * 初始化12个对话处理器
   */
  initializeProcessors() {
    // 核心业务处理器
    this.processors.set(
      "job_recommendation",
      this.handleJobRecommendation.bind(this)
    );
    this.processors.set("job_question", this.handleJobQuestion.bind(this));
    this.processors.set(
      "preference_expression",
      this.handlePreferenceExpression.bind(this)
    );
    this.processors.set(
      "info_collection",
      this.handleInfoCollection.bind(this)
    );
    this.processors.set(
      "third_party_inquiry",
      this.handleThirdPartyInquiry.bind(this)
    );

    // 辅助业务处理器
    this.processors.set(
      "background_update",
      this.handleUserBackgroundUpdate.bind(this)
    );
    this.processors.set("resume_upload", this.handleResumeUpload.bind(this));
    this.processors.set("general_chat", this.handleGeneralChat.bind(this));
    this.processors.set(
      "job_related_inquiry",
      this.handleJobRelatedInquiry.bind(this)
    );
    this.processors.set(
      "company_inquiry",
      this.handleCompanyInquiry.bind(this)
    );
    this.processors.set(
      "multi_direction",
      this.handleMultiDirectionRecommendation.bind(this)
    );

    // 特殊场景处理器
    this.processors.set("detailed_jd", this.handleDetailedJDInquiry.bind(this));
    this.processors.set(
      "second_recommendation",
      this.handleSecondRecommendationInquiry.bind(this)
    );

    // 高级功能处理器
    this.processors.set("interview_prep", this.handleInterviewPrep.bind(this));
    this.processors.set(
      "skill_assessment",
      this.handleSkillAssessment.bind(this)
    );
    this.processors.set(
      "market_analysis",
      this.handleMarketAnalysis.bind(this)
    );
    this.processors.set("followup", this.handleFollowup.bind(this));
  }

  /**
   * 主消息路由函数
   */
  async processMessage(sessionId, message, context = {}) {
    try {
      // 1. 消息预处理
      const processedMessage = await this.preprocessMessage(message);

      // 2. 意图识别
      const intent = await this.identifyIntent(processedMessage, context);

      // 3. 路由到对应处理器
      const processor =
        this.processors.get(intent) || this.processors.get("greeting");

      // 4. 执行处理
      const response = await processor(sessionId, processedMessage, context);

      // 5. 后处理
      return await this.postprocessResponse(response, sessionId);
    } catch (error) {
      console.error("消息处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  // ==================== 核心业务处理器集群 ====================

  /**
   * 职位推荐处理器 - 处理推荐请求
   * 对应业务逻辑: handleJobRecommendation(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleJobRecommendation(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      // 1. 检查推荐触发条件
      const triggerResult = await this.checkRecommendationTrigger(
        candidateInfo,
        userMessage
      );
      if (!triggerResult.shouldRecommend) {
        return await this.handleInfoCollection(
          userMessage,
          contextAnalysis,
          candidateInfo,
          userId
        );
      }

      // 2. 生成职位推荐
      const recommendations = await this.generateJobRecommendations(
        candidateInfo,
        triggerResult.type,
        triggerResult.userPreference
      );

      // 3. 生成推荐文本
      const recommendationText = await this.generateRecommendationText(
        candidateInfo,
        recommendations,
        triggerResult.type,
        triggerResult.userPreference
      );

      return {
        reply: recommendationText,
        tokensUsed: 150,
        apiTier: "deepseek_medium",
        recommendations: recommendations,
      };
    } catch (error) {
      console.error("职位推荐处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 职位问题处理器 - 处理职位相关询问
   * 对应业务逻辑: handleJobQuestion(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleJobQuestion(userMessage, contextAnalysis, candidateInfo, userId) {
    try {
      // 检查是否为详细JD询问
      if (await this.isDetailedJDInquiry(userMessage, [])) {
        return await this.handleDetailedJDInquiry(
          userMessage,
          candidateInfo,
          userId
        );
      }

      // 检查是否为第二次推荐询问
      if (await this.isSecondRecommendationInquiry(userMessage, [])) {
        return await this.handleSecondRecommendationInquiry(
          userMessage,
          candidateInfo,
          userId
        );
      }

      // 通用职位相关询问处理
      return await this.handleJobRelatedInquiry(
        userMessage,
        contextAnalysis,
        candidateInfo,
        userId
      );
    } catch (error) {
      console.error("职位问题处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 偏好表达处理器 - 处理用户偏好声明
   * 对应业务逻辑: handlePreferenceExpression(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handlePreferenceExpression(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      // 1. 提取用户偏好
      const userPreference = this.detectUserPreference(userMessage);

      // 2. 检查是否满足推荐条件
      const triggerResult = await this.checkRecommendationTrigger(
        candidateInfo,
        userMessage
      );

      if (triggerResult.shouldRecommend) {
        // 生成基于偏好的推荐
        return await this.handleJobRecommendation(
          userMessage,
          contextAnalysis,
          candidateInfo,
          userId
        );
      } else {
        // 收集更多信息
        return await this.handleInfoCollection(
          userMessage,
          contextAnalysis,
          candidateInfo,
          userId
        );
      }
    } catch (error) {
      console.error("偏好表达处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 信息收集处理器 - 收集候选人信息
   * 对应业务逻辑: handleInfoCollection(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleInfoCollection(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      // 1. 提取信息
      const extractedInfo = await this.extractInformation(
        userMessage,
        candidateInfo
      );

      // 2. 更新候选人档案（带歧义检测）
      const updateResult = await this.updateCandidateProfileWithAmbiguity(
        userId,
        extractedInfo
      );

      if (updateResult.hasAmbiguity) {
        return {
          reply: updateResult.reply,
          tokensUsed: updateResult.tokensUsed,
          needsMoreInfo: true,
          ambiguityType: "tech_direction",
        };
      }

      // 3. 检查是否可以推荐
      const triggerResult = await this.checkRecommendationTrigger(
        updateResult.candidateInfo,
        userMessage
      );

      if (triggerResult.shouldRecommend) {
        return await this.handleJobRecommendation(
          userMessage,
          contextAnalysis,
          updateResult.candidateInfo,
          userId
        );
      } else {
        // 继续收集信息
        const missingInfo = this.analyzeMissingInfo(updateResult.candidateInfo);
        return {
          reply: `感谢您提供的信息！为了给您更精准的推荐，还需要了解：${missingInfo.join(
            "、"
          )}`,
          tokensUsed: 0,
          needsMoreInfo: true,
        };
      }
    } catch (error) {
      console.error("信息收集处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 第三方询问处理器 - 处理为他人询问职位
   * 对应业务逻辑: handleThirdPartyInquiry(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleThirdPartyInquiry(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      const { targetPerson, relationship, techDirection } =
        contextAnalysis.thirdPartyInfo;

      // 创建第三方临时档案
      const thirdPartyProfile = {
        candidate_tech_direction_raw: techDirection,
        current_company_name_raw: null,
        candidate_level_raw: null,
        expected_compensation_raw: null,
        desired_location_raw: null,
      };

      // 检查技术方向歧义
      if (techDirection) {
        const techId = await this.getTechDirectionId(
          techDirection,
          thirdPartyProfile
        );
        if (techId && typeof techId === "object" && techId.isMultipleMatch) {
          return await this.generateThirdPartyAmbiguityResponse(
            userMessage,
            techId,
            targetPerson,
            relationship,
            userId
          );
        }
        thirdPartyProfile.primary_tech_direction_id = techId;
      }

      // 生成第三方推荐
      return await this.generateThirdPartyRecommendations(
        thirdPartyProfile,
        targetPerson,
        relationship
      );
    } catch (error) {
      console.error("第三方询问处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 用户背景更新处理器
   * 对应业务逻辑: handleUserBackgroundUpdate(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleUserBackgroundUpdate(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      // 提取背景信息
      const extractedInfo = await this.extractInformation(
        userMessage,
        candidateInfo
      );

      // 更新候选人档案
      const updateResult = await this.updateCandidateProfileWithAmbiguity(
        userId,
        extractedInfo
      );

      if (updateResult.hasAmbiguity) {
        return {
          reply: updateResult.reply,
          tokensUsed: updateResult.tokensUsed,
          needsMoreInfo: true,
          ambiguityType: "tech_direction",
        };
      }

      return {
        reply: "感谢您更新背景信息！我已经记录下来了。",
        tokensUsed: 0,
        candidateInfo: updateResult.candidateInfo,
      };
    } catch (error) {
      console.error("用户背景更新处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 简历上传处理器
   * 对应业务逻辑: handleResumeUpload(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleResumeUpload(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      return {
        reply: "感谢您上传简历！我正在分析您的背景信息，请稍等片刻...",
        tokensUsed: 0,
        needsMoreInfo: false,
      };
    } catch (error) {
      console.error("简历上传处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 通用对话处理器
   * 对应业务逻辑: handleGeneralChat(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleGeneralChat(userMessage, contextAnalysis, candidateInfo, userId) {
    try {
      // 检查固定话术
      const fixedResponse = this.checkFixedResponses(userMessage);
      if (fixedResponse) {
        return {
          reply: fixedResponse,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 检查是否需要自我介绍
      if (this.checkNeedsIntroduction(userMessage, candidateInfo)) {
        return {
          reply: this.getOpeningMessage(),
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 生成对话回复
      return await this.generateConversationReply(userMessage, candidateInfo);
    } catch (error) {
      console.error("通用对话处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 职位相关询问处理器
   * 对应业务逻辑: handleJobRelatedInquiry(userMessage, contextAnalysis, candidateInfo, userId)
   */
  async handleJobRelatedInquiry(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  ) {
    try {
      // 禁止虚构职位信息，引导用户提供更多背景
      const missingInfo = this.analyzeMissingInfo(candidateInfo);

      if (missingInfo.length > 0) {
        return {
          reply: `为了给您提供准确的职位信息，我需要先了解您的背景：${missingInfo.join(
            "、"
          )}`,
          tokensUsed: 0,
          needsMoreInfo: true,
        };
      }

      // 如果信息完整，转到推荐处理
      return await this.handleJobRecommendation(
        userMessage,
        contextAnalysis,
        candidateInfo,
        userId
      );
    } catch (error) {
      console.error("职位相关询问处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  /**
   * 公司询问处理器
   * 对应业务逻辑: handleCompanyInquiry(userMessage, contextAnalysis, candidateInfo)
   */
  async handleCompanyInquiry(userMessage, contextAnalysis, candidateInfo) {
    try {
      // 检查是否为薪资结构询问
      if (this.isSalaryStructureInquiry(userMessage)) {
        return {
          reply:
            "抱歉，目前我们暂未收录该公司的薪资结构信息。如有需要，我们后续会补充。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 提取公司名称
      const companyName = this.extractCompanyFromQuestion(userMessage);
      if (!companyName) {
        return {
          reply: "请告诉我您想了解哪家公司的信息？",
          tokensUsed: 0,
          needsMoreInfo: true,
        };
      }

      // 查询公司信息
      const companyInfo = await this.getCompanyInfo(companyName);
      if (!companyInfo) {
        return {
          reply: `抱歉，我暂时没有${companyName}的详细信息。如果您对这家公司的职位感兴趣，我可以帮您关注相关机会。`,
          tokensUsed: 0,
        };
      }

      // 格式化公司信息回复
      return {
        reply: this.formatCompanyInfo(companyInfo),
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    } catch (error) {
      console.error("公司询问处理失败:", error);
      return this.getErrorResponse(error);
    }
  }

  // ==================== 辅助方法 ====================

  async preprocessMessage(message) {
    // 消息预处理逻辑
    return message.trim();
  }

  async identifyIntent(message, context) {
    // 意图识别逻辑 - 使用AI或规则引擎
    // 暂时返回默认意图
    return "greeting";
  }

  async postprocessResponse(response, sessionId) {
    // 响应后处理逻辑
    return response;
  }

  /**
   * 统一错误响应处理
   * 对应业务逻辑: getErrorResponse(error)
   */
  getErrorResponse(error) {
    return {
      reply: "抱歉，处理您的请求时遇到了问题，请稍后再试。",
      tokensUsed: 0,
      apiTier: "rules_engine",
    };
  }

  // ==================== 信息提取和处理引擎 ====================

  /**
   * AI驱动的信息提取 - 从用户消息中提取结构化候选人信息
   * 对应业务逻辑: extractInformation(userMessage, existingInfo)
   */
  async extractInformation(userMessage, existingInfo) {
    try {
      const prompt = this.buildExtractionPrompt(userMessage, existingInfo);

      const response = await callDeepSeek(prompt, {
        temperature: 0.2,
        maxTokens: 400,
      });

      const extractedInfo = this.parseExtractionResult(response.content);

      // 添加元数据
      extractedInfo.tokensUsed = response.tokensUsed || 200;
      extractedInfo.model = "deepseek";
      extractedInfo.extractionTimestamp = new Date().toISOString();

      return extractedInfo;
    } catch (error) {
      console.error("AI信息提取失败:", error);
      // 回退到规则引擎提取
      return this.fallbackExtractInformation(userMessage, existingInfo);
    }
  }

  /**
   * 构建信息提取提示词
   * 对应业务逻辑: buildExtractionPrompt(userMessage, existingInfo)
   */
  buildExtractionPrompt(userMessage, existingInfo) {
    const existingContext = existingInfo
      ? JSON.stringify(existingInfo, null, 2)
      : "无现有信息";

    return `
作为专业的信息提取引擎，请从用户消息中提取候选人的职业信息。

用户消息: "${userMessage}"

现有候选人信息:
${existingContext}

请严格按照以下JSON格式返回提取结果：
{
  "company": "公司名称或null",
  "level": "职级信息或null",
  "techDirection": "技术方向或null",
  "expectedSalary": "期望薪资或null",
  "location": "期望地点或null",
  "businessScenario": "业务场景或null",
  "isGraduate": false,
  "experience": "工作经验或null",
  "education": "教育背景或null"
}

提取规则：
1. 公司名称：提取当前或之前工作的公司名称
2. 职级：P级、T级、高级、资深、专家、总监等职级信息
3. 技术方向：算法、开发、测试、产品等技术领域
4. 期望薪资：包含数字的薪资表达，保留原始格式
5. 地点：城市、区域等地理位置信息
6. 业务场景：电商、金融、游戏等业务领域
7. 应届生：判断是否为应届毕业生
8. 工作经验：年限、项目经验等
9. 教育背景：学历、学校、专业等

注意：
- 如果信息不明确或不存在，设置为null
- 保持原始表达方式，不要过度解释
- 应届生判断基于关键词：应届、校招、毕业、实习等
`;
  }

  /**
   * 解析AI提取结果
   * 对应业务逻辑: parseExtractionResult(aiResponse)
   */
  parseExtractionResult(aiResponse) {
    try {
      const result = JSON.parse(aiResponse);

      // 验证和标准化结果
      return {
        company: result.company || null,
        level: result.level || null,
        techDirection: result.techDirection || null,
        expectedSalary: result.expectedSalary || null,
        location: result.location || null,
        businessScenario: result.businessScenario || null,
        isGraduate: Boolean(result.isGraduate),
        experience: result.experience || null,
        education: result.education || null,
        rawResponse: aiResponse,
      };
    } catch (error) {
      console.error("解析提取结果失败:", error);
      return this.getEmptyExtractionResult();
    }
  }

  /**
   * 回退信息提取 - 基于规则引擎
   * 对应业务逻辑: fallbackExtractInformation(userMessage, existingInfo)
   */
  fallbackExtractInformation(userMessage, existingInfo) {
    const result = this.getEmptyExtractionResult();
    result.model = "rules_engine";

    // 公司名称提取
    const companyPatterns = [
      /在(.+?)(工作|上班|任职)/,
      /(.+?)(公司|集团|科技|技术)/,
      /从(.+?)跳槽/,
    ];

    for (const pattern of companyPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.company = match[1].trim();
        break;
      }
    }

    // 职级提取
    const levelPatterns = [
      /(P\d+|T\d+)/i,
      /(高级|资深|专家|总监|经理|主管)/,
      /(\d+年经验)/,
    ];

    for (const pattern of levelPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.level = match[1];
        break;
      }
    }

    // 技术方向提取
    const techPatterns = [
      /(算法|开发|测试|产品|运维|数据|前端|后端|全栈)/,
      /(Java|Python|JavaScript|Go|C\+\+|PHP)/i,
      /(推荐|搜索|广告|CV|NLP|大模型)/,
    ];

    for (const pattern of techPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.techDirection = match[1];
        break;
      }
    }

    // 薪资提取
    const salaryPatterns = [/(\d+[万k千])/, /(\d+-\d+[万k千])/, /(年薪\d+)/];

    for (const pattern of salaryPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.expectedSalary = match[1];
        break;
      }
    }

    // 地点提取
    const locationPatterns = [
      /(北京|上海|深圳|杭州|广州|成都|南京|武汉|西安|苏州)/,
      /在(.+?)工作/,
    ];

    for (const pattern of locationPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.location = match[1];
        break;
      }
    }

    // 应届生判断
    const graduateKeywords = ["应届", "校招", "毕业", "实习", "学生"];
    result.isGraduate = graduateKeywords.some((keyword) =>
      userMessage.includes(keyword)
    );

    return result;
  }

  /**
   * 获取空的提取结果
   * 对应业务逻辑: getEmptyExtractionResult()
   */
  getEmptyExtractionResult() {
    return {
      company: null,
      level: null,
      techDirection: null,
      expectedSalary: null,
      location: null,
      businessScenario: null,
      isGraduate: false,
      experience: null,
      education: null,
      tokensUsed: 0,
    };
  }

  /**
   * 候选人档案更新 - 将提取的信息更新到数据库
   * 对应业务逻辑: updateCandidateProfile(userId, extractedInfo)
   */
  async updateCandidateProfile(userId, extractedInfo) {
    try {
      const updateData = {};

      if (extractedInfo.company) {
        updateData.current_company_name_raw = extractedInfo.company;
      }
      if (extractedInfo.level) {
        updateData.candidate_level_raw = extractedInfo.level;
      }
      if (extractedInfo.techDirection) {
        updateData.candidate_tech_direction_raw = extractedInfo.techDirection;
      }
      if (extractedInfo.expectedSalary) {
        updateData.expected_compensation_raw = extractedInfo.expectedSalary;
        const salaryRange = this.parseSalaryRange(extractedInfo.expectedSalary);
        updateData.expected_compensation_min = salaryRange.min;
        updateData.expected_compensation_max = salaryRange.max;
      }
      if (extractedInfo.location) {
        updateData.desired_location_raw = extractedInfo.location;
      }
      if (extractedInfo.businessScenario) {
        updateData.candidate_business_scenario_raw =
          extractedInfo.businessScenario;
      }

      updateData.last_updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from("candidate_profiles")
        .update(updateData)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("候选人档案更新失败:", error);
      throw error;
    }
  }

  /**
   * 支持歧义检测的候选人档案更新
   * 对应业务逻辑: updateCandidateProfileWithAmbiguity(userId, extractedInfo)
   */
  async updateCandidateProfileWithAmbiguity(userId, extractedInfo) {
    try {
      // 检查技术方向歧义
      if (extractedInfo.techDirection) {
        const ambiguityResult = await this.detectTechAmbiguityIntelligently(
          extractedInfo.techDirection
        );

        if (ambiguityResult.needsClarification) {
          // 存储歧义状态
          this.ambiguityStates.set(userId, {
            originalTech: ambiguityResult.originalTech,
            options: ambiguityResult.options,
            waitingForSelection: true,
            timestamp: Date.now(),
          });

          const ambiguityQuestion = this.generateAmbiguityQuestion(
            ambiguityResult.originalTech,
            ambiguityResult.options
          );

          return {
            hasAmbiguity: true,
            reply: ambiguityQuestion,
            tokensUsed: 0,
            needsMoreInfo: true,
            ambiguityType: "tech_direction",
          };
        }

        // 获取技术方向ID
        const techId = await this.getTechDirectionId(
          extractedInfo.techDirection,
          {}
        );
        if (techId) {
          extractedInfo.primary_tech_direction_id = techId;
        }
      }

      // 更新档案
      const updatedProfile = await this.updateCandidateProfile(
        userId,
        extractedInfo
      );

      return {
        hasAmbiguity: false,
        candidateInfo: updatedProfile,
        tokensUsed: extractedInfo.tokensUsed || 0,
        needsMoreInfo: false,
      };
    } catch (error) {
      console.error("带歧义检测的档案更新失败:", error);
      return {
        hasAmbiguity: false,
        candidateInfo: null,
        tokensUsed: 0,
        needsMoreInfo: true,
      };
    }
  }

  /**
   * 薪资解析 - 支持万、k、千等多种单位格式
   * 对应业务逻辑: parseSalaryRange(salaryStr)
   */
  parseSalaryRange(salaryStr) {
    try {
      if (!salaryStr) return { min: null, max: null, value: null };

      const str = salaryStr.toString().toLowerCase();

      // 提取数字
      const numbers = str.match(/\d+/g);
      if (!numbers) return { min: null, max: null, value: null };

      let values = numbers.map((n) => parseInt(n));

      // 处理单位
      if (str.includes("k") || str.includes("千")) {
        values = values.map((v) => v / 10); // k转万
      }

      if (values.length === 1) {
        const value = values[0];
        return { min: value, max: value, value: value };
      } else if (values.length >= 2) {
        const min = Math.min(...values);
        const max = Math.max(...values);
        return { min: min, max: max, value: (min + max) / 2 };
      }

      return { min: null, max: null, value: null };
    } catch (error) {
      console.error("薪资解析失败:", error);
      return { min: null, max: null, value: null };
    }
  }

  // ==================== 业务规则和特殊场景处理 ====================

  /**
   * 检查固定话术回复
   * 对应业务逻辑: checkFixedResponses(userMessage)
   */
  checkFixedResponses(userMessage) {
    const normalizedMessage = userMessage.toLowerCase().trim();
    return this.fixedResponses[normalizedMessage] || null;
  }

  /**
   * 获取标准开场白内容
   * 对应业务逻辑: getOpeningMessage()
   */
  getOpeningMessage() {
    return "您好！我是Katrina，您的AI招聘顾问。我可以帮您推荐合适的技术职位。请告诉我您的技术方向、当前公司和职级，我来为您匹配最适合的机会。";
  }

  /**
   * 检查是否需要自我介绍
   * 对应业务逻辑: checkNeedsIntroduction(userMessage, candidateInfo)
   */
  checkNeedsIntroduction(userMessage, candidateInfo) {
    const greetingPatterns = [/^(你好|您好|hi|hello)/i, /^(介绍|自我介绍)/i];

    return (
      greetingPatterns.some((pattern) => pattern.test(userMessage)) ||
      !candidateInfo ||
      !candidateInfo.candidate_tech_direction_raw
    );
  }

  /**
   * 分析缺失的候选人信息
   * 对应业务逻辑: analyzeMissingInfo(candidateInfo, isGraduate)
   */
  analyzeMissingInfo(candidateInfo, isGraduate = false) {
    const missingInfo = [];

    if (!candidateInfo.candidate_tech_direction_raw) {
      missingInfo.push("技术方向");
    }
    if (!candidateInfo.current_company_name_raw) {
      missingInfo.push("当前公司");
    }
    if (!isGraduate && !candidateInfo.candidate_level_raw) {
      missingInfo.push("职级");
    }
    if (!candidateInfo.expected_compensation_raw) {
      missingInfo.push("期望薪资");
    }

    return missingInfo;
  }

  /**
   * 检查是否为应届生
   * 对应业务逻辑: checkIsGraduate(candidateInfo)
   */
  checkIsGraduate(candidateInfo) {
    if (!candidateInfo.candidate_level_raw) return false;

    const graduateKeywords = ["应届", "校招", "实习", "毕业"];
    return graduateKeywords.some((keyword) =>
      candidateInfo.candidate_level_raw.includes(keyword)
    );
  }

  /**
   * 检测用户偏好类型
   * 对应业务逻辑: detectUserPreference(message)
   */
  detectUserPreference(message) {
    const preferenceMap = {
      大厂: "bigTech",
      头部: "bigTech",
      国企: "stateOwned",
      中型: "medium",
      创业: "startup",
      初创: "startup",
    };

    for (const [keyword, preference] of Object.entries(preferenceMap)) {
      if (message.includes(keyword)) {
        return preference;
      }
    }

    return null;
  }

  /**
   * 检测职位询问意图
   * 对应业务逻辑: detectJobInquiry(message)
   */
  detectJobInquiry(message) {
    const jobInquiryPatterns = [
      /推荐.*职位/,
      /有什么.*工作/,
      /找.*工作/,
      /职位.*推荐/,
      /工作.*机会/,
    ];

    return jobInquiryPatterns.some((pattern) => pattern.test(message));
  }

  /**
   * 检查是否为薪资结构询问
   * 对应业务逻辑: isSalaryStructureInquiry(userMessage)
   */
  isSalaryStructureInquiry(userMessage) {
    const salaryStructurePatterns = [
      /(.+?)(的)?(薪资结构|薪酬结构|工资结构)(是什么|怎么样|如何)/,
      /(.+?)(薪资|薪酬|工资)(体系|制度|结构)/,
      /(.+?)(的)?(薪资|薪酬)(怎么样|如何)/,
    ];

    return salaryStructurePatterns.some((pattern) => pattern.test(userMessage));
  }

  /**
   * 从用户问题中提取公司名称
   * 对应业务逻辑: extractCompanyFromQuestion(userMessage)
   */
  extractCompanyFromQuestion(userMessage) {
    const companyPatterns = [
      /(.+?)没听说过|(.+?)不了解|(.+?)是什么公司|(.+?)怎么样|(.+?)介绍/,
      /介绍.*?(.+?)公司|(.+?)公司.*?介绍/,
      /(.+?)的情况|了解.*?(.+?)/,
    ];

    for (const pattern of companyPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        return match[1] || match[2] || match[3];
      }
    }

    return null;
  }

  // ==================== 数据库操作方法 ====================

  /**
   * 获取候选人信息 - 完整的档案查询
   * 对应业务逻辑: getCandidateInfo(userEmail)
   */
  async getCandidateInfo(userEmail) {
    try {
      // 先获取用户ID
      const { data: user } = await supabase
        .from("users")
        .select("id")
        .eq("email", userEmail)
        .single();

      if (!user) {
        // 如果用户不存在，创建新用户
        const { data: newUser } = await supabase
          .from("users")
          .insert({
            email: userEmail,
            user_type: "candidate",
            created_at: new Date().toISOString(),
            is_active: true,
          })
          .select("id")
          .single();

        if (!newUser) return null;

        // 为新用户创建候选人档案
        const { data: newProfile } = await supabase
          .from("candidate_profiles")
          .insert({
            user_id: newUser.id,
            profile_completeness_score: 0,
            last_updated_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
          })
          .select("*")
          .single();

        return newProfile;
      }

      // 获取候选人档案（包含关联的技术方向信息）
      const { data: profile } = await supabase
        .from("candidate_profiles")
        .select(
          `
          *,
          tech_tree:primary_tech_direction_id(
            id,
            tech_name,
            level,
            parent_tech_id
          ),
          companies:current_company_id(
            id,
            company_name,
            company_type
          )
        `
        )
        .eq("user_id", user.id)
        .single();

      return profile;
    } catch (error) {
      console.error("获取候选人信息失败:", error);
      return null;
    }
  }

  /**
   * 获取或创建会话
   * 对应业务逻辑: getOrCreateSession(sessionUuid, userEmail)
   */
  async getOrCreateSession(sessionUuid, userEmail) {
    try {
      // 先尝试获取现有会话
      const { data: existingSession } = await supabase
        .from("chat_sessions")
        .select("*")
        .eq("session_uuid", sessionUuid)
        .single();

      if (existingSession) {
        // 更新最后活跃时间
        await supabase
          .from("chat_sessions")
          .update({ last_active_at: new Date().toISOString() })
          .eq("id", existingSession.id);

        return existingSession;
      }

      // 获取用户ID
      const { data: user } = await supabase
        .from("users")
        .select("id")
        .eq("email", userEmail)
        .single();

      if (!user) {
        throw new Error("用户不存在");
      }

      // 创建新会话
      const { data: newSession } = await supabase
        .from("chat_sessions")
        .insert({
          session_uuid: sessionUuid,
          user_id: user.id,
          entry_source_url: "web_chat",
          initial_intent: "job_inquiry",
          current_interaction_context: {},
          created_at: new Date().toISOString(),
          last_active_at: new Date().toISOString(),
        })
        .select("*")
        .single();

      return newSession;
    } catch (error) {
      console.error("获取或创建会话失败:", error);
      return null;
    }
  }

  /**
   * 获取对话历史 - 使用session_id而不是session_uuid
   * 对应业务逻辑: getConversationHistory(sessionId)
   */
  async getConversationHistory(sessionId) {
    try {
      // 如果传入的是UUID，先获取session的内部ID
      let actualSessionId = sessionId;

      if (typeof sessionId === "string" && sessionId.includes("-")) {
        const { data: session } = await supabase
          .from("chat_sessions")
          .select("id")
          .eq("session_uuid", sessionId)
          .single();

        if (session) {
          actualSessionId = session.id;
        }
      }

      const { data } = await supabase
        .from("chat_messages")
        .select("*")
        .eq("session_id", actualSessionId)
        .order("timestamp", { ascending: true });

      return data || [];
    } catch (error) {
      console.error("获取对话历史失败:", error);
      return [];
    }
  }

  /**
   * 保存对话记录 - 使用正确的session_id
   * 对应业务逻辑: saveConversation(sessionId, userMessage, response)
   */
  async saveConversation(sessionId, userMessage, response) {
    try {
      // 如果传入的是UUID，先获取session的内部ID
      let actualSessionId = sessionId;

      if (typeof sessionId === "string" && sessionId.includes("-")) {
        const { data: session } = await supabase
          .from("chat_sessions")
          .select("id")
          .eq("session_uuid", sessionId)
          .single();

        if (session) {
          actualSessionId = session.id;
        }
      }

      const timestamp = new Date().toISOString();

      // 保存用户消息
      await supabase.from("chat_messages").insert({
        session_id: actualSessionId,
        message_type: "user",
        message_content: userMessage,
        metadata_json: {},
        timestamp: timestamp,
      });

      // 保存助手回复
      await supabase.from("chat_messages").insert({
        session_id: actualSessionId,
        message_type: "assistant",
        message_content: response.reply,
        metadata_json: {
          tokensUsed: response.tokensUsed || 0,
          apiTier: response.apiTier || "rules_engine",
          hasRecommendations: !!response.recommendations,
          handlerType: response.handlerType || "unknown",
          contextIntent: response.contextIntent || "unknown",
        },
        timestamp: timestamp,
      });
    } catch (error) {
      console.error("保存对话记录失败:", error);
    }
  }

  /**
   * 查询公司信息
   * 对应业务逻辑: getCompanyInfo(companyName)
   */
  async getCompanyInfo(companyName) {
    try {
      const { data } = await supabase
        .from("companies")
        .select("*")
        .eq("company_name", companyName)
        .single();

      return data;
    } catch (error) {
      console.error("查询公司信息失败:", error);
      return null;
    }
  }

  /**
   * 格式化公司信息回复
   * 对应业务逻辑: formatCompanyInfo(companyInfo)
   */
  formatCompanyInfo(companyInfo) {
    let reply = `🏢 ${companyInfo.company_name}\n\n`;

    if (companyInfo.company_type) {
      reply += `📊 公司类型：${companyInfo.company_type}\n`;
    }

    if (companyInfo.description) {
      reply += `📝 公司介绍：${companyInfo.description}\n`;
    }

    if (companyInfo.website) {
      reply += `🌐 官方网站：${companyInfo.website}\n`;
    }

    return reply;
  }

  // ==================== 占位符方法（待实现） ====================

  /**
   * 检查推荐触发条件 - 占位符
   * 对应业务逻辑: checkRecommendationTrigger(candidateInfo, userMessage)
   */
  async checkRecommendationTrigger(candidateInfo, userMessage) {
    // 简化版本的触发条件检查
    const hasTechDirection = !!candidateInfo?.candidate_tech_direction_raw;
    const hasCompany = !!candidateInfo?.current_company_name_raw;
    const hasLevel = !!candidateInfo?.candidate_level_raw;
    const hasSalary = !!candidateInfo?.expected_compensation_raw;

    const condition1 = hasTechDirection && hasLevel && hasCompany;
    const condition2 = hasTechDirection && hasCompany && hasSalary;
    const condition3 = hasTechDirection && hasCompany && hasSalary && hasLevel;

    const shouldRecommend = condition1 || condition2 || condition3;

    return {
      shouldRecommend,
      type: shouldRecommend ? "initial" : "info_needed",
      userPreference: this.detectUserPreference(userMessage),
      conditions: { condition1, condition2, condition3 },
    };
  }

  /**
   * 生成职位推荐 - 主推荐生成函数
   * 对应业务逻辑: generateJobRecommendations(candidateInfo, recommendationType, userPreference)
   */
  async generateJobRecommendations(
    candidateInfo,
    recommendationType,
    userPreference
  ) {
    try {
      // 1. 检查缓存
      const cachedRecommendations = await this.getCachedRecommendations(
        candidateInfo.user_id,
        candidateInfo
      );
      if (cachedRecommendations) {
        return cachedRecommendations;
      }

      // 2. 查询匹配职位
      const allJobs = await this.queryMatchingJobs(
        candidateInfo,
        recommendationType
      );

      if (!allJobs || allJobs.length === 0) {
        return {
          头部大厂: [],
          国企: [],
          中型公司: [],
          创业型公司: [],
        };
      }

      // 3. 获取已推荐的职位，用于排除
      const excludeCompanies = await this.getRecentRecommendedJobs(
        candidateInfo.user_id
      );

      // 4. 过滤已推荐的公司
      const filteredJobs = allJobs.filter(
        (job) => !excludeCompanies.includes(job.companies?.company_name)
      );

      // 5. 按4x4规则分类职位
      const categorizedJobs = this.categorizeJobsBy4x4Rule(
        filteredJobs,
        userPreference
      );

      // 6. 应用替补机制确保推荐数量
      const finalCategories = this.applyFallbackMechanism(
        categorizedJobs,
        filteredJobs
      );

      // 7. 缓存推荐结果
      await this.cacheRecommendations(
        candidateInfo.user_id,
        finalCategories,
        candidateInfo
      );

      return finalCategories;
    } catch (error) {
      console.error("职位推荐生成失败:", error);
      return {
        头部大厂: [],
        国企: [],
        中型公司: [],
        创业型公司: [],
      };
    }
  }

  /**
   * 获取缓存的推荐结果
   * 对应业务逻辑: getCachedRecommendations(userId, candidateInfo)
   */
  async getCachedRecommendations(userId, candidateInfo) {
    try {
      const cacheKey = this.generateCacheKey(userId, candidateInfo);
      const cached = this.recommendationCache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < this.cacheExpiryTime) {
        return cached.data;
      }

      // 清理过期缓存
      this.recommendationCache.delete(cacheKey);
      return null;
    } catch (error) {
      console.error("获取缓存推荐失败:", error);
      return null;
    }
  }

  /**
   * 缓存推荐结果
   * 对应业务逻辑: cacheRecommendations(userId, recommendations, candidateInfo)
   */
  async cacheRecommendations(userId, recommendations, candidateInfo) {
    try {
      const cacheKey = this.generateCacheKey(userId, candidateInfo);
      this.recommendationCache.set(cacheKey, {
        data: recommendations,
        timestamp: Date.now(),
        candidateInfo: candidateInfo,
      });
    } catch (error) {
      console.error("缓存推荐结果失败:", error);
    }
  }

  /**
   * 生成缓存键
   * 对应业务逻辑: generateCacheKey(userId, candidateInfo)
   */
  generateCacheKey(userId, candidateInfo) {
    const keyParts = [
      userId,
      candidateInfo.primary_tech_direction_id || "no_tech",
      candidateInfo.expected_compensation_min || "no_salary",
      candidateInfo.desired_location_raw || "no_location",
    ];
    return keyParts.join("_");
  }

  /**
   * 获取最近推荐的职位公司列表
   * 对应业务逻辑: getRecentRecommendedJobs(userId)
   */
  async getRecentRecommendedJobs(userId) {
    try {
      // 查询最近7天的推荐记录
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // 先获取用户的会话
      const { data: sessions } = await supabase
        .from("chat_sessions")
        .select("id")
        .eq("user_id", userId);

      if (!sessions || sessions.length === 0) {
        return [];
      }

      const sessionIds = sessions.map((s) => s.id);

      const { data } = await supabase
        .from("chat_messages")
        .select("metadata_json")
        .eq("message_type", "assistant")
        .in("session_id", sessionIds)
        .gte("timestamp", sevenDaysAgo.toISOString())
        .not("metadata_json->hasRecommendations", "is", null);

      const recommendedCompanies = new Set();

      if (data) {
        for (const message of data) {
          if (message.metadata_json?.recommendations) {
            // 从推荐中提取公司名称
            for (const category of Object.values(
              message.metadata_json.recommendations
            )) {
              if (Array.isArray(category)) {
                for (const job of category) {
                  if (job.companies?.company_name) {
                    recommendedCompanies.add(job.companies.company_name);
                  }
                }
              }
            }
          }
        }
      }

      return Array.from(recommendedCompanies);
    } catch (error) {
      console.error("获取推荐历史失败:", error);
      return [];
    }
  }

  /**
   * 生成推荐文本 - 主推荐文本生成函数
   * 对应业务逻辑: generateRecommendationText(candidateInfo, categorizedJobs, recommendationType, userPreference)
   */
  async generateRecommendationText(
    candidateInfo,
    recommendations,
    recommendationType,
    userPreference
  ) {
    try {
      let recommendationText = "";

      // 生成开头
      const greeting = this.generateRecommendationGreeting(
        candidateInfo,
        recommendationType
      );
      recommendationText += greeting + "\n\n";

      // 生成各分类的推荐
      for (const [categoryName, jobs] of Object.entries(recommendations)) {
        if (jobs && jobs.length > 0) {
          recommendationText += `**${categoryName}：**\n`;

          for (let i = 0; i < Math.min(jobs.length, 4); i++) {
            const job = jobs[i];
            const jobText = await this.formatJobRecommendation(
              job,
              candidateInfo,
              i + 1
            );
            recommendationText += jobText + "\n";
          }

          recommendationText += "\n";
        }
      }

      // 生成结尾
      const closing = this.generateRecommendationClosing(userPreference);
      recommendationText += closing;

      return recommendationText;
    } catch (error) {
      console.error("推荐文本生成失败:", error);
      return "基于您的背景，我为您推荐了一些合适的职位机会，请查看详细信息。";
    }
  }

  /**
   * 智能技术方向歧义检测 - 动态数据库查询
   * 对应业务逻辑: detectTechAmbiguityIntelligently(userInput)
   */
  async detectTechAmbiguityIntelligently(userInput) {
    try {
      if (!userInput) {
        return {
          originalTech: userInput,
          options: [],
          needsClarification: false,
        };
      }

      // 1. 标准化输入
      const normalizedInput = this.normalizeInput(userInput);

      // 2. 扩展自然语言表达
      const expandedKeywords = this.expandNaturalExpressions(normalizedInput);

      // 3. 查找所有可能的匹配
      const exactMatches = await this.findExactTechMatches(normalizedInput);
      const fuzzyMatches = await this.findFuzzyTechMatches(normalizedInput);
      const keywordMatches = await this.findKeywordTechMatches(normalizedInput);

      // 4. 合并匹配结果
      const allMatches = this.mergeTechMatches(
        exactMatches,
        fuzzyMatches,
        keywordMatches
      );

      // 5. 分析歧义
      const ambiguityResult = await this.analyzeTechAmbiguity(
        allMatches,
        userInput
      );

      return ambiguityResult;
    } catch (error) {
      console.error("智能歧义检测失败:", error);
      return {
        originalTech: userInput,
        options: [],
        needsClarification: false,
      };
    }
  }

  /**
   * 生成标准歧义澄清问题
   * 对应业务逻辑: generateAmbiguityQuestion(originalTech, ambiguityOptions)
   */
  generateAmbiguityQuestion(originalTech, ambiguityOptions) {
    if (!ambiguityOptions || ambiguityOptions.length === 0) {
      return `您说的"${originalTech}"我没有完全理解，能否提供更多信息？`;
    }

    let question = `您说的"${originalTech}"可能指以下几个方向中的哪一个？\n\n`;

    for (const option of ambiguityOptions) {
      question += `${option.index}. ${option.parentName}`;
      if (option.description && option.description !== option.parentName) {
        question += ` - ${option.description}`;
      }
      question += "\n";
    }

    question += "\n请告诉我具体是哪个方向？";

    return question;
  }

  /**
   * 获取技术方向ID - 智能映射技术方向到数据库ID
   * 对应业务逻辑: getTechDirectionId(techName, candidateInfo)
   */
  async getTechDirectionId(techName, candidateInfo) {
    try {
      if (!techName) return null;

      // 1. 标准化输入
      const normalizedTech = this.normalizeInput(techName);

      // 2. 检查硬编码映射
      const hardcodedId = this.techHierarchyMap[normalizedTech];
      if (hardcodedId) {
        return hardcodedId;
      }

      // 3. 语义映射检查
      const semanticMapped = this.semanticMappings[normalizedTech];
      if (semanticMapped && this.techHierarchyMap[semanticMapped]) {
        return this.techHierarchyMap[semanticMapped];
      }

      // 4. 数据库精确匹配
      const { data: exactMatch } = await supabase
        .from("tech_tree")
        .select("id")
        .eq("tech_name", normalizedTech)
        .single();

      if (exactMatch) {
        return exactMatch.id;
      }

      // 5. 模糊匹配
      const { data: fuzzyMatches } = await supabase
        .from("tech_tree")
        .select("id, tech_name")
        .ilike("tech_name", `%${normalizedTech}%`)
        .limit(5);

      if (fuzzyMatches && fuzzyMatches.length > 0) {
        // 选择最相似的匹配
        const bestMatch = fuzzyMatches.find((match) =>
          match.tech_name.toLowerCase().includes(normalizedTech.toLowerCase())
        );
        if (bestMatch) {
          return bestMatch.id;
        }
      }

      // 6. 如果有候选人信息，尝试上下文推断
      if (candidateInfo && candidateInfo.primary_tech_direction_id) {
        // 查找相关的子技术方向
        const { data: relatedTechs } = await supabase
          .from("tech_tree")
          .select("id, tech_name")
          .eq("parent_id", candidateInfo.primary_tech_direction_id)
          .ilike("tech_name", `%${normalizedTech}%`);

        if (relatedTechs && relatedTechs.length > 0) {
          return relatedTechs[0].id;
        }
      }

      return null;
    } catch (error) {
      console.error("技术方向映射失败:", error);
      return null;
    }
  }

  /**
   * 提取第二次推荐偏好
   * 对应业务逻辑: extractSecondRecommendationPreference(userMessage)
   */
  extractSecondRecommendationPreference(userMessage) {
    const preferences = {
      大厂: "bigTech",
      头部: "bigTech",
      国企: "stateOwned",
      央企: "stateOwned",
      中型: "medium",
      创业: "startup",
      初创: "startup",
    };

    for (const [keyword, preference] of Object.entries(preferences)) {
      if (userMessage.includes(keyword)) {
        return preference;
      }
    }

    return null;
  }

  /**
   * 生成第二次推荐
   * 对应业务逻辑: generateSecondRecommendations(candidateInfo, preference, excludeCompanies)
   */
  async generateSecondRecommendations(
    candidateInfo,
    preference,
    excludeCompanies
  ) {
    try {
      // 查询职位，排除已推荐的公司
      const allJobs = await this.queryMatchingJobs(
        candidateInfo,
        "second_recommendation"
      );

      if (!allJobs || allJobs.length === 0) {
        return {
          头部大厂: [],
          国企: [],
          中型公司: [],
          创业型公司: [],
        };
      }

      // 过滤已推荐的公司
      const filteredJobs = allJobs.filter(
        (job) => !excludeCompanies.includes(job.companies?.company_name)
      );

      // 按4x4规则分类
      const categorizedJobs = this.categorizeJobsBy4x4Rule(
        filteredJobs,
        preference
      );

      return categorizedJobs;
    } catch (error) {
      console.error("生成第二次推荐失败:", error);
      return {
        头部大厂: [],
        国企: [],
        中型公司: [],
        创业型公司: [],
      };
    }
  }

  /**
   * 生成第三方推荐文本
   * 对应业务逻辑: generateThirdPartyRecommendationText(thirdPartyProfile, categorizedJobs, targetPerson, relationship)
   */
  async generateThirdPartyRecommendationText(
    thirdPartyProfile,
    categorizedJobs,
    targetPerson,
    relationship
  ) {
    try {
      let recommendationText = `基于您${relationship}的背景，我为他/她推荐了以下职位：\n\n`;

      for (const [categoryName, jobs] of Object.entries(categorizedJobs)) {
        if (jobs && jobs.length > 0) {
          recommendationText += `**${categoryName}：**\n`;

          for (let i = 0; i < Math.min(jobs.length, 4); i++) {
            const job = jobs[i];
            recommendationText += `${i + 1}. ${job.companies?.company_name} - ${
              job.job_title
            }\n`;
            recommendationText += `   薪资：${job.salary_min || "面议"}-${
              job.salary_max || "面议"
            }万\n`;
            recommendationText += `   地点：${job.job_location || "待定"}\n\n`;
          }
        }
      }

      recommendationText += `这些职位都比较适合您${relationship}的背景。如需了解详细JD，请告诉我具体想了解哪个职位。`;

      return recommendationText;
    } catch (error) {
      console.error("生成第三方推荐文本失败:", error);
      return `为您的${relationship}推荐了一些合适的职位，请查看详细信息。`;
    }
  }

  /**
   * 保存第三方歧义状态
   * 对应业务逻辑: saveThirdPartyAmbiguityState(userId, ambiguityData)
   */
  async saveThirdPartyAmbiguityState(userId, ambiguityData) {
    try {
      // 将歧义状态保存到内存缓存
      const cacheKey = `third_party_ambiguity_${userId}`;
      this.ambiguityStateCache.set(cacheKey, {
        ...ambiguityData,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("保存第三方歧义状态失败:", error);
    }
  }

  /**
   * 识别目标职位
   * 对应业务逻辑: identifyTargetJob(userMessage, recentJobs)
   */
  async identifyTargetJob(userMessage, recentJobs) {
    try {
      // 1. 检查数字序号
      const numberMatch = userMessage.match(/第?(\d+)[个]?/);
      if (numberMatch) {
        const index = parseInt(numberMatch[1]) - 1;
        if (index >= 0 && index < recentJobs.length) {
          return recentJobs[index];
        }
      }

      // 2. 检查公司名称
      for (const job of recentJobs) {
        if (userMessage.includes(job.companies?.company_name)) {
          return job;
        }
      }

      // 3. 如果只有一个推荐，默认返回
      if (recentJobs.length === 1) {
        return recentJobs[0];
      }

      return null;
    } catch (error) {
      console.error("识别目标职位失败:", error);
      return null;
    }
  }

  /**
   * 获取详细职位信息
   * 对应业务逻辑: getDetailedJobInfo(jobId)
   */
  async getDetailedJobInfo(jobId) {
    try {
      const { data } = await supabase
        .from("job_listings")
        .select(
          `
          *,
          companies (
            company_name,
            company_type,
            company_size,
            company_description
          ),
          tech_tree (
            tech_name,
            parent_id
          )
        `
        )
        .eq("id", jobId)
        .single();

      return data;
    } catch (error) {
      console.error("获取详细职位信息失败:", error);
      return null;
    }
  }

  /**
   * 格式化详细JD
   * 对应业务逻辑: formatDetailedJD(jobData)
   */
  formatDetailedJD(jobData) {
    try {
      let jdText = `**${jobData.companies?.company_name} - ${jobData.job_title}**\n\n`;

      jdText += `**基本信息：**\n`;
      jdText += `• 薪资范围：${jobData.salary_min || "面议"}-${
        jobData.salary_max || "面议"
      }万\n`;
      jdText += `• 工作地点：${jobData.job_location || "待定"}\n`;
      jdText += `• 职级要求：${jobData.job_standard_level_min || "不限"}-${
        jobData.job_standard_level_max || "不限"
      }\n`;
      jdText += `• 经验要求：${jobData.experience_min || "不限"}-${
        jobData.experience_max || "不限"
      }年\n\n`;

      if (jobData.job_description) {
        jdText += `**职位描述：**\n${jobData.job_description}\n\n`;
      }

      if (jobData.job_requirements) {
        jdText += `**任职要求：**\n${jobData.job_requirements}\n\n`;
      }

      if (jobData.companies?.company_description) {
        jdText += `**公司介绍：**\n${jobData.companies.company_description}\n\n`;
      }

      jdText += `如果您对这个职位感兴趣，我可以为您提供更多信息或安排进一步沟通。`;

      return jdText;
    } catch (error) {
      console.error("格式化详细JD失败:", error);
      return "职位详情格式化失败，请稍后再试。";
    }
  }

  /**
   * 生成对话回复 - AI驱动的通用对话生成
   * 对应业务逻辑: generateConversationReply(userMessage, candidateInfo)
   */
  async generateConversationReply(userMessage, candidateInfo) {
    try {
      const prompt = `
作为专业的AI招聘顾问Katrina，请回复用户的消息。

用户消息: ${userMessage}
候选人背景: ${JSON.stringify(candidateInfo)}

要求：
1. 保持专业、友好的语调
2. 如果用户询问职位相关问题，引导提供更多背景信息
3. 回复简洁明了，不超过100字
4. 体现AI招聘顾问的专业性

请直接返回回复内容，不要包含其他格式。
`;

      const response = await callDeepSeek(prompt, {
        temperature: 0.7,
        maxTokens: 150,
      });

      return {
        reply: response.content || "感谢您的消息，我会尽力为您提供帮助。",
        tokensUsed: response.tokensUsed || 100,
        apiTier: "deepseek_medium",
      };
    } catch (error) {
      console.error("生成对话回复失败:", error);
      return {
        reply: "感谢您的消息，我正在为您处理...",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 详细JD询问检测 - AI驱动的智能检测
   * 对应业务逻辑: isDetailedJDInquiry(userMessage, conversationHistory)
   */
  async isDetailedJDInquiry(userMessage, conversationHistory) {
    try {
      // 1. 基于关键词的快速检测
      const jdKeywords = [
        "详细",
        "JD",
        "职位描述",
        "工作内容",
        "具体要求",
        "岗位职责",
        "任职要求",
        "更多信息",
        "详情",
      ];

      const hasJDKeywords = jdKeywords.some((keyword) =>
        userMessage.includes(keyword)
      );

      // 2. 检查对话历史中是否有推荐
      const hasRecentRecommendations = conversationHistory.some(
        (msg) =>
          msg.message_type === "assistant" &&
          msg.metadata_json?.hasRecommendations === true
      );

      // 3. 如果有关键词且有推荐历史，使用AI进一步分析
      if (hasJDKeywords && hasRecentRecommendations) {
        const prompt = `
判断用户是否在询问详细的职位描述(JD)。

用户消息: "${userMessage}"
对话上下文: 之前已经推荐了职位

请返回JSON格式：
{
  "isDetailedJDInquiry": true/false,
  "confidence": 0.0-1.0
}
`;

        const response = await callDeepSeek(prompt, {
          temperature: 0.1,
          maxTokens: 100,
        });
        const result = JSON.parse(response.content);

        return result.isDetailedJDInquiry && result.confidence > 0.7;
      }

      return hasJDKeywords && hasRecentRecommendations;
    } catch (error) {
      console.error("详细JD询问检测失败:", error);
      // 回退到关键词检测
      const jdKeywords = ["详细", "JD", "职位描述", "工作内容"];
      return jdKeywords.some((keyword) => userMessage.includes(keyword));
    }
  }

  /**
   * 第二次推荐询问检测 - AI驱动的智能检测
   * 对应业务逻辑: isSecondRecommendationInquiry(userMessage, conversationHistory)
   */
  async isSecondRecommendationInquiry(userMessage, conversationHistory) {
    try {
      // 1. 检查是否有推荐历史
      const hasRecommendations = conversationHistory.some(
        (msg) =>
          msg.message_type === "assistant" &&
          msg.metadata_json?.hasRecommendations === true
      );

      if (!hasRecommendations) {
        return false;
      }

      // 2. 基于关键词的快速检测
      const secondRecKeywords = [
        "还有",
        "其他",
        "更多",
        "别的",
        "再推荐",
        "再来",
        "换一批",
        "不满意",
        "不合适",
        "看看别的",
      ];

      const hasSecondRecKeywords = secondRecKeywords.some((keyword) =>
        userMessage.includes(keyword)
      );

      // 3. 使用AI进行更精确的判断
      if (hasSecondRecKeywords) {
        const prompt = `
判断用户是否在请求第二次推荐或更多推荐。

用户消息: "${userMessage}"
对话上下文: 之前已经推荐了职位

请返回JSON格式：
{
  "isSecondRecommendation": true/false,
  "preferenceType": "bigTech/stateOwned/medium/startup/null",
  "confidence": 0.0-1.0
}
`;

        const response = await callDeepSeek(prompt, {
          temperature: 0.1,
          maxTokens: 100,
        });
        const result = JSON.parse(response.content);

        return result.isSecondRecommendation && result.confidence > 0.7;
      }

      return false;
    } catch (error) {
      console.error("第二次推荐询问检测失败:", error);
      // 回退到关键词检测
      const keywords = ["还有", "其他", "更多", "再推荐"];
      return keywords.some((keyword) => userMessage.includes(keyword));
    }
  }

  /**
   * 处理详细JD询问 - 占位符
   * 对应业务逻辑: handleDetailedJDInquiry(userMessage, candidateInfo, userId)
   */
  async handleDetailedJDInquiry(userMessage, candidateInfo, userId) {
    try {
      // 1. 获取最近推荐的职位
      const recentJobs = await this.getRecentRecommendedJobs(userId);

      if (!recentJobs || recentJobs.length === 0) {
        return {
          reply:
            "抱歉，我没有找到最近为您推荐的职位。请先让我为您推荐一些合适的职位。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 2. 识别用户询问的目标职位
      const targetJob = await this.identifyTargetJob(userMessage, recentJobs);

      if (!targetJob) {
        return {
          reply:
            "请告诉我您想了解哪个公司的职位详情？比如：第1个、第2个，或者直接说公司名称。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 3. 获取详细职位信息
      const detailedJob = await this.getDetailedJobInfo(targetJob.id);

      if (!detailedJob) {
        return {
          reply: "抱歉，暂时无法获取该职位的详细信息，请稍后再试。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 4. 格式化详细JD
      const formattedJD = this.formatDetailedJD(detailedJob);

      return {
        reply: formattedJD,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          jobId: targetJob.id,
          companyName: detailedJob.companies?.company_name,
          hasDetailedJD: true,
        },
      };
    } catch (error) {
      console.error("处理详细JD询问失败:", error);
      return {
        reply: "抱歉，获取职位详情时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理第二次推荐询问 - 占位符
   * 对应业务逻辑: handleSecondRecommendationInquiry(userMessage, candidateInfo, userId)
   */
  async handleSecondRecommendationInquiry(userMessage, candidateInfo, userId) {
    try {
      // 1. 提取用户的偏好类型
      const preference =
        this.extractSecondRecommendationPreference(userMessage);

      // 2. 获取已推荐的公司列表
      const excludeCompanies = await this.getRecentRecommendedJobs(userId);

      // 3. 生成第二次推荐
      const secondRecommendations = await this.generateSecondRecommendations(
        candidateInfo,
        preference,
        excludeCompanies
      );

      if (
        !secondRecommendations ||
        Object.values(secondRecommendations).every((jobs) => jobs.length === 0)
      ) {
        return {
          reply:
            "抱歉，基于您的条件，暂时没有更多合适的职位推荐。建议您可以适当调整期望条件，我会为您寻找更多机会。",
          tokensUsed: 0,
          apiTier: "database_query",
        };
      }

      // 4. 生成推荐文本
      const recommendationText = await this.generateRecommendationText(
        candidateInfo,
        secondRecommendations,
        "second_recommendation",
        preference
      );

      return {
        reply: recommendationText,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          hasRecommendations: true,
          recommendations: secondRecommendations,
          recommendationType: "second_recommendation",
          userPreference: preference,
        },
      };
    } catch (error) {
      console.error("处理第二次推荐询问失败:", error);
      return {
        reply: "抱歉，生成更多推荐时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 生成第三方推荐 - 占位符
   * 对应业务逻辑: generateThirdPartyRecommendations(thirdPartyProfile, targetPerson, relationship)
   */
  async generateThirdPartyRecommendations(
    thirdPartyProfile,
    targetPerson,
    relationship
  ) {
    try {
      // 1. 验证第三方档案信息完整性
      if (!thirdPartyProfile.primary_tech_direction_id) {
        return {
          reply: `要为您的${relationship}推荐合适的职位，我需要了解他/她的技术方向。请告诉我他/她主要从事什么技术领域？`,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 2. 查询匹配的职位
      const matchingJobs = await this.queryMatchingJobs(
        thirdPartyProfile,
        "third_party"
      );

      if (!matchingJobs || matchingJobs.length === 0) {
        return {
          reply: `抱歉，暂时没有找到适合您${relationship}的职位。建议提供更多背景信息，比如期望薪资、工作地点等。`,
          tokensUsed: 0,
          apiTier: "database_query",
        };
      }

      // 3. 按4x4规则分类
      const categorizedJobs = this.categorizeJobsBy4x4Rule(matchingJobs, null);

      // 4. 生成第三方推荐文本
      const recommendationText =
        await this.generateThirdPartyRecommendationText(
          thirdPartyProfile,
          categorizedJobs,
          targetPerson,
          relationship
        );

      return {
        reply: recommendationText,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          hasRecommendations: true,
          recommendations: categorizedJobs,
          recommendationType: "third_party",
          targetPerson: targetPerson,
          relationship: relationship,
        },
      };
    } catch (error) {
      console.error("生成第三方推荐失败:", error);
      return {
        reply: `抱歉，为您的${relationship}推荐职位时遇到问题，请稍后再试。`,
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 生成第三方歧义响应 - 占位符
   * 对应业务逻辑: generateThirdPartyAmbiguityResponse(userMessage, ambiguityResult, targetPerson, relationship, userId)
   */
  async generateThirdPartyAmbiguityResponse(
    userMessage,
    ambiguityResult,
    targetPerson,
    relationship,
    userId
  ) {
    try {
      if (
        !ambiguityResult.needsClarification ||
        !ambiguityResult.options ||
        ambiguityResult.options.length === 0
      ) {
        return {
          reply: `关于您${relationship}的技术方向，我需要更多信息。请告诉我他/她具体从事什么技术领域？`,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 生成第三方歧义澄清问题
      let question = `关于您${relationship}的"${ambiguityResult.originalTech}"技术方向，可能指以下几个方向中的哪一个？\n\n`;

      for (const option of ambiguityResult.options) {
        question += `${option.index}. ${option.parentName}`;
        if (option.description && option.description !== option.parentName) {
          question += ` - ${option.description}`;
        }
        question += "\n";
      }

      question += `\n请告诉我您的${relationship}具体是哪个方向？这样我可以为他/她推荐更精准的职位。`;

      // 保存歧义状态
      await this.saveThirdPartyAmbiguityState(userId, {
        targetPerson,
        relationship,
        ambiguityResult,
        originalMessage: userMessage,
      });

      return {
        reply: question,
        tokensUsed: 0,
        apiTier: "rules_engine",
        metadata: {
          hasAmbiguity: true,
          ambiguityType: "third_party_tech_direction",
          targetPerson: targetPerson,
          relationship: relationship,
          options: ambiguityResult.options,
        },
      };
    } catch (error) {
      console.error("生成第三方歧义响应失败:", error);
      return {
        reply: `关于您${relationship}的技术方向，我需要更多信息。请告诉我他/她具体从事什么技术领域？`,
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  // ==================== 技术方向智能映射系统 ====================

  /**
   * 智能技术方向映射主函数 - 支持歧义检测和多种匹配策略
   * 对应业务逻辑: getTechDirectionId(techName, candidateInfo)
   */
  async getTechDirectionId(techName, candidateInfo) {
    try {
      if (!techName) return null;

      // 1. 直接精确匹配
      let techId = await this.findDirectTechMatch(techName);
      if (techId) return techId;

      // 2. 智能变体匹配
      techId = await this.findIntelligentVariantMatch(techName);
      if (techId) return techId;

      // 3. 模糊匹配
      techId = await this.findFuzzyTechMatch(techName);
      if (techId) return techId;

      // 4. 关键词匹配
      techId = await this.findKeywordTechMatch(techName);
      if (techId) return techId;

      // 5. 兼容性硬编码匹配
      techId = await this.findLegacyTechMatch(techName);
      if (techId) return techId;

      return null;
    } catch (error) {
      console.error("技术方向映射失败:", error);
      return null;
    }
  }

  /**
   * 直接精确匹配技术名称
   * 对应业务逻辑: findDirectTechMatch(techName)
   */
  async findDirectTechMatch(techName) {
    try {
      const { data } = await supabase
        .from("tech_tree")
        .select("id")
        .eq("tech_name", techName)
        .single();

      return data?.id || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 智能变体匹配 - 处理用户输入与标准名称的差异
   * 对应业务逻辑: findIntelligentVariantMatch(techName)
   */
  async findIntelligentVariantMatch(techName) {
    try {
      // 获取所有技术方向
      const { data: techDirections } = await supabase
        .from("tech_tree")
        .select("id, tech_name");

      if (!techDirections) return null;

      const normalizedInput = this.normalizeInput(techName);
      let bestMatch = null;
      let bestScore = 0;

      for (const tech of techDirections) {
        const similarity = this.calculateTechSimilarity(
          normalizedInput,
          tech.tech_name
        );
        if (similarity > bestScore && similarity >= 0.75) {
          bestScore = similarity;
          bestMatch = tech;
        }
      }

      return bestMatch?.id || null;
    } catch (error) {
      console.error("智能变体匹配失败:", error);
      return null;
    }
  }

  /**
   * 模糊匹配技术名称 - 支持部分匹配
   * 对应业务逻辑: findFuzzyTechMatch(techName)
   */
  async findFuzzyTechMatch(techName) {
    try {
      const normalizedInput = this.normalizeInput(techName);

      const { data: techDirections } = await supabase
        .from("tech_tree")
        .select("id, tech_name")
        .ilike("tech_name", `%${normalizedInput}%`);

      if (!techDirections || techDirections.length === 0) return null;

      // 如果只有一个匹配，直接返回
      if (techDirections.length === 1) {
        return techDirections[0].id;
      }

      // 多个匹配时，选择最相似的
      let bestMatch = null;
      let bestScore = 0;

      for (const tech of techDirections) {
        const similarity = this.calculateTechSimilarity(
          normalizedInput,
          tech.tech_name
        );
        if (similarity > bestScore) {
          bestScore = similarity;
          bestMatch = tech;
        }
      }

      return bestMatch?.id || null;
    } catch (error) {
      console.error("模糊匹配失败:", error);
      return null;
    }
  }

  /**
   * 关键词字段匹配
   * 对应业务逻辑: findKeywordTechMatch(techName)
   */
  async findKeywordTechMatch(techName) {
    try {
      const normalizedInput = this.normalizeInput(techName);

      const { data: techDirections } = await supabase
        .from("tech_tree")
        .select("id, tech_name, keywords")
        .not("keywords", "is", null);

      if (!techDirections) return null;

      for (const tech of techDirections) {
        if (tech.keywords && tech.keywords.includes(normalizedInput)) {
          return tech.id;
        }
      }

      return null;
    } catch (error) {
      console.error("关键词匹配失败:", error);
      return null;
    }
  }

  /**
   * 兼容性硬编码映射查找
   * 对应业务逻辑: findLegacyTechMatch(techName)
   */
  async findLegacyTechMatch(techName) {
    // 硬编码的技术方向映射表（简化版本）
    const legacyMappings = {
      nlp: "自然语言处理",
      cv: "计算机视觉",
      推荐: "推荐算法",
      搜索: "搜索算法",
      广告: "广告算法",
      大模型: "大模型（LLM）算法",
      llm: "大模型（LLM）算法",
    };

    const normalizedInput = this.normalizeInput(techName);
    const mappedName = legacyMappings[normalizedInput];

    if (mappedName) {
      return await this.findDirectTechMatch(mappedName);
    }

    return null;
  }

  /**
   * 标准化用户输入 - 移除噪声词和停用词
   * 对应业务逻辑: normalizeInput(input)
   */
  normalizeInput(input) {
    if (!input) return "";

    const noiseWords = ["算法", "方向", "领域", "技术", "专业", "做", "从事"];
    let normalized = input.toLowerCase().trim();

    // 移除噪声词
    for (const noise of noiseWords) {
      normalized = normalized.replace(new RegExp(noise, "g"), "");
    }

    return normalized.trim();
  }

  /**
   * 技术方向相似度计算 - 支持复合技术方向处理
   * 对应业务逻辑: calculateTechSimilarity(userInput, techDirection)
   */
  calculateTechSimilarity(userInput, techDirection) {
    if (!userInput || !techDirection) return 0;

    const input = userInput.toLowerCase();
    const tech = techDirection.toLowerCase();

    // 精确匹配
    if (input === tech) return 1.0;

    // 包含匹配
    if (tech.includes(input) || input.includes(tech)) {
      const longer = Math.max(input.length, tech.length);
      const shorter = Math.min(input.length, tech.length);
      return 0.7 + (shorter / longer) * 0.15;
    }

    // 语义相似度
    const semanticScore = this.calculateSemanticSimilarity(input, tech);
    if (semanticScore > 0) return semanticScore;

    // 字符相似度
    const charScore = this.calculateCharSimilarity(input, tech);
    if (charScore > 0.6) return charScore * 0.5;

    return 0;
  }

  /**
   * 语义相似度计算 - 基于语义关系网络
   * 对应业务逻辑: calculateSemanticSimilarity(userInput, techName)
   */
  calculateSemanticSimilarity(userInput, techName) {
    const semanticGroups = this.buildSemanticGroups();

    for (const [groupName, keywords] of Object.entries(semanticGroups)) {
      const inputMatch = keywords.some((keyword) =>
        userInput.includes(keyword)
      );
      const techMatch = keywords.some((keyword) => techName.includes(keyword));

      if (inputMatch && techMatch) {
        return 0.75;
      }
    }

    return 0;
  }

  /**
   * 动态构建技术领域语义关系网络
   * 对应业务逻辑: buildSemanticGroups()
   */
  buildSemanticGroups() {
    return {
      computer_vision: ["cv", "计算机视觉", "computer vision", "视觉", "图像"],
      natural_language: [
        "nlp",
        "自然语言处理",
        "natural language",
        "语言",
        "文本",
      ],
      large_language_model: ["llm", "大模型", "large language", "语言模型"],
      recommendation: ["推荐", "recommendation", "个性化", "召回"],
      search: ["搜索", "search", "检索", "retrieval", "查询"],
      advertising: ["广告", "advertising", "ad", "竞价"],
      machine_learning: ["机器学习", "machine learning", "ml", "深度学习"],
    };
  }

  /**
   * 字符相似度计算 - 使用编辑距离算法
   * 对应业务逻辑: calculateCharSimilarity(str1, str2)
   */
  calculateCharSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;

    const len1 = str1.length;
    const len2 = str2.length;
    const maxLen = Math.max(len1, len2);

    if (maxLen === 0) return 1;

    // 简化的编辑距离计算
    let distance = 0;
    const minLen = Math.min(len1, len2);

    for (let i = 0; i < minLen; i++) {
      if (str1[i] !== str2[i]) {
        distance++;
      }
    }

    distance += Math.abs(len1 - len2);

    return Math.max(0, 1 - distance / maxLen);
  }

  // ==================== 智能歧义检测和处理系统 ====================

  /**
   * 智能技术方向歧义检测主函数 - 动态数据库查询
   * 对应业务逻辑: detectTechAmbiguityIntelligently(userInput)
   */
  async detectTechAmbiguityIntelligently(userInput) {
    try {
      if (!userInput) {
        return {
          originalTech: userInput,
          options: [],
          needsClarification: false,
        };
      }

      // 1. 标准化输入
      const normalizedInput = this.normalizeInput(userInput);

      // 2. 扩展自然语言表达
      const expandedKeywords = this.expandNaturalExpressions(normalizedInput);

      // 3. 查找所有可能的匹配
      const exactMatches = await this.findExactTechMatches(normalizedInput);
      const fuzzyMatches = await this.findFuzzyTechMatches(normalizedInput);
      const keywordMatches = await this.findKeywordTechMatches(normalizedInput);

      // 4. 合并匹配结果
      const allMatches = this.mergeTechMatches(
        exactMatches,
        fuzzyMatches,
        keywordMatches
      );

      // 5. 分析歧义
      const ambiguityResult = await this.analyzeTechAmbiguity(
        allMatches,
        userInput
      );

      return ambiguityResult;
    } catch (error) {
      console.error("智能歧义检测失败:", error);
      return {
        originalTech: userInput,
        options: [],
        needsClarification: false,
      };
    }
  }

  /**
   * 自然语言扩展 - 将用户表达转换为技术关键词
   * 对应业务逻辑: expandNaturalExpressions(userInput)
   */
  expandNaturalExpressions(userInput) {
    const naturalExpressionMap = {
      我做推荐的: ["推荐", "推荐算法", "推荐系统"],
      做推荐的: ["推荐", "推荐算法", "推荐系统"],
      我做广告的: ["广告", "广告算法", "广告投放"],
      我做算法的: ["算法", "机器学习", "深度学习"],
      我是做图像的: ["图像", "图像处理", "计算机视觉", "cv"],
      做图像的: ["图像", "图像处理", "计算机视觉", "cv"],
      我是做视频的: ["视频", "视频处理", "视频分析"],
      我做检索的: ["检索", "搜索", "rag", "检索增强生成"],
      我做生成的: ["生成", "aigc", "生成算法", "内容生成"],
    };

    const expandedKeywords = [userInput];

    for (const [expression, keywords] of Object.entries(naturalExpressionMap)) {
      if (userInput.includes(expression)) {
        expandedKeywords.push(...keywords);
      }
    }

    return expandedKeywords;
  }

  /**
   * 精确匹配技术名称和分类
   * 对应业务逻辑: findExactTechMatches(normalizedInput)
   */
  async findExactTechMatches(normalizedInput) {
    try {
      const { data } = await supabase
        .from("tech_tree")
        .select("id, tech_name, level, parent_tech_id")
        .eq("tech_name", normalizedInput);

      return data || [];
    } catch (error) {
      console.error("精确匹配失败:", error);
      return [];
    }
  }

  /**
   * 模糊匹配技术名称 - 支持部分匹配
   * 对应业务逻辑: findFuzzyTechMatches(normalizedInput)
   */
  async findFuzzyTechMatches(normalizedInput) {
    try {
      const { data } = await supabase
        .from("tech_tree")
        .select("id, tech_name, level, parent_tech_id")
        .ilike("tech_name", `%${normalizedInput}%`)
        .limit(10);

      return data || [];
    } catch (error) {
      console.error("模糊匹配失败:", error);
      return [];
    }
  }

  /**
   * 关键词字段匹配
   * 对应业务逻辑: findKeywordTechMatches(normalizedInput)
   */
  async findKeywordTechMatches(normalizedInput) {
    try {
      const { data } = await supabase
        .from("tech_tree")
        .select("id, tech_name, level, parent_tech_id, keywords")
        .not("keywords", "is", null)
        .limit(10);

      if (!data) return [];

      return data.filter(
        (tech) => tech.keywords && tech.keywords.includes(normalizedInput)
      );
    } catch (error) {
      console.error("关键词匹配失败:", error);
      return [];
    }
  }

  /**
   * 合并多个匹配结果并去重
   * 对应业务逻辑: mergeTechMatches(...matchArrays)
   */
  mergeTechMatches(...matchArrays) {
    const allMatches = [];
    const seenIds = new Set();

    for (const matches of matchArrays) {
      for (const match of matches) {
        if (!seenIds.has(match.id)) {
          seenIds.add(match.id);
          allMatches.push(match);
        }
      }
    }

    return allMatches;
  }

  /**
   * 分析技术方向歧义程度和类型
   * 对应业务逻辑: analyzeTechAmbiguity(matches, originalInput)
   */
  async analyzeTechAmbiguity(matches, originalInput) {
    if (!matches || matches.length <= 1) {
      return {
        originalTech: originalInput,
        options: [],
        needsClarification: false,
      };
    }

    // 按父级分组
    const groupedMatches = await this.groupMatchesByParent(matches);

    // 如果只有一个父级分组，不需要歧义澄清
    if (Object.keys(groupedMatches).length <= 1) {
      return {
        originalTech: originalInput,
        options: [],
        needsClarification: false,
      };
    }

    // 生成歧义选项
    const ambiguityOptions = this.generateAmbiguityOptions(groupedMatches);

    return {
      originalTech: originalInput,
      options: ambiguityOptions,
      needsClarification: true,
    };
  }

  /**
   * 按一级父分类对匹配结果分组
   * 对应业务逻辑: groupMatchesByParent(matches)
   */
  async groupMatchesByParent(matches) {
    const grouped = {};

    for (const match of matches) {
      const level1Parent = await this.findLevel1Parent(match);
      const parentKey = level1Parent ? level1Parent.tech_name : "unknown";

      if (!grouped[parentKey]) {
        grouped[parentKey] = [];
      }
      grouped[parentKey].push(match);
    }

    return grouped;
  }

  /**
   * 查找技术方向的一级父分类
   * 对应业务逻辑: findLevel1Parent(techRecord)
   */
  async findLevel1Parent(techRecord) {
    try {
      if (techRecord.level === 1) {
        return techRecord;
      }

      if (!techRecord.parent_tech_id) {
        return null;
      }

      const { data: parent } = await supabase
        .from("tech_tree")
        .select("id, tech_name, level, parent_tech_id")
        .eq("id", techRecord.parent_tech_id)
        .single();

      if (!parent) return null;

      if (parent.level === 1) {
        return parent;
      }

      // 递归查找
      return await this.findLevel1Parent(parent);
    } catch (error) {
      console.error("查找父分类失败:", error);
      return null;
    }
  }

  /**
   * 生成歧义澄清选项列表
   * 对应业务逻辑: generateAmbiguityOptions(groupedMatches)
   */
  generateAmbiguityOptions(groupedMatches) {
    const options = [];
    let index = 1;

    for (const [parentName, matches] of Object.entries(groupedMatches)) {
      if (matches.length > 0) {
        const representative = matches[0];
        options.push({
          index: index++,
          techId: representative.id,
          parentName: parentName,
          description: representative.tech_name,
          level1Name: parentName,
        });
      }
    }

    return options;
  }

  /**
   * 生成标准歧义澄清问题
   * 对应业务逻辑: generateAmbiguityQuestion(originalTech, ambiguityOptions)
   */
  generateAmbiguityQuestion(originalTech, ambiguityOptions) {
    if (!ambiguityOptions || ambiguityOptions.length === 0) {
      return `您说的"${originalTech}"我没有完全理解，能否提供更多信息？`;
    }

    let question = `您说的"${originalTech}"可能指以下几个方向中的哪一个？\n\n`;

    for (const option of ambiguityOptions) {
      question += `${option.index}. ${option.parentName}`;
      if (option.description && option.description !== option.parentName) {
        question += ` - ${option.description}`;
      }
      question += "\n";
    }

    question += "\n请告诉我具体是哪个方向？";

    return question;
  }

  /**
   * 生成拟人化歧义澄清问题
   * 对应业务逻辑: generateHumanizedAmbiguityQuestion(originalTech, ambiguityOptions)
   */
  generateHumanizedAmbiguityQuestion(originalTech, ambiguityOptions) {
    const templates = [
      `您说的是下面这几个方向中的哪一个呢？\n`,
      `是这几种的其中一个方向吗？如果是的，您告诉我一下，具体是哪一个吧。\n`,
      `我想确认一下，您指的是下面哪个具体方向呢？\n`,
      `为了给您更精准的推荐，能帮我确认一下是哪个方向吗？\n`,
    ];

    const template = templates[Math.floor(Math.random() * templates.length)];
    let question = template;

    for (const option of ambiguityOptions) {
      question += `${option.index}. ${option.parentName}`;
      if (option.description && option.description !== option.parentName) {
        question += ` - ${option.description}`;
      }
      question += "\n";
    }

    return question;
  }

  // ==================== 推荐引擎核心系统 ====================

  /**
   * 检查推荐触发条件 - 支持三种组合条件
   * 对应业务逻辑: checkRecommendationTrigger(candidateInfo, userMessage)
   */
  async checkRecommendationTrigger(candidateInfo, userMessage) {
    try {
      if (!candidateInfo) {
        return {
          shouldRecommend: false,
          type: "no_profile",
          userPreference: null,
          conditions: {
            condition1: false,
            condition2: false,
            condition3: false,
          },
        };
      }

      // 检查基础信息
      const hasTechDirection = !!candidateInfo.candidate_tech_direction_raw;
      const hasCompany = !!candidateInfo.current_company_name_raw;
      const hasLevel = !!candidateInfo.candidate_level_raw;
      const hasSalary = !!candidateInfo.expected_compensation_raw;
      const isGraduate = this.checkIsGraduate(candidateInfo);

      // 三种触发条件
      const condition1 = hasTechDirection && hasLevel && hasCompany; // 技术方向 + 职级 + 公司
      const condition2 = hasTechDirection && hasCompany && hasSalary; // 技术方向 + 公司 + 薪资
      const condition3 =
        hasTechDirection && hasCompany && hasSalary && hasLevel; // 完整信息

      // 应届生特殊处理
      const graduateCondition = isGraduate && hasTechDirection && hasCompany;

      const shouldRecommend =
        condition1 || condition2 || condition3 || graduateCondition;

      // 检测用户偏好
      const userPreference = this.detectUserPreference(userMessage);

      // 确定推荐类型
      let recommendationType = "initial";
      if (condition3) {
        recommendationType = "complete_info";
      } else if (condition2) {
        recommendationType = "with_salary";
      } else if (condition1) {
        recommendationType = "basic_info";
      } else if (graduateCondition) {
        recommendationType = "graduate";
      }

      return {
        shouldRecommend,
        type: recommendationType,
        userPreference,
        conditions: { condition1, condition2, condition3, graduateCondition },
        isGraduate,
      };
    } catch (error) {
      console.error("推荐触发条件检查失败:", error);
      return {
        shouldRecommend: false,
        type: "error",
        userPreference: null,
        conditions: { condition1: false, condition2: false, condition3: false },
      };
    }
  }

  /**
   * 生成职位推荐 - 主推荐生成函数
   * 对应业务逻辑: generateJobRecommendations(candidateInfo, recommendationType, userPreference)
   */
  async generateJobRecommendations(
    candidateInfo,
    recommendationType,
    userPreference
  ) {
    try {
      // 1. 查询匹配职位
      const allJobs = await this.queryMatchingJobs(
        candidateInfo,
        recommendationType
      );

      if (!allJobs || allJobs.length === 0) {
        return {
          头部大厂: [],
          国企: [],
          中型公司: [],
          创业型公司: [],
        };
      }

      // 2. 按4x4规则分类职位
      const categorizedJobs = this.categorizeJobsBy4x4Rule(
        allJobs,
        userPreference
      );

      // 3. 应用替补机制确保推荐数量
      const finalCategories = this.applyFallbackMechanism(
        categorizedJobs,
        allJobs
      );

      return finalCategories;
    } catch (error) {
      console.error("职位推荐生成失败:", error);
      return {
        头部大厂: [],
        国企: [],
        中型公司: [],
        创业型公司: [],
      };
    }
  }

  /**
   * 查询匹配职位 - 支持多维度匹配
   * 对应业务逻辑: queryMatchingJobs(candidateInfo, recommendationType)
   */
  async queryMatchingJobs(candidateInfo, recommendationType) {
    try {
      // 使用正确的表名 job_listings 而不是 jobs
      let query = supabase
        .from("job_listings")
        .select(
          `
          *,
          companies!inner(
            id,
            company_name,
            company_type,
            industry,
            description,
            website
          ),
          tech_tree!inner(
            id,
            tech_name,
            level,
            parent_tech_id
          )
        `
        )
        .eq("is_active", true); // 使用正确的字段名 is_active

      // 技术方向匹配
      if (candidateInfo.primary_tech_direction_id) {
        const relatedTechIds = await this.findRelatedTechIds(
          candidateInfo.primary_tech_direction_id
        );
        query = query.in("primary_tech_direction_id", relatedTechIds);
      }

      // 薪资匹配
      if (
        candidateInfo.expected_compensation_min &&
        candidateInfo.expected_compensation_max
      ) {
        query = query
          .gte("salary_max", candidateInfo.expected_compensation_min)
          .lte("salary_min", candidateInfo.expected_compensation_max);
      }

      // 地理位置匹配
      if (candidateInfo.desired_location_raw) {
        const locationKeywords = this.extractLocationKeywords(
          candidateInfo.desired_location_raw
        );
        if (locationKeywords.length > 0) {
          query = query.in("location", locationKeywords);
        }
      }

      // 职级匹配（如果有）
      if (
        candidateInfo.candidate_level_raw &&
        recommendationType !== "graduate"
      ) {
        const levelRange = this.parseJobLevelRange(
          candidateInfo.candidate_level_raw
        );
        if (levelRange.min && levelRange.max) {
          // 使用正确的字段名
          query = query
            .gte("job_standard_level_max", levelRange.min)
            .lte("job_standard_level_min", levelRange.max);
        }
      }

      // 经验要求匹配（应届生特殊处理）
      if (recommendationType === "graduate") {
        query = query.or(
          "experience_required.is.null,experience_required.ilike.%应届%,experience_required.ilike.%校招%"
        );
      }

      const { data: jobs } = await query.limit(50);
      return jobs || [];
    } catch (error) {
      console.error("职位查询失败:", error);
      return [];
    }
  }

  /**
   * 查找相关技术方向ID - 扩展匹配范围
   * 对应业务逻辑: findRelatedTechIds(primaryTechId)
   */
  async findRelatedTechIds(primaryTechId) {
    try {
      const relatedIds = [primaryTechId];

      // 查找同级技术方向
      const { data: primaryTech } = await supabase
        .from("tech_tree")
        .select("parent_tech_id, tech_name")
        .eq("id", primaryTechId)
        .single();

      if (primaryTech && primaryTech.parent_tech_id) {
        const { data: siblings } = await supabase
          .from("tech_tree")
          .select("id")
          .eq("parent_tech_id", primaryTech.parent_tech_id)
          .limit(5);

        if (siblings) {
          relatedIds.push(...siblings.map((s) => s.id));
        }
      }

      // 基于技术名称查找相关方向
      if (primaryTech && primaryTech.tech_name) {
        const nameRelated = await this.findRelatedTechByName(
          primaryTech.tech_name,
          primaryTechId
        );
        relatedIds.push(...nameRelated);
      }

      return [...new Set(relatedIds)]; // 去重
    } catch (error) {
      console.error("查找相关技术方向失败:", error);
      return [primaryTechId];
    }
  }

  /**
   * 基于技术名称查找相关技术方向
   * 对应业务逻辑: findRelatedTechByName(techName, excludeId)
   */
  async findRelatedTechByName(techName, excludeId) {
    try {
      const semanticGroups = this.buildSemanticGroups();
      const relatedIds = [];

      // 查找语义相关的技术方向
      for (const [groupName, keywords] of Object.entries(semanticGroups)) {
        const isRelated = keywords.some((keyword) =>
          techName.includes(keyword)
        );
        if (isRelated) {
          for (const keyword of keywords) {
            const { data: related } = await supabase
              .from("tech_tree")
              .select("id")
              .ilike("tech_name", `%${keyword}%`)
              .neq("id", excludeId)
              .limit(3);

            if (related) {
              relatedIds.push(...related.map((r) => r.id));
            }
          }
        }
      }

      return [...new Set(relatedIds)];
    } catch (error) {
      console.error("基于名称查找相关技术方向失败:", error);
      return [];
    }
  }

  /**
   * 按4x4规则对职位进行分类
   * 对应业务逻辑: categorizeJobsBy4x4Rule(jobs, userPreference)
   */
  categorizeJobsBy4x4Rule(jobs, userPreference) {
    const categories = {
      头部大厂: [],
      国企: [],
      中型公司: [],
      创业型公司: [],
    };

    const seenCompanies = new Set();

    // 根据用户偏好优先分配
    if (userPreference) {
      const preferredCategory = this.getPreferredCategory(userPreference);
      if (preferredCategory && categories[preferredCategory]) {
        this.fillCategoryWithPreference(
          jobs,
          categories[preferredCategory],
          preferredCategory,
          seenCompanies,
          4
        );
      }
    }

    // 填充其他分类
    for (const job of jobs) {
      if (seenCompanies.has(job.companies.company_name)) continue;

      const category = this.getJobCategory(job);
      if (categories[category] && categories[category].length < 4) {
        categories[category].push(job);
        seenCompanies.add(job.companies.company_name);
      }
    }

    return categories;
  }

  /**
   * 应用替补机制确保推荐数量
   * 对应业务逻辑: applyFallbackMechanism(categories, allJobs)
   */
  applyFallbackMechanism(categories, allJobs) {
    const seenCompanies = new Set();

    // 收集已使用的公司
    for (const categoryJobs of Object.values(categories)) {
      for (const job of categoryJobs) {
        seenCompanies.add(job.companies.company_name);
      }
    }

    // 为不足的分类补充职位
    for (const [categoryName, categoryJobs] of Object.entries(categories)) {
      while (categoryJobs.length < 4) {
        const availableJob = allJobs.find(
          (job) =>
            !seenCompanies.has(job.companies.company_name) &&
            this.getJobCategory(job) === categoryName
        );

        if (availableJob) {
          categoryJobs.push(availableJob);
          seenCompanies.add(availableJob.companies.company_name);
        } else {
          // 如果同类型没有更多职位，从其他类型补充
          const anyAvailableJob = allJobs.find(
            (job) => !seenCompanies.has(job.companies.company_name)
          );

          if (anyAvailableJob) {
            categoryJobs.push(anyAvailableJob);
            seenCompanies.add(anyAvailableJob.companies.company_name);
          } else {
            break; // 没有更多职位可用
          }
        }
      }
    }

    return categories;
  }

  /**
   * 获取用户偏好对应的分类
   */
  getPreferredCategory(userPreference) {
    const preferenceMap = {
      bigTech: "头部大厂",
      stateOwned: "国企",
      medium: "中型公司",
      startup: "创业型公司",
    };

    return preferenceMap[userPreference] || null;
  }

  /**
   * 根据公司类型确定职位分类
   */
  getJobCategory(job) {
    const companyType = job.companies?.company_type?.toLowerCase() || "";
    const companySize = job.companies?.company_size || "";

    if (companyType.includes("大厂") || companyType.includes("头部")) {
      return "头部大厂";
    }
    if (companyType.includes("国企") || companyType.includes("央企")) {
      return "国企";
    }
    if (
      companyType.includes("创业") ||
      companyType.includes("初创") ||
      companySize === "startup"
    ) {
      return "创业型公司";
    }

    return "中型公司"; // 默认分类
  }

  /**
   * 优先填充偏好分类
   */
  fillCategoryWithPreference(
    jobs,
    categoryArray,
    categoryName,
    seenCompanies,
    maxCount
  ) {
    for (const job of jobs) {
      if (categoryArray.length >= maxCount) break;
      if (seenCompanies.has(job.companies.company_name)) continue;
      if (this.getJobCategory(job) === categoryName) {
        categoryArray.push(job);
        seenCompanies.add(job.companies.company_name);
      }
    }
  }

  // ==================== 辅助工具方法 ====================

  /**
   * 提取地理位置关键词 - 支持主要城市和区域映射
   * 对应业务逻辑: extractLocationKeywords(locationString)
   */
  extractLocationKeywords(locationString) {
    if (!locationString) return [];

    const keywords = [];
    const normalizedLocation = locationString.toLowerCase();

    for (const [city, variants] of Object.entries(this.locationMap)) {
      if (
        variants.some((variant) =>
          normalizedLocation.includes(variant.toLowerCase())
        )
      ) {
        keywords.push(...variants);
      }
    }

    // 如果没有匹配到预定义城市，直接使用原始输入
    if (keywords.length === 0) {
      keywords.push(locationString);
    }

    return [...new Set(keywords)];
  }

  /**
   * 解析职位级别范围
   * 对应业务逻辑: parseJobLevelRange(jobLevelStr)
   */
  parseJobLevelRange(jobLevelStr) {
    if (!jobLevelStr) return { min: null, max: null };

    // 提取数字
    const numbers = jobLevelStr.match(/\d+/g);
    if (!numbers) return { min: null, max: null };

    const levels = numbers.map((n) => parseInt(n));

    if (levels.length === 1) {
      return { min: levels[0], max: levels[0] };
    } else if (levels.length >= 2) {
      return { min: Math.min(...levels), max: Math.max(...levels) };
    }

    return { min: null, max: null };
  }

  /**
   * 生成推荐文本 - 主推荐文本生成函数
   * 对应业务逻辑: generateRecommendationText(candidateInfo, categorizedJobs, recommendationType, userPreference)
   */
  async generateRecommendationText(
    candidateInfo,
    categorizedJobs,
    recommendationType,
    userPreference
  ) {
    try {
      let recommendationText = "";

      // 生成开头
      const greeting = this.generateRecommendationGreeting(
        candidateInfo,
        recommendationType
      );
      recommendationText += greeting + "\n\n";

      // 生成各分类的推荐
      for (const [categoryName, jobs] of Object.entries(categorizedJobs)) {
        if (jobs && jobs.length > 0) {
          recommendationText += `**${categoryName}：**\n`;

          for (let i = 0; i < Math.min(jobs.length, 4); i++) {
            const job = jobs[i];
            const jobText = await this.formatJobRecommendation(
              job,
              candidateInfo,
              i + 1
            );
            recommendationText += jobText + "\n";
          }

          recommendationText += "\n";
        }
      }

      // 生成结尾
      const closing = this.generateRecommendationClosing(userPreference);
      recommendationText += closing;

      return recommendationText;
    } catch (error) {
      console.error("推荐文本生成失败:", error);
      return "基于您的背景，我为您推荐了一些合适的职位机会，请查看详细信息。";
    }
  }

  /**
   * 生成推荐开头语
   */
  generateRecommendationGreeting(candidateInfo, recommendationType) {
    const techDirection = candidateInfo.candidate_tech_direction_raw || "技术";
    const company = candidateInfo.current_company_name_raw || "";

    if (recommendationType === "graduate") {
      return `基于您的${techDirection}背景，我为您推荐以下校招职位：`;
    }

    if (company) {
      return `基于您在${company}的${techDirection}经验，我为您推荐以下职位：`;
    }

    return `基于您的${techDirection}背景，我为您推荐以下职位：`;
  }

  /**
   * 格式化单个职位推荐
   */
  async formatJobRecommendation(job, candidateInfo, index) {
    try {
      const companyName = job.companies?.company_name || "未知公司";
      const jobTitle = job.job_title || "技术职位";
      const location = job.location || "";
      const salaryRange = this.formatSalaryRange(
        job.salary_min,
        job.salary_max
      );

      let jobText = `${index}. **${companyName}** - ${jobTitle}`;

      if (location) {
        jobText += ` (${location})`;
      }

      if (salaryRange) {
        jobText += ` | ${salaryRange}`;
      }

      // 生成推荐理由
      const reason = await this.generateMatchReason(candidateInfo, job);
      if (reason) {
        jobText += `\n   ${reason}`;
      }

      return jobText;
    } catch (error) {
      console.error("格式化职位推荐失败:", error);
      return `${index}. ${job.companies?.company_name || "公司"} - ${
        job.job_title || "职位"
      }`;
    }
  }

  /**
   * 格式化薪酬范围
   * 对应业务逻辑: formatSalaryRange(salaryMin, salaryMax)
   */
  formatSalaryRange(salaryMin, salaryMax) {
    if (!salaryMin && !salaryMax) return "";

    if (salaryMin && salaryMax) {
      if (salaryMin === salaryMax) {
        return `${salaryMin}万`;
      }
      return `${salaryMin}-${salaryMax}万`;
    }

    if (salaryMin) {
      return `${salaryMin}万起`;
    }

    if (salaryMax) {
      return `最高${salaryMax}万`;
    }

    return "";
  }

  /**
   * 生成推荐理由 - 基于真实JD的推荐理由
   * 对应业务逻辑: generateMatchReason(candidateInfo, job)
   */
  async generateMatchReason(candidateInfo, job) {
    try {
      const reasons = [];

      // 技术方向匹配
      if (
        candidateInfo.candidate_tech_direction_raw &&
        job.primary_tech_direction_name
      ) {
        if (
          candidateInfo.candidate_tech_direction_raw.includes(
            job.primary_tech_direction_name
          ) ||
          job.primary_tech_direction_name.includes(
            candidateInfo.candidate_tech_direction_raw
          )
        ) {
          reasons.push(
            `技术方向匹配您的${candidateInfo.candidate_tech_direction_raw}背景`
          );
        }
      }

      // 薪资匹配
      if (
        candidateInfo.expected_compensation_min &&
        candidateInfo.expected_compensation_max
      ) {
        const jobSalaryAvg = (job.salary_min + job.salary_max) / 2;
        const expectedAvg =
          (candidateInfo.expected_compensation_min +
            candidateInfo.expected_compensation_max) /
          2;

        if (Math.abs(jobSalaryAvg - expectedAvg) <= 5) {
          reasons.push("薪资范围符合您的期望");
        }
      }

      // 公司规模匹配
      if (job.companies?.company_type) {
        reasons.push(`${job.companies.company_type}发展机会`);
      }

      // 地理位置匹配
      if (candidateInfo.desired_location_raw && job.location) {
        if (
          candidateInfo.desired_location_raw.includes(job.location) ||
          job.location.includes(candidateInfo.desired_location_raw)
        ) {
          reasons.push("地理位置符合期望");
        }
      }

      return reasons.length > 0 ? reasons.join("，") : "综合匹配度较高";
    } catch (error) {
      console.error("生成推荐理由失败:", error);
      return "推荐匹配";
    }
  }

  /**
   * 生成推荐结尾语
   */
  generateRecommendationClosing(userPreference) {
    const closingTemplates = [
      "以上职位都是基于您的背景精心筛选的，如需了解详细JD或有其他问题，请随时告诉我。",
      "这些推荐都考虑了您的技术背景和职业发展，有感兴趣的职位可以进一步沟通。",
      "推荐职位已按公司类型分类，您可以根据个人偏好选择感兴趣的方向。",
    ];

    if (userPreference) {
      const preferenceText = this.getPreferenceText(userPreference);
      return `特别为您推荐了${preferenceText}的职位。${closingTemplates[0]}`;
    }

    return closingTemplates[
      Math.floor(Math.random() * closingTemplates.length)
    ];
  }

  /**
   * 获取偏好描述文本
   */
  getPreferenceText(userPreference) {
    const preferenceTextMap = {
      bigTech: "头部大厂",
      stateOwned: "国企",
      medium: "中型公司",
      startup: "创业型公司",
    };

    return preferenceTextMap[userPreference] || "匹配";
  }

  /**
   * 生成对话回复 - AI驱动的通用对话生成
   * 对应业务逻辑: generateConversationReply(userMessage, candidateInfo)
   */
  async generateConversationReply(userMessage, candidateInfo) {
    try {
      const prompt = `
作为专业的AI招聘顾问Katrina，请回复用户的消息。

用户消息: ${userMessage}
候选人背景: ${JSON.stringify(candidateInfo)}

要求：
1. 保持专业、友好的语调
2. 如果用户询问职位相关问题，引导提供更多背景信息
3. 回复简洁明了，不超过100字
4. 体现AI招聘顾问的专业性

请直接返回回复内容，不要包含其他格式。
`;

      const response = await callDeepSeek(prompt, {
        temperature: 0.7,
        maxTokens: 150,
      });

      return {
        reply: response.content || "感谢您的消息，我会尽力为您提供帮助。",
        tokensUsed: response.tokensUsed || 100,
        apiTier: "deepseek_medium",
      };
    } catch (error) {
      console.error("生成对话回复失败:", error);
      return {
        reply: "感谢您的消息，我正在为您处理...",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }
}

// 导出单例实例
export const messageProcessor = new MessageProcessor();
