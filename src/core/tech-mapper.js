/**
 * 技术方向映射器
 * 负责技术方向映射、歧义检测和澄清
 */

import { databaseManager } from './database-manager.js';
import { TECH_HIERARCHY_MAP, SEMANTIC_MAPPINGS, STOP_WORDS } from '../config/mapping-tables.js';

// ==================== 技术方向映射器 ====================

export class TechMapper {
  constructor() {
    this.techHierarchyMap = TECH_HIERARCHY_MAP;
    this.semanticMappings = SEMANTIC_MAPPINGS;
    this.stopWords = STOP_WORDS;
    this.ambiguityStateCache = new Map();
  }

  // ==================== 技术方向映射 ====================

  /**
   * 获取技术方向ID - 智能映射技术方向到数据库ID
   * 对应业务逻辑: getTechDirectionId(techName, candidateInfo)
   */
  async getTechDirectionId(techName, candidateInfo) {
    try {
      if (!techName) return null;

      // 1. 标准化输入
      const normalizedTech = this.normalizeInput(techName);

      // 2. 检查硬编码映射
      const hardcodedId = this.techHierarchyMap[normalizedTech];
      if (hardcodedId) {
        return hardcodedId;
      }

      // 3. 语义映射检查
      const semanticMapped = this.semanticMappings[normalizedTech];
      if (semanticMapped && this.techHierarchyMap[semanticMapped]) {
        return this.techHierarchyMap[semanticMapped];
      }

      // 4. 数据库精确匹配
      const { data: exactMatch } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('id')
        .eq('tech_name', normalizedTech)
        .single();
      
      if (exactMatch) {
        return exactMatch.id;
      }

      // 5. 模糊匹配
      const { data: fuzzyMatches } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('id, tech_name')
        .ilike('tech_name', `%${normalizedTech}%`)
        .limit(5);

      if (fuzzyMatches && fuzzyMatches.length > 0) {
        // 选择最相似的匹配
        const bestMatch = fuzzyMatches.find(match => 
          match.tech_name.toLowerCase().includes(normalizedTech.toLowerCase())
        );
        if (bestMatch) {
          return bestMatch.id;
        }
      }

      // 6. 如果有候选人信息，尝试上下文推断
      if (candidateInfo && candidateInfo.primary_tech_direction_id) {
        // 查找相关的子技术方向
        const { data: relatedTechs } = await databaseManager.supabase
          .from(databaseManager.tables.techTree)
          .select('id, tech_name')
          .eq('parent_id', candidateInfo.primary_tech_direction_id)
          .ilike('tech_name', `%${normalizedTech}%`);

        if (relatedTechs && relatedTechs.length > 0) {
          return relatedTechs[0].id;
        }
      }

      return null;
    } catch (error) {
      console.error("技术方向映射失败:", error);
      return null;
    }
  }

  // ==================== 歧义检测 ====================

  /**
   * 智能技术方向歧义检测
   * 对应业务逻辑: detectTechAmbiguityIntelligently(userInput)
   */
  async detectTechAmbiguityIntelligently(userInput) {
    try {
      if (!userInput) {
        return { originalTech: userInput, options: [], needsClarification: false };
      }

      // 1. 标准化输入
      const normalizedInput = this.normalizeInput(userInput);
      
      // 2. 扩展自然语言表达
      const expandedKeywords = this.expandNaturalExpressions(normalizedInput);
      
      // 3. 查找所有可能的匹配
      const exactMatches = await this.findExactTechMatches(normalizedInput);
      const fuzzyMatches = await this.findFuzzyTechMatches(normalizedInput);
      const keywordMatches = await this.findKeywordTechMatches(normalizedInput);
      
      // 4. 合并匹配结果
      const allMatches = this.mergeTechMatches(exactMatches, fuzzyMatches, keywordMatches);
      
      // 5. 分析歧义
      const ambiguityResult = await this.analyzeTechAmbiguity(allMatches, userInput);
      
      return ambiguityResult;
    } catch (error) {
      console.error('智能歧义检测失败:', error);
      return { originalTech: userInput, options: [], needsClarification: false };
    }
  }

  /**
   * 查找精确技术匹配
   * 对应业务逻辑: findExactTechMatches(normalizedInput)
   */
  async findExactTechMatches(normalizedInput) {
    try {
      const { data } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('id, tech_name, parent_id, description')
        .eq('tech_name', normalizedInput);

      return data || [];
    } catch (error) {
      console.error('精确匹配查询失败:', error);
      return [];
    }
  }

  /**
   * 查找模糊技术匹配
   * 对应业务逻辑: findFuzzyTechMatches(normalizedInput)
   */
  async findFuzzyTechMatches(normalizedInput) {
    try {
      const { data } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('id, tech_name, parent_id, description')
        .or(`tech_name.ilike.%${normalizedInput}%,description.ilike.%${normalizedInput}%`)
        .limit(20);

      return data || [];
    } catch (error) {
      console.error('模糊匹配查询失败:', error);
      return [];
    }
  }

  /**
   * 查找关键词技术匹配
   * 对应业务逻辑: findKeywordTechMatches(normalizedInput)
   */
  async findKeywordTechMatches(normalizedInput) {
    try {
      // 提取关键词
      const keywords = this.extractKeywords(normalizedInput);
      
      if (keywords.length === 0) return [];

      const conditions = keywords.map(keyword => 
        `tech_name.ilike.%${keyword}%`
      ).join(',');

      const { data } = await databaseManager.supabase
        .from(databaseManager.tables.techTree)
        .select('id, tech_name, parent_id, description')
        .or(conditions)
        .limit(15);

      return data || [];
    } catch (error) {
      console.error('关键词匹配查询失败:', error);
      return [];
    }
  }

  /**
   * 合并技术匹配结果
   * 对应业务逻辑: mergeTechMatches(exactMatches, fuzzyMatches, keywordMatches)
   */
  mergeTechMatches(exactMatches, fuzzyMatches, keywordMatches) {
    const allMatches = new Map();

    // 精确匹配优先级最高
    exactMatches.forEach(match => {
      allMatches.set(match.id, { ...match, matchType: 'exact', priority: 1 });
    });

    // 模糊匹配次之
    fuzzyMatches.forEach(match => {
      if (!allMatches.has(match.id)) {
        allMatches.set(match.id, { ...match, matchType: 'fuzzy', priority: 2 });
      }
    });

    // 关键词匹配优先级最低
    keywordMatches.forEach(match => {
      if (!allMatches.has(match.id)) {
        allMatches.set(match.id, { ...match, matchType: 'keyword', priority: 3 });
      }
    });

    return Array.from(allMatches.values());
  }

  /**
   * 分析技术歧义
   * 对应业务逻辑: analyzeTechAmbiguity(allMatches, userInput)
   */
  async analyzeTechAmbiguity(allMatches, userInput) {
    if (allMatches.length === 0) {
      return {
        originalTech: userInput,
        options: [],
        needsClarification: false,
        reason: 'no_matches'
      };
    }

    if (allMatches.length === 1) {
      return {
        originalTech: userInput,
        options: [],
        needsClarification: false,
        selectedTech: allMatches[0],
        reason: 'single_match'
      };
    }

    // 多个匹配，需要歧义澄清
    const options = await this.buildAmbiguityOptions(allMatches);
    
    return {
      originalTech: userInput,
      options: options,
      needsClarification: true,
      reason: 'multiple_matches',
      matchCount: allMatches.length
    };
  }

  /**
   * 构建歧义选项
   * 对应业务逻辑: buildAmbiguityOptions(allMatches)
   */
  async buildAmbiguityOptions(allMatches) {
    const options = [];
    
    // 按优先级排序
    const sortedMatches = allMatches.sort((a, b) => a.priority - b.priority);
    
    for (let i = 0; i < Math.min(sortedMatches.length, 5); i++) {
      const match = sortedMatches[i];
      
      // 获取父级技术方向名称
      let parentName = match.tech_name;
      if (match.parent_id) {
        const { data: parent } = await databaseManager.supabase
          .from(databaseManager.tables.techTree)
          .select('tech_name')
          .eq('id', match.parent_id)
          .single();
        
        if (parent) {
          parentName = parent.tech_name;
        }
      }

      options.push({
        index: i + 1,
        id: match.id,
        techName: match.tech_name,
        parentName: parentName,
        description: match.description || match.tech_name,
        matchType: match.matchType
      });
    }

    return options;
  }

  // ==================== 歧义澄清 ====================

  /**
   * 生成歧义澄清问题
   * 对应业务逻辑: generateAmbiguityQuestion(originalTech, ambiguityOptions)
   */
  generateAmbiguityQuestion(originalTech, ambiguityOptions) {
    if (!ambiguityOptions || ambiguityOptions.length === 0) {
      return `您说的"${originalTech}"我没有完全理解，能否提供更多信息？`;
    }

    let question = `您说的"${originalTech}"可能指以下几个方向中的哪一个？\n\n`;
    
    for (const option of ambiguityOptions) {
      question += `${option.index}. ${option.parentName}`;
      if (option.description && option.description !== option.parentName) {
        question += ` - ${option.description}`;
      }
      question += '\n';
    }

    question += '\n请告诉我具体是哪个方向？';
    
    return question;
  }

  /**
   * 生成拟人化歧义澄清问题
   * 对应业务逻辑: generateHumanizedAmbiguityQuestion(originalTech, ambiguityOptions)
   */
  generateHumanizedAmbiguityQuestion(originalTech, ambiguityOptions) {
    if (!ambiguityOptions || ambiguityOptions.length === 0) {
      return `关于"${originalTech}"，我想确认一下具体是指哪个技术领域呢？`;
    }

    const greetings = [
      '我想确认一下',
      '为了给您更精准的推荐',
      '让我确认一下您的技术方向'
    ];

    const greeting = greetings[Math.floor(Math.random() * greetings.length)];
    
    let question = `${greeting}，您提到的"${originalTech}"是指：\n\n`;
    
    for (const option of ambiguityOptions) {
      question += `${option.index}. ${option.parentName}`;
      if (option.description && option.description !== option.parentName) {
        question += ` (${option.description})`;
      }
      question += '\n';
    }

    question += '\n请选择对应的数字，或者直接告诉我具体方向～';
    
    return question;
  }

  // ==================== 工具方法 ====================

  /**
   * 标准化输入
   * 对应业务逻辑: normalizeInput(input)
   */
  normalizeInput(input) {
    if (!input) return '';
    
    return input
      .toLowerCase()
      .trim()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 只保留中文、英文、数字
      .replace(/\s+/g, ''); // 移除空格
  }

  /**
   * 扩展自然语言表达
   * 对应业务逻辑: expandNaturalExpressions(normalizedInput)
   */
  expandNaturalExpressions(normalizedInput) {
    const expansions = {
      '机器学习': ['ml', '机器学习', '深度学习'],
      '人工智能': ['ai', '人工智能', '机器学习'],
      '算法': ['算法', '机器学习', '深度学习'],
      '开发': ['开发', '编程', '程序员'],
      '前端': ['前端', 'frontend', 'fe'],
      '后端': ['后端', 'backend', 'be']
    };

    return expansions[normalizedInput] || [normalizedInput];
  }

  /**
   * 提取关键词
   * 对应业务逻辑: extractKeywords(input)
   */
  extractKeywords(input) {
    if (!input) return [];

    // 简单的关键词提取，移除停用词
    const words = input.split(/\s+/);
    return words.filter(word => 
      word.length > 1 && !this.stopWords.includes(word)
    );
  }

  /**
   * 保存歧义状态
   * 对应业务逻辑: saveAmbiguityState(userId, ambiguityData)
   */
  saveAmbiguityState(userId, ambiguityData) {
    try {
      const cacheKey = `ambiguity_${userId}`;
      this.ambiguityStateCache.set(cacheKey, {
        ...ambiguityData,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('保存歧义状态失败:', error);
    }
  }

  /**
   * 获取歧义状态
   * 对应业务逻辑: getAmbiguityState(userId)
   */
  getAmbiguityState(userId) {
    try {
      const cacheKey = `ambiguity_${userId}`;
      const cached = this.ambiguityStateCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < 10 * 60 * 1000) { // 10分钟有效
        return cached;
      }
      
      this.ambiguityStateCache.delete(cacheKey);
      return null;
    } catch (error) {
      console.error('获取歧义状态失败:', error);
      return null;
    }
  }

  /**
   * 清除歧义状态
   * 对应业务逻辑: clearAmbiguityState(userId)
   */
  clearAmbiguityState(userId) {
    try {
      const cacheKey = `ambiguity_${userId}`;
      this.ambiguityStateCache.delete(cacheKey);
    } catch (error) {
      console.error('清除歧义状态失败:', error);
    }
  }
}

// 导出单例实例
export const techMapper = new TechMapper();
