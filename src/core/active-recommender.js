/**
 * 主动推荐引擎
 * 负责处理候选人主动索要推荐的各种场景
 */

import { databaseManager } from './database-manager.js';
import { passiveRecommender } from './passive-recommender.js';
import { JOB_CATEGORIES, USER_PREFERENCE_MAP } from '../config/mapping-tables.js';

// ==================== 主动推荐引擎 ====================

export class ActiveRecommender {
  constructor() {
    this.passiveRecommender = passiveRecommender;
  }

  // ==================== 主动推荐处理 ====================

  /**
   * 处理通用推荐请求
   * 对应业务逻辑: handleGeneralRequest(candidateInfo, context)
   */
  async handleGeneralRequest(candidateInfo, context) {
    try {
      // "还有其他职位吗？" "给我推荐更多职位"
      
      // 1. 获取已推荐的公司列表
      const excludeCompanies = await this.passiveRecommender.getRecentRecommendedJobs(candidateInfo.user_id);
      
      // 2. 查询新的职位，排除已推荐的
      const allJobs = await databaseManager.queryMatchingJobs(candidateInfo, 'active_general');
      
      const filteredJobs = allJobs.filter(job => 
        !excludeCompanies.includes(job.companies?.company_name)
      );

      if (filteredJobs.length === 0) {
        return {
          success: false,
          message: "抱歉，基于您的条件，暂时没有更多合适的职位推荐。建议您可以适当调整期望条件。",
          recommendations: this.passiveRecommender.getEmptyRecommendations()
        };
      }

      // 3. 按4x4规则分类
      const categorizedJobs = this.passiveRecommender.categorizeJobsBy4x4Rule(filteredJobs, null);

      return {
        success: true,
        message: "为您找到了更多合适的职位：",
        recommendations: categorizedJobs,
        requestType: 'general'
      };
    } catch (error) {
      console.error('处理通用推荐请求失败:', error);
      return {
        success: false,
        message: "获取更多推荐时遇到问题，请稍后再试。",
        recommendations: this.passiveRecommender.getEmptyRecommendations()
      };
    }
  }

  /**
   * 处理特定推荐请求
   * 对应业务逻辑: handleSpecificRequest(candidateInfo, context)
   */
  async handleSpecificRequest(candidateInfo, context) {
    try {
      // "有没有阿里巴巴的职位？" "推荐一些大厂的职位" "有没有薪资更高的？"
      
      const requestInfo = this.parseSpecificRequest(context.userMessage);
      
      if (!requestInfo.isValid) {
        return {
          success: false,
          message: "请告诉我您具体想了解什么类型的职位？比如：特定公司、薪资范围、公司类型等。",
          recommendations: this.passiveRecommender.getEmptyRecommendations()
        };
      }

      // 构建特定查询条件
      const specificCandidateInfo = this.buildSpecificQuery(candidateInfo, requestInfo);
      
      // 查询匹配的职位
      const matchingJobs = await databaseManager.queryMatchingJobs(specificCandidateInfo, 'active_specific');
      
      if (matchingJobs.length === 0) {
        return {
          success: false,
          message: `抱歉，没有找到符合"${requestInfo.description}"条件的职位。`,
          recommendations: this.passiveRecommender.getEmptyRecommendations()
        };
      }

      // 按4x4规则分类
      const categorizedJobs = this.passiveRecommender.categorizeJobsBy4x4Rule(matchingJobs, requestInfo.preference);

      return {
        success: true,
        message: `为您找到了符合"${requestInfo.description}"条件的职位：`,
        recommendations: categorizedJobs,
        requestType: 'specific',
        requestInfo: requestInfo
      };
    } catch (error) {
      console.error('处理特定推荐请求失败:', error);
      return {
        success: false,
        message: "获取特定推荐时遇到问题，请稍后再试。",
        recommendations: this.passiveRecommender.getEmptyRecommendations()
      };
    }
  }

  /**
   * 处理过滤推荐请求
   * 对应业务逻辑: handleFilteredRequest(candidateInfo, context)
   */
  async handleFilteredRequest(candidateInfo, context) {
    try {
      // "不要创业公司的" "只要北京的职位" "薪资30万以上的"
      
      const filters = this.parseFilterRequest(context.userMessage);
      
      if (filters.length === 0) {
        return {
          success: false,
          message: "请告诉我您想要过滤什么条件？比如：地点、公司类型、薪资范围等。",
          recommendations: this.passiveRecommender.getEmptyRecommendations()
        };
      }

      // 应用过滤条件
      const filteredCandidateInfo = this.applyFilters(candidateInfo, filters);
      
      // 查询过滤后的职位
      const filteredJobs = await databaseManager.queryMatchingJobs(filteredCandidateInfo, 'active_filtered');
      
      if (filteredJobs.length === 0) {
        const filterDescription = filters.map(f => f.description).join('、');
        return {
          success: false,
          message: `抱歉，没有找到符合过滤条件"${filterDescription}"的职位。`,
          recommendations: this.passiveRecommender.getEmptyRecommendations()
        };
      }

      // 按4x4规则分类
      const categorizedJobs = this.passiveRecommender.categorizeJobsBy4x4Rule(filteredJobs, null);

      const filterDescription = filters.map(f => f.description).join('、');
      return {
        success: true,
        message: `为您找到了符合过滤条件"${filterDescription}"的职位：`,
        recommendations: categorizedJobs,
        requestType: 'filtered',
        filters: filters
      };
    } catch (error) {
      console.error('处理过滤推荐请求失败:', error);
      return {
        success: false,
        message: "获取过滤推荐时遇到问题，请稍后再试。",
        recommendations: this.passiveRecommender.getEmptyRecommendations()
      };
    }
  }

  // ==================== 请求解析 ====================

  /**
   * 解析主动推荐请求
   * 对应业务逻辑: parseActiveRequest(userMessage)
   */
  parseActiveRequest(userMessage) {
    const message = userMessage.toLowerCase();
    
    // 通用请求模式
    const generalPatterns = [
      /还有.*?职位/,
      /更多.*?推荐/,
      /其他.*?工作/,
      /再.*?推荐/,
      /换.*?批/
    ];

    if (generalPatterns.some(pattern => pattern.test(message))) {
      return { type: 'general', confidence: 0.9 };
    }

    // 特定请求模式
    const specificPatterns = [
      /有没有.*?(公司|企业)/,
      /推荐.*?(大厂|国企|创业)/,
      /薪资.*?(更高|以上)/,
      /.*?的职位/
    ];

    if (specificPatterns.some(pattern => pattern.test(message))) {
      return { type: 'specific', confidence: 0.8 };
    }

    // 过滤请求模式
    const filterPatterns = [
      /不要.*?(创业|国企|大厂)/,
      /只要.*?(北京|上海|深圳)/,
      /薪资.*?(以上|以下)/,
      /排除.*?/
    ];

    if (filterPatterns.some(pattern => pattern.test(message))) {
      return { type: 'filtered', confidence: 0.8 };
    }

    return { type: 'unknown', confidence: 0.3 };
  }

  /**
   * 解析特定推荐请求
   * 对应业务逻辑: parseSpecificRequest(userMessage)
   */
  parseSpecificRequest(userMessage) {
    const message = userMessage.toLowerCase();
    const result = {
      isValid: false,
      type: null,
      value: null,
      description: '',
      preference: null
    };

    // 公司名称请求
    const companyMatch = message.match(/有没有.*?(阿里|腾讯|字节|美团|百度|京东|滴滴|小米|华为)/);
    if (companyMatch) {
      result.isValid = true;
      result.type = 'company';
      result.value = companyMatch[1];
      result.description = `${companyMatch[1]}的职位`;
      return result;
    }

    // 公司类型请求
    const typeMatch = message.match(/(大厂|头部|国企|央企|创业|初创)/);
    if (typeMatch) {
      result.isValid = true;
      result.type = 'companyType';
      result.value = typeMatch[1];
      result.description = `${typeMatch[1]}类型的职位`;
      result.preference = USER_PREFERENCE_MAP[typeMatch[1]] || null;
      return result;
    }

    // 薪资请求
    const salaryMatch = message.match(/薪资.*?(\d+).*?(万|k)/);
    if (salaryMatch) {
      result.isValid = true;
      result.type = 'salary';
      result.value = `${salaryMatch[1]}${salaryMatch[2]}`;
      result.description = `薪资${salaryMatch[1]}${salaryMatch[2]}以上的职位`;
      return result;
    }

    return result;
  }

  /**
   * 解析过滤请求
   * 对应业务逻辑: parseFilterRequest(userMessage)
   */
  parseFilterRequest(userMessage) {
    const message = userMessage.toLowerCase();
    const filters = [];

    // 排除公司类型
    const excludeTypeMatch = message.match(/不要.*?(创业|国企|大厂|初创)/);
    if (excludeTypeMatch) {
      filters.push({
        type: 'excludeCompanyType',
        value: excludeTypeMatch[1],
        description: `排除${excludeTypeMatch[1]}公司`
      });
    }

    // 指定地点
    const locationMatch = message.match(/只要.*?(北京|上海|深圳|杭州|广州|成都|南京|武汉|西安|苏州)/);
    if (locationMatch) {
      filters.push({
        type: 'includeLocation',
        value: locationMatch[1],
        description: `只要${locationMatch[1]}的职位`
      });
    }

    // 薪资范围
    const salaryFilterMatch = message.match(/薪资.*?(\d+).*?(万|k).*?(以上|以下)/);
    if (salaryFilterMatch) {
      filters.push({
        type: 'salaryRange',
        value: `${salaryFilterMatch[1]}${salaryFilterMatch[2]}`,
        operator: salaryFilterMatch[3],
        description: `薪资${salaryFilterMatch[1]}${salaryFilterMatch[2]}${salaryFilterMatch[3]}`
      });
    }

    return filters;
  }

  // ==================== 查询构建 ====================

  /**
   * 构建特定查询条件
   * 对应业务逻辑: buildSpecificQuery(candidateInfo, requestInfo)
   */
  buildSpecificQuery(candidateInfo, requestInfo) {
    const specificInfo = { ...candidateInfo };

    switch (requestInfo.type) {
      case 'company':
        specificInfo.targetCompany = requestInfo.value;
        break;
      case 'companyType':
        specificInfo.preferredCompanyType = requestInfo.preference;
        break;
      case 'salary':
        const salaryValue = parseInt(requestInfo.value);
        specificInfo.expected_compensation_min = salaryValue;
        break;
    }

    return specificInfo;
  }

  /**
   * 应用过滤条件
   * 对应业务逻辑: applyFilters(candidateInfo, filters)
   */
  applyFilters(candidateInfo, filters) {
    const filteredInfo = { ...candidateInfo };

    for (const filter of filters) {
      switch (filter.type) {
        case 'excludeCompanyType':
          filteredInfo.excludeCompanyType = filter.value;
          break;
        case 'includeLocation':
          filteredInfo.desired_location_raw = filter.value;
          break;
        case 'salaryRange':
          const salaryValue = parseInt(filter.value);
          if (filter.operator === '以上') {
            filteredInfo.expected_compensation_min = salaryValue;
          } else if (filter.operator === '以下') {
            filteredInfo.expected_compensation_max = salaryValue;
          }
          break;
      }
    }

    return filteredInfo;
  }

  // ==================== 第二次推荐 ====================

  /**
   * 生成第二次推荐
   * 对应业务逻辑: generateSecondRecommendations(candidateInfo, preference, excludeCompanies)
   */
  async generateSecondRecommendations(candidateInfo, preference, excludeCompanies) {
    try {
      // 查询职位，排除已推荐的公司
      const allJobs = await databaseManager.queryMatchingJobs(candidateInfo, 'second_recommendation');
      
      if (!allJobs || allJobs.length === 0) {
        return this.passiveRecommender.getEmptyRecommendations();
      }

      // 过滤已推荐的公司
      const filteredJobs = allJobs.filter(job => 
        !excludeCompanies.includes(job.companies?.company_name)
      );

      // 按4x4规则分类
      const categorizedJobs = this.passiveRecommender.categorizeJobsBy4x4Rule(filteredJobs, preference);

      return categorizedJobs;
    } catch (error) {
      console.error('生成第二次推荐失败:', error);
      return this.passiveRecommender.getEmptyRecommendations();
    }
  }

  /**
   * 提取第二次推荐偏好
   * 对应业务逻辑: extractSecondRecommendationPreference(userMessage)
   */
  extractSecondRecommendationPreference(userMessage) {
    const preferences = {
      '大厂': 'bigTech',
      '头部': 'bigTech',
      '国企': 'stateOwned',
      '央企': 'stateOwned',
      '中型': 'medium',
      '创业': 'startup',
      '初创': 'startup'
    };

    for (const [keyword, preference] of Object.entries(preferences)) {
      if (userMessage.includes(keyword)) {
        return preference;
      }
    }

    return null;
  }
}

// 导出单例实例
export const activeRecommender = new ActiveRecommender();
