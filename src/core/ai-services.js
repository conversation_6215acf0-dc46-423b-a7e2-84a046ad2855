/**
 * Katrina AI 服务层
 * AI分析、对话生成、意图识别服务
 */

import {
  callDeepSeek,
  extractInformation,
  generateResponse,
  recognizeIntent,
} from "../../lib/deepseekAgent.js";
import { validateMessage } from "../utils/validators.js";
import { AIServiceError } from "../utils/error-handlers.js";
import {
  generateId,
  calculateSimilarity,
  normalizeText,
} from "../utils/utilities.js";
import { AI_CONFIG, INTENT_TYPES, TECH_MAPPINGS } from "../utils/config.js";

// ==================== 上下文分析器 ====================

export class ContextAnalyzer {
  constructor() {
    this.intentPatterns = this.buildIntentPatterns();
    this.techKeywords = this.buildTechKeywords();
  }

  /**
   * 分析用户消息上下文
   */
  async analyzeContext(
    userMessage,
    conversationHistory = [],
    candidateInfo = {}
  ) {
    try {
      const validation = validateMessage(userMessage);
      if (!validation.valid) {
        throw new AIServiceError(`消息验证失败: ${validation.error}`);
      }

      const normalizedMessage = normalizeText(userMessage);

      // 1. 基础意图识别
      const basicIntent = this.detectBasicIntent(normalizedMessage);

      // 2. AI增强意图分析
      const aiIntent = await this.performAIIntentAnalysis(
        userMessage,
        conversationHistory,
        candidateInfo
      );

      // 3. 技术方向检测
      const techAnalysis = this.analyzeTechDirection(normalizedMessage);

      // 4. 对话阶段判断
      const conversationStage = this.determineConversationStage(
        conversationHistory,
        candidateInfo
      );

      // 5. 歧义检测
      const ambiguityCheck = this.detectAmbiguity(
        normalizedMessage,
        techAnalysis
      );

      const analysis = {
        intent: aiIntent.intent || basicIntent,
        confidence: aiIntent.confidence || basicIntent.confidence,
        techDirection: techAnalysis.direction,
        techConfidence: techAnalysis.confidence,
        conversationStage,
        ambiguity: ambiguityCheck,
        entities: aiIntent.entities || {},
        context: {
          hasHistory: conversationHistory.length > 0,
          hasProfile: Object.keys(candidateInfo).length > 0,
          messageLength: userMessage.length,
          complexity: this.calculateComplexity(normalizedMessage),
        },
      };

      console.log(`🧠 上下文分析完成:`, {
        intent: analysis.intent,
        techDirection: analysis.techDirection,
        stage: analysis.conversationStage,
      });

      return analysis;
    } catch (error) {
      throw new AIServiceError(`上下文分析失败: ${error.message}`);
    }
  }

  /**
   * 基础意图识别（规则基础）
   */
  detectBasicIntent(normalizedMessage) {
    for (const [intentType, patterns] of this.intentPatterns) {
      for (const pattern of patterns) {
        if (pattern.test(normalizedMessage)) {
          return {
            intent: intentType,
            confidence: 0.8,
            method: "rule-based",
          };
        }
      }
    }

    return {
      intent: INTENT_TYPES.GENERAL_CHAT,
      confidence: 0.5,
      method: "default",
    };
  }

  /**
   * AI增强意图分析
   */
  async performAIIntentAnalysis(
    userMessage,
    conversationHistory,
    candidateInfo
  ) {
    try {
      const contextPrompt = this.buildContextPrompt(
        conversationHistory,
        candidateInfo
      );

      const intentPrompt = `
你是一个专业的意图识别专家。请分析用户消息的意图。

可能的意图类型：
- greeting: 问候和开场
- profile_update: 更新个人信息
- job_inquiry: 询问职位信息
- recommendation_request: 请求职位推荐
- company_inquiry: 询问公司信息
- salary_discussion: 薪资相关讨论
- general_chat: 一般对话
- clarification: 澄清歧义
- resume_upload: 简历上传

${contextPrompt}

请返回JSON格式：
{
  "intent": "意图类型",
  "confidence": 0.95,
  "entities": {
    "技能": ["Java", "Python"],
    "位置": "北京",
    "薪资": "20k"
  },
  "reasoning": "判断理由"
}`;

      const response = await recognizeIntent(userMessage, intentPrompt, {
        temperature: 0.2,
        maxTokens: 400,
      });

      return this.parseAIIntentResponse(response);
    } catch (error) {
      console.warn("AI意图分析失败，使用基础识别:", error.message);
      return this.detectBasicIntent(normalizeText(userMessage));
    }
  }

  /**
   * 技术方向分析
   */
  analyzeTechDirection(normalizedMessage) {
    const detectedTechs = [];
    let maxConfidence = 0;
    let primaryDirection = null;

    // 遍历技术关键词
    for (const [techDirection, keywords] of this.techKeywords) {
      let techScore = 0;
      const matchedKeywords = [];

      for (const keyword of keywords) {
        if (normalizedMessage.includes(keyword.toLowerCase())) {
          techScore += 1;
          matchedKeywords.push(keyword);
        }
      }

      if (techScore > 0) {
        const confidence = Math.min(techScore / keywords.length, 1.0);
        detectedTechs.push({
          direction: techDirection,
          confidence,
          matchedKeywords,
        });

        if (confidence > maxConfidence) {
          maxConfidence = confidence;
          primaryDirection = techDirection;
        }
      }
    }

    return {
      direction: primaryDirection,
      confidence: maxConfidence,
      allDetected: detectedTechs,
      hasMultiple: detectedTechs.length > 1,
    };
  }

  /**
   * 对话阶段判断
   */
  determineConversationStage(conversationHistory, candidateInfo) {
    const historyLength = conversationHistory.length;
    const hasBasicInfo = candidateInfo.name && candidateInfo.experience_years;
    const hasSkills = candidateInfo.skills && candidateInfo.skills.length > 0;
    const hasPreferences =
      candidateInfo.job_preferences &&
      Object.keys(candidateInfo.job_preferences).length > 0;

    if (historyLength === 0) {
      return "greeting";
    } else if (historyLength < 3 && !hasBasicInfo) {
      return "information_gathering";
    } else if (hasBasicInfo && !hasSkills) {
      return "skill_exploration";
    } else if (hasSkills && !hasPreferences) {
      return "preference_setting";
    } else if (hasBasicInfo && hasSkills) {
      return "recommendation_ready";
    } else {
      return "ongoing_conversation";
    }
  }

  /**
   * 歧义检测
   */
  detectAmbiguity(normalizedMessage, techAnalysis) {
    const ambiguities = [];

    // 技术方向歧义
    if (techAnalysis.hasMultiple && techAnalysis.allDetected.length > 1) {
      const similarConfidences = techAnalysis.allDetected.filter(
        (tech) => Math.abs(tech.confidence - techAnalysis.confidence) < 0.2
      );

      if (similarConfidences.length > 1) {
        ambiguities.push({
          type: "tech_direction",
          options: similarConfidences.map((tech) => tech.direction),
          confidence: 0.8,
        });
      }
    }

    // 职级歧义检测
    const levelKeywords = ["初级", "中级", "高级", "资深", "专家", "架构师"];
    const detectedLevels = levelKeywords.filter((level) =>
      normalizedMessage.includes(level)
    );

    if (detectedLevels.length > 1) {
      ambiguities.push({
        type: "experience_level",
        options: detectedLevels,
        confidence: 0.7,
      });
    }

    return {
      hasAmbiguity: ambiguities.length > 0,
      ambiguities,
      needsClarification: ambiguities.some((amb) => amb.confidence > 0.7),
    };
  }

  /**
   * 构建意图模式
   */
  buildIntentPatterns() {
    return new Map([
      [INTENT_TYPES.GREETING, [/^(你好|hi|hello|嗨)/i, /(开始|开始聊天)/i]],
      [
        INTENT_TYPES.PROFILE_UPDATE,
        [/(我是|我叫|我的名字)/i, /(工作经验|工作了|从事)/i, /(技能|会|掌握)/i],
      ],
      [
        INTENT_TYPES.JOB_INQUIRY,
        [/(什么职位|有什么工作|职位信息)/i, /(招聘|岗位|工作机会)/i],
      ],
      [
        INTENT_TYPES.RECOMMENDATION_REQUEST,
        [/(推荐|介绍|找工作)/i, /(合适的|适合我)/i],
      ],
      [
        INTENT_TYPES.COMPANY_INQUIRY,
        [/(公司|企业|雇主)/i, /(怎么样|如何|评价)/i],
      ],
      [
        INTENT_TYPES.SALARY_DISCUSSION,
        [/(薪资|工资|薪水|待遇)/i, /(多少钱|收入)/i],
      ],
    ]);
  }

  /**
   * 构建技术关键词
   */
  buildTechKeywords() {
    return new Map([
      [
        "前端开发",
        [
          "前端",
          "frontend",
          "react",
          "vue",
          "angular",
          "javascript",
          "html",
          "css",
        ],
      ],
      [
        "后端开发",
        ["后端", "backend", "java", "python", "node.js", "go", "php", "spring"],
      ],
      [
        "移动开发",
        [
          "移动",
          "android",
          "ios",
          "flutter",
          "react native",
          "swift",
          "kotlin",
        ],
      ],
      [
        "数据科学",
        ["数据", "data", "python", "sql", "machine learning", "ai", "人工智能"],
      ],
      [
        "运维开发",
        ["运维", "devops", "docker", "kubernetes", "linux", "aws", "云计算"],
      ],
      ["测试开发", ["测试", "test", "qa", "automation", "自动化", "selenium"]],
    ]);
  }

  /**
   * 构建上下文提示
   */
  buildContextPrompt(conversationHistory, candidateInfo) {
    let contextPrompt = "";

    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-3);
      contextPrompt += "\n最近对话历史：\n";
      recentHistory.forEach((msg, index) => {
        contextPrompt += `${index + 1}. 用户: ${msg.user_message}\n`;
        contextPrompt += `   回复: ${msg.bot_response}\n`;
      });
    }

    if (candidateInfo && Object.keys(candidateInfo).length > 0) {
      contextPrompt += "\n候选人信息：\n";
      if (candidateInfo.name) contextPrompt += `姓名: ${candidateInfo.name}\n`;
      if (candidateInfo.experience_years)
        contextPrompt += `经验: ${candidateInfo.experience_years}年\n`;
      if (candidateInfo.skills)
        contextPrompt += `技能: ${candidateInfo.skills.join(", ")}\n`;
      if (candidateInfo.current_position)
        contextPrompt += `当前职位: ${candidateInfo.current_position}\n`;
    }

    return contextPrompt;
  }

  /**
   * 解析AI意图响应
   */
  parseAIIntentResponse(response) {
    try {
      const parsed = JSON.parse(response);
      return {
        intent: parsed.intent || INTENT_TYPES.GENERAL_CHAT,
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || {},
        reasoning: parsed.reasoning || "",
      };
    } catch (error) {
      console.warn("解析AI意图响应失败:", error.message);
      return {
        intent: INTENT_TYPES.GENERAL_CHAT,
        confidence: 0.3,
        entities: {},
        reasoning: "JSON解析失败",
      };
    }
  }

  /**
   * 计算消息复杂度
   */
  calculateComplexity(message) {
    const factors = {
      length: message.length > 100 ? 0.3 : 0.1,
      punctuation: (message.match(/[。！？；，]/g) || []).length * 0.1,
      numbers: (message.match(/\d+/g) || []).length * 0.1,
      english: (message.match(/[a-zA-Z]+/g) || []).length * 0.1,
    };

    return Math.min(
      Object.values(factors).reduce((sum, val) => sum + val, 0),
      1.0
    );
  }
}

// ==================== 信息提取器 ====================

export class InformationExtractor {
  constructor() {
    this.extractionRules = this.buildExtractionRules();
  }

  /**
   * 从用户消息中提取结构化信息
   */
  async extractInformation(userMessage, existingInfo = {}) {
    try {
      // 1. 规则基础提取
      const ruleBasedInfo = this.extractByRules(userMessage);

      // 2. AI增强提取
      const aiExtractedInfo = await this.extractByAI(userMessage, existingInfo);

      // 3. 合并和验证
      const mergedInfo = this.mergeExtractionResults(
        ruleBasedInfo,
        aiExtractedInfo,
        existingInfo
      );

      console.log(`📊 信息提取完成:`, mergedInfo);
      return mergedInfo;
    } catch (error) {
      throw new AIServiceError(`信息提取失败: ${error.message}`);
    }
  }

  /**
   * 规则基础信息提取
   */
  extractByRules(userMessage) {
    const extracted = {};
    const normalizedMessage = normalizeText(userMessage);

    // 提取姓名
    const nameMatch = normalizedMessage.match(
      /我(是|叫|的名字是)\s*([^\s，。！？]+)/
    );
    if (nameMatch) {
      extracted.name = nameMatch[2];
    }

    // 提取工作经验
    const expMatch = normalizedMessage.match(
      /(\d+)\s*(年|年的?)\s*(工作?经验|从事|工作)/
    );
    if (expMatch) {
      extracted.experience_years = parseInt(expMatch[1]);
    }

    // 提取技能
    const skillKeywords = [
      "java",
      "python",
      "javascript",
      "react",
      "vue",
      "spring",
      "mysql",
    ];
    const detectedSkills = skillKeywords.filter((skill) =>
      normalizedMessage.includes(skill.toLowerCase())
    );
    if (detectedSkills.length > 0) {
      extracted.skills = detectedSkills;
    }

    // 提取薪资期望
    const salaryMatch = normalizedMessage.match(
      /(\d+)\s*[kK万千]\s*(以上|左右|期望)?/
    );
    if (salaryMatch) {
      extracted.salary_expectation = this.parseSalaryRange(salaryMatch[0]);
    }

    // 提取位置
    const locationMatch = normalizedMessage.match(
      /(北京|上海|深圳|广州|杭州|成都|武汉|西安|南京|苏州)/
    );
    if (locationMatch) {
      extracted.location = locationMatch[1];
    }

    return extracted;
  }

  /**
   * AI增强信息提取
   */
  async extractByAI(userMessage, existingInfo) {
    try {
      const extractionPrompt = `
你是一个专业的信息提取专家。请从用户消息中提取结构化信息。

已有信息：
${JSON.stringify(existingInfo, null, 2)}

请提取以下信息（如果消息中包含）：
- name: 姓名
- experience_years: 工作年限（数字）
- skills: 技能列表
- current_position: 当前职位
- current_company: 当前公司
- education: 教育背景
- location: 工作地点
- salary_expectation: 薪资期望
- job_preferences: 工作偏好

请返回JSON格式，只包含消息中明确提到的信息：
{
  "name": "张三",
  "experience_years": 5,
  "skills": ["Java", "Spring"],
  "current_position": "高级开发工程师"
}`;

      const response = await extractInformation(userMessage, extractionPrompt, {
        temperature: 0.1,
        maxTokens: 500,
      });

      return this.parseExtractionResponse(response);
    } catch (error) {
      console.warn("AI信息提取失败，使用规则提取:", error.message);
      return {};
    }
  }

  /**
   * 合并提取结果
   */
  mergeExtractionResults(ruleBasedInfo, aiExtractedInfo, existingInfo) {
    const merged = { ...existingInfo };

    // 优先使用AI提取的结果，规则提取作为补充
    const sources = [aiExtractedInfo, ruleBasedInfo];

    for (const source of sources) {
      for (const [key, value] of Object.entries(source)) {
        if (value !== undefined && value !== null && value !== "") {
          if (key === "skills") {
            // 技能合并去重
            const existingSkills = merged.skills || [];
            const newSkills = Array.isArray(value) ? value : [value];
            merged.skills = [...new Set([...existingSkills, ...newSkills])];
          } else if (!merged[key]) {
            // 其他字段只在不存在时添加
            merged[key] = value;
          }
        }
      }
    }

    return merged;
  }

  /**
   * 解析提取响应
   */
  parseExtractionResponse(response) {
    try {
      const parsed = JSON.parse(response);

      // 验证和清理数据
      const cleaned = {};

      if (parsed.name && typeof parsed.name === "string") {
        cleaned.name = parsed.name.trim();
      }

      if (parsed.experience_years && !isNaN(parsed.experience_years)) {
        cleaned.experience_years = parseInt(parsed.experience_years);
      }

      if (parsed.skills && Array.isArray(parsed.skills)) {
        cleaned.skills = parsed.skills.filter(
          (skill) => typeof skill === "string" && skill.trim().length > 0
        );
      }

      if (
        parsed.current_position &&
        typeof parsed.current_position === "string"
      ) {
        cleaned.current_position = parsed.current_position.trim();
      }

      if (parsed.location && typeof parsed.location === "string") {
        cleaned.location = parsed.location.trim();
      }

      return cleaned;
    } catch (error) {
      console.warn("解析提取响应失败:", error.message);
      return {};
    }
  }

  /**
   * 解析薪资范围
   */
  parseSalaryRange(salaryStr) {
    const normalized = salaryStr.toLowerCase().replace(/\s/g, "");

    let amount = 0;
    const numberMatch = normalized.match(/(\d+)/);
    if (numberMatch) {
      amount = parseInt(numberMatch[1]);
    }

    // 处理单位
    if (normalized.includes("万")) {
      amount *= 10000;
    } else if (normalized.includes("k")) {
      amount *= 1000;
    } else if (normalized.includes("千")) {
      amount *= 1000;
    }

    return {
      min: amount * 0.8,
      max: amount * 1.2,
      target: amount,
      original: salaryStr,
    };
  }

  /**
   * 构建提取规则
   */
  buildExtractionRules() {
    return {
      name: [
        /我(是|叫|的名字是)\s*([^\s，。！？]+)/,
        /叫我\s*([^\s，。！？]+)/,
      ],
      experience: [
        /(\d+)\s*(年|年的?)\s*(工作?经验|从事|工作)/,
        /(工作|从事)\s*(\d+)\s*年/,
      ],
      skills: [
        /(会|掌握|熟悉|使用)\s*([^，。！？]+)/,
        /(技能|技术栈|语言)[:：]\s*([^，。！？]+)/,
      ],
      position: [
        /(目前|现在|当前)\s*(是|在|做)\s*([^，。！？]+)/,
        /(职位|岗位|工作)[:：]\s*([^，。！？]+)/,
      ],
    };
  }
}

// ==================== 响应生成器 ====================

export class ResponseGenerator {
  constructor() {
    this.responseTemplates = this.buildResponseTemplates();
  }

  /**
   * 生成AI响应
   */
  async generateResponse(
    userMessage,
    contextAnalysis,
    candidateInfo,
    conversationHistory = []
  ) {
    try {
      const responseContext = this.buildResponseContext(
        contextAnalysis,
        candidateInfo,
        conversationHistory
      );

      const responsePrompt = this.selectResponsePrompt(contextAnalysis.intent);

      const response = await generateResponse(
        userMessage,
        responseContext,
        responsePrompt,
        {
          temperature: 0.8,
          maxTokens: 600,
        }
      );

      const processedResponse = this.postProcessResponse(
        response,
        contextAnalysis
      );

      console.log(`💬 生成响应完成: ${contextAnalysis.intent}`);
      return processedResponse;
    } catch (error) {
      throw new AIServiceError(`响应生成失败: ${error.message}`);
    }
  }

  /**
   * 构建响应上下文
   */
  buildResponseContext(contextAnalysis, candidateInfo, conversationHistory) {
    let context = `
对话意图: ${contextAnalysis.intent}
对话阶段: ${contextAnalysis.conversationStage}
技术方向: ${contextAnalysis.techDirection || "未确定"}
`;

    if (candidateInfo && Object.keys(candidateInfo).length > 0) {
      context += `\n候选人信息:\n`;
      if (candidateInfo.name) context += `- 姓名: ${candidateInfo.name}\n`;
      if (candidateInfo.experience_years)
        context += `- 经验: ${candidateInfo.experience_years}年\n`;
      if (candidateInfo.skills)
        context += `- 技能: ${candidateInfo.skills.join(", ")}\n`;
      if (candidateInfo.current_position)
        context += `- 当前职位: ${candidateInfo.current_position}\n`;
    }

    if (conversationHistory.length > 0) {
      context += `\n最近对话:\n`;
      const recent = conversationHistory.slice(-2);
      recent.forEach((msg, index) => {
        context += `${index + 1}. 用户: ${msg.user_message}\n`;
      });
    }

    return context;
  }

  /**
   * 选择响应提示模板
   */
  selectResponsePrompt(intent) {
    return this.responseTemplates[intent] || this.responseTemplates.default;
  }

  /**
   * 后处理响应
   */
  postProcessResponse(response, contextAnalysis) {
    let processed = response.trim();

    // 移除可能的JSON格式包装
    if (processed.startsWith("{") && processed.endsWith("}")) {
      try {
        const parsed = JSON.parse(processed);
        processed = parsed.response || parsed.message || processed;
      } catch (e) {
        // 保持原样
      }
    }

    // 确保响应以合适的语气结尾
    if (!processed.match(/[。！？]$/)) {
      processed += "。";
    }

    return processed;
  }

  /**
   * 构建响应模板
   */
  buildResponseTemplates() {
    return {
      [INTENT_TYPES.GREETING]: `
作为专业的AI猎头顾问Katrina，请给出热情专业的问候回复。
要求：
- 简洁友好的问候
- 简单介绍自己的作用
- 引导用户开始对话
`,

      [INTENT_TYPES.PROFILE_UPDATE]: `
作为专业的AI猎头顾问，请确认用户提供的信息并给出积极回应。
要求：
- 确认收到的信息
- 如果信息不完整，友好地询问缺失部分
- 鼓励用户继续分享
`,

      [INTENT_TYPES.JOB_INQUIRY]: `
作为专业的AI猎头顾问，请回应用户的职位询问。
要求：
- 了解用户的具体需求
- 如果信息不足，询问更多细节
- 准备为用户提供个性化推荐
`,

      [INTENT_TYPES.RECOMMENDATION_REQUEST]: `
作为专业的AI猎头顾问，请为用户提供职位推荐建议。
要求：
- 基于用户背景给出专业建议
- 如果信息不足，先收集必要信息
- 提供具体可行的建议
`,

      default: `
作为专业的AI猎头顾问Katrina，请给出自然、专业的回复。
要求：
- 保持专业友好的语气
- 根据上下文给出相关回应
- 适当引导对话向求职咨询方向发展
`,
    };
  }
}

// ==================== 导出实例 ====================

export const contextAnalyzer = new ContextAnalyzer();
export const informationExtractor = new InformationExtractor();
export const responseGenerator = new ResponseGenerator();

export default {
  ContextAnalyzer,
  InformationExtractor,
  ResponseGenerator,
  contextAnalyzer,
  informationExtractor,
  responseGenerator,
};
