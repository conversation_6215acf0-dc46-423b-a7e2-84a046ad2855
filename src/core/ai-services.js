/**
 * AI服务模块
 * 负责AI分析、对话生成、意图识别等功能
 */

import { AI_CONFIG } from "../config/app-config.js";
import { INTENT_TYPES, SEMANTIC_MAPPINGS } from "../config/mapping-tables.js";

// ==================== AI服务管理器 ====================

export class AIServices {
  constructor() {
    this.config = AI_CONFIG;
    this.apiTiers = AI_CONFIG.apiTiers;
  }

  // ==================== 上下文分析 ====================

  /**
   * AI驱动的上下文分析
   * 对应业务逻辑: analyzeContext(userMessage, conversationHistory, candidateInfo)
   */
  async analyzeContext(userMessage, conversationHistory, candidateInfo) {
    try {
      // 构建上下文分析提示词
      const contextPrompt = this.buildContextAnalysisPrompt(
        userMessage,
        conversationHistory,
        candidateInfo
      );

      // 调用DeepSeek进行意图分析
      const response = await this.callDeepSeek(contextPrompt, {
        temperature: 0.3,
        maxTokens: 300,
      });

      // 解析AI返回的分析结果
      const analysis = this.parseContextAnalysis(response.content);

      // 添加额外的上下文信息
      analysis.tokensUsed = response.tokensUsed || 200;
      analysis.apiTier = "deepseek_medium";
      analysis.timestamp = new Date().toISOString();

      return analysis;
    } catch (error) {
      console.error("上下文分析失败:", error);
      // 回退到规则引擎分析
      return this.fallbackContextAnalysis(
        userMessage,
        conversationHistory,
        candidateInfo
      );
    }
  }

  /**
   * 构建上下文分析提示词
   */
  buildContextAnalysisPrompt(userMessage, conversationHistory, candidateInfo) {
    const historyContext = this.formatHistoryForContext(conversationHistory);
    const candidateContext = this.formatCandidateInfo(candidateInfo);

    return `
作为AI招聘助手的上下文分析引擎，请分析用户消息的意图和上下文。

用户消息: "${userMessage}"

对话历史:
${historyContext}

候选人信息:
${candidateContext}

请返回JSON格式的分析结果：
{
  "intent": "主要意图类型",
  "confidence": 0.0-1.0,
  "entities": {
    "techDirection": "技术方向或null",
    "company": "公司名称或null",
    "salary": "薪资信息或null",
    "location": "地点信息或null",
    "preference": "偏好类型或null"
  },
  "context": {
    "isThirdParty": false,
    "needsRecommendation": false,
    "hasAmbiguity": false,
    "conversationStage": "阶段描述"
  },
  "thirdPartyInfo": {
    "targetPerson": "目标人员或null",
    "relationship": "关系或null",
    "techDirection": "技术方向或null"
  }
}

意图类型包括：
- job_recommendation: 职位推荐请求
- job_question: 职位相关询问
- preference_expression: 偏好表达
- info_collection: 信息收集
- third_party_inquiry: 第三方询问
- user_background_update: 背景更新
- resume_upload: 简历上传
- general_chat: 通用对话
- job_related_inquiry: 职位相关询问
- company_inquiry: 公司询问
- detailed_jd_inquiry: 详细JD询问
- second_recommendation: 二次推荐
`;
  }

  /**
   * 解析AI返回的上下文分析结果
   */
  parseContextAnalysis(aiResponse) {
    try {
      const analysis = JSON.parse(aiResponse);

      // 验证和标准化分析结果
      return {
        intent: analysis.intent || "general_chat",
        confidence: Math.max(0, Math.min(1, analysis.confidence || 0.5)),
        entities: analysis.entities || {},
        context: analysis.context || {},
        thirdPartyInfo: analysis.thirdPartyInfo || {},
        rawResponse: aiResponse,
      };
    } catch (error) {
      console.error("解析上下文分析结果失败:", error);
      return this.getDefaultContextAnalysis();
    }
  }

  /**
   * 回退上下文分析 - 基于规则引擎
   */
  fallbackContextAnalysis(userMessage, conversationHistory, candidateInfo) {
    const analysis = {
      intent: "general_chat",
      confidence: 0.6,
      entities: {},
      context: {},
      thirdPartyInfo: {},
      apiTier: "rules_engine",
    };

    // 基于关键词的意图识别
    if (this.detectJobInquiry(userMessage)) {
      analysis.intent = "job_recommendation";
      analysis.confidence = 0.8;
    } else if (this.isCompanyInquiry(userMessage)) {
      analysis.intent = "company_inquiry";
      analysis.confidence = 0.7;
    } else if (this.detectUserPreference(userMessage)) {
      analysis.intent = "preference_expression";
      analysis.confidence = 0.7;
    }

    // 检测第三方询问
    const thirdPartyPatterns = [
      /我(朋友|同事|同学|室友)/,
      /帮.*?(朋友|同事|同学|室友)/,
      /有个(朋友|同事|同学|室友)/,
    ];

    if (thirdPartyPatterns.some((pattern) => pattern.test(userMessage))) {
      analysis.intent = "third_party_inquiry";
      analysis.context.isThirdParty = true;
    }

    return analysis;
  }

  // ==================== 信息提取 ====================

  /**
   * AI驱动的信息提取
   * 对应业务逻辑: extractInformation(userMessage, existingInfo)
   */
  async extractInformation(userMessage, existingInfo) {
    try {
      const prompt = this.buildExtractionPrompt(userMessage, existingInfo);

      const response = await this.callDeepSeek(prompt, {
        temperature: 0.2,
        maxTokens: 400,
      });

      const extractedInfo = this.parseExtractionResult(response.content);

      // 添加元数据
      extractedInfo.tokensUsed = response.tokensUsed || 200;
      extractedInfo.model = "deepseek";
      extractedInfo.extractionTimestamp = new Date().toISOString();

      return extractedInfo;
    } catch (error) {
      console.error("AI信息提取失败:", error);
      // 回退到规则引擎提取
      return this.fallbackExtractInformation(userMessage, existingInfo);
    }
  }

  /**
   * 构建信息提取提示词
   */
  buildExtractionPrompt(userMessage, existingInfo) {
    const existingContext = existingInfo
      ? JSON.stringify(existingInfo, null, 2)
      : "无现有信息";

    return `
作为专业的信息提取引擎，请从用户消息中提取候选人的职业信息。

用户消息: "${userMessage}"

现有候选人信息:
${existingContext}

请严格按照以下JSON格式返回提取结果：
{
  "company": "公司名称或null",
  "level": "职级信息或null",
  "techDirection": "技术方向或null",
  "expectedSalary": "期望薪资或null",
  "location": "期望地点或null",
  "businessScenario": "业务场景或null",
  "isGraduate": false,
  "experience": "工作经验或null",
  "education": "教育背景或null"
}

提取规则：
1. 公司名称：提取当前或之前工作的公司名称
2. 职级：P级、T级、高级、资深、专家、总监等职级信息
3. 技术方向：算法、开发、测试、产品等技术领域
4. 期望薪资：包含数字的薪资表达，保留原始格式
5. 地点：城市、区域等地理位置信息
6. 业务场景：电商、金融、游戏等业务领域
7. 应届生：判断是否为应届毕业生
8. 工作经验：年限、项目经验等
9. 教育背景：学历、学校、专业等

注意：
- 如果信息不明确或不存在，设置为null
- 保持原始表达方式，不要过度解释
- 应届生判断基于关键词：应届、校招、毕业、实习等
`;
  }

  /**
   * 解析AI提取结果
   */
  parseExtractionResult(aiResponse) {
    try {
      const result = JSON.parse(aiResponse);

      // 验证和标准化结果
      return {
        company: result.company || null,
        level: result.level || null,
        techDirection: result.techDirection || null,
        expectedSalary: result.expectedSalary || null,
        location: result.location || null,
        businessScenario: result.businessScenario || null,
        isGraduate: Boolean(result.isGraduate),
        experience: result.experience || null,
        education: result.education || null,
        rawResponse: aiResponse,
      };
    } catch (error) {
      console.error("解析提取结果失败:", error);
      return this.getEmptyExtractionResult();
    }
  }

  /**
   * 回退信息提取 - 基于规则引擎
   */
  fallbackExtractInformation(userMessage, existingInfo) {
    const result = this.getEmptyExtractionResult();
    result.model = "rules_engine";

    // 公司名称提取
    const companyPatterns = [
      /在(.+?)(工作|上班|任职)/,
      /(.+?)(公司|集团|科技|技术)/,
      /从(.+?)跳槽/,
    ];

    for (const pattern of companyPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.company = match[1].trim();
        break;
      }
    }

    // 职级提取
    const levelPatterns = [
      /(P\d+|T\d+)/i,
      /(高级|资深|专家|总监|经理|主管)/,
      /(\d+年经验)/,
    ];

    for (const pattern of levelPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.level = match[1];
        break;
      }
    }

    // 技术方向提取
    const techPatterns = [
      /(算法|开发|测试|产品|运维|数据|前端|后端|全栈)/,
      /(Java|Python|JavaScript|Go|C\+\+|PHP)/i,
      /(推荐|搜索|广告|CV|NLP|大模型)/,
    ];

    for (const pattern of techPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.techDirection = match[1];
        break;
      }
    }

    // 薪资提取
    const salaryPatterns = [/(\d+[万k千])/, /(\d+-\d+[万k千])/, /(年薪\d+)/];

    for (const pattern of salaryPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.expectedSalary = match[1];
        break;
      }
    }

    // 地点提取
    const locationPatterns = [
      /(北京|上海|深圳|杭州|广州|成都|南京|武汉|西安|苏州)/,
      /在(.+?)工作/,
    ];

    for (const pattern of locationPatterns) {
      const match = userMessage.match(pattern);
      if (match) {
        result.location = match[1];
        break;
      }
    }

    // 应届生判断
    const graduateKeywords = ["应届", "校招", "毕业", "实习", "学生"];
    result.isGraduate = graduateKeywords.some((keyword) =>
      userMessage.includes(keyword)
    );

    return result;
  }

  /**
   * 获取空的提取结果
   */
  getEmptyExtractionResult() {
    return {
      company: null,
      level: null,
      techDirection: null,
      expectedSalary: null,
      location: null,
      businessScenario: null,
      isGraduate: false,
      experience: null,
      education: null,
      tokensUsed: 0,
    };
  }

  // ==================== 对话生成 ====================

  /**
   * 生成对话回复
   * 对应业务逻辑: generateConversationReply(userMessage, candidateInfo)
   */
  async generateConversationReply(userMessage, candidateInfo) {
    try {
      const prompt = `
作为专业的AI招聘顾问Katrina，请回复用户的消息。

用户消息: ${userMessage}
候选人背景: ${JSON.stringify(candidateInfo)}

要求：
1. 保持专业、友好的语调
2. 如果用户询问职位相关问题，引导提供更多背景信息
3. 回复简洁明了，不超过100字
4. 体现AI招聘顾问的专业性

请直接返回回复内容，不要包含其他格式。
`;

      const response = await this.callDeepSeek(prompt, {
        temperature: 0.7,
        maxTokens: 150,
      });

      return {
        reply: response.content || "感谢您的消息，我会尽力为您提供帮助。",
        tokensUsed: response.tokensUsed || 100,
        apiTier: "deepseek_medium",
      };
    } catch (error) {
      console.error("生成对话回复失败:", error);
      return {
        reply: "感谢您的消息，我正在为您处理...",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 调用DeepSeek API
   */
  async callDeepSeek(prompt, options = {}) {
    // 这里应该实现实际的DeepSeek API调用
    // 暂时返回模拟响应
    return {
      content: '{"intent": "general_chat", "confidence": 0.8}',
      tokensUsed: options.maxTokens || 200,
    };
  }

  /**
   * 格式化对话历史用于上下文分析
   */
  formatHistoryForContext(conversationHistory) {
    if (!conversationHistory || conversationHistory.length === 0) {
      return "无对话历史";
    }

    // 只取最近5轮对话
    const recentHistory = conversationHistory.slice(-10);

    return recentHistory
      .map((msg) => {
        const role = msg.message_type === "user" ? "用户" : "AI";
        return `${role}: ${msg.message_content}`;
      })
      .join("\n");
  }

  /**
   * 格式化候选人信息用于上下文分析
   */
  formatCandidateInfo(candidateInfo) {
    if (!candidateInfo) {
      return "无候选人信息";
    }

    const info = [];
    if (candidateInfo.candidate_tech_direction_raw) {
      info.push(`技术方向: ${candidateInfo.candidate_tech_direction_raw}`);
    }
    if (candidateInfo.current_company_name_raw) {
      info.push(`当前公司: ${candidateInfo.current_company_name_raw}`);
    }
    if (candidateInfo.candidate_level_raw) {
      info.push(`职级: ${candidateInfo.candidate_level_raw}`);
    }
    if (candidateInfo.expected_compensation_raw) {
      info.push(`期望薪资: ${candidateInfo.expected_compensation_raw}`);
    }
    if (candidateInfo.desired_location_raw) {
      info.push(`期望地点: ${candidateInfo.desired_location_raw}`);
    }

    return info.length > 0 ? info.join(", ") : "候选人信息不完整";
  }

  /**
   * 获取默认上下文分析结果
   */
  getDefaultContextAnalysis() {
    return {
      intent: "general_chat",
      confidence: 0.5,
      entities: {},
      context: {},
      thirdPartyInfo: {},
      apiTier: "rules_engine",
    };
  }

  /**
   * 检测职位询问
   */
  detectJobInquiry(userMessage) {
    const jobKeywords = ["职位", "工作", "推荐", "岗位", "机会"];
    return jobKeywords.some((keyword) => userMessage.includes(keyword));
  }

  /**
   * 检测公司询问
   */
  isCompanyInquiry(userMessage) {
    const companyKeywords = ["公司", "企业", "薪资结构", "待遇"];
    return companyKeywords.some((keyword) => userMessage.includes(keyword));
  }

  /**
   * 检测用户偏好
   */
  detectUserPreference(userMessage) {
    const preferenceKeywords = ["喜欢", "偏好", "希望", "想要", "不要"];
    return preferenceKeywords.some((keyword) => userMessage.includes(keyword));
  }
}

// 导出单例实例
export const aiServices = new AIServices();
