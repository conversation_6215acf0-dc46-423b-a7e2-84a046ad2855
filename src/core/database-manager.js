/**
 * 数据库管理器
 * 负责所有数据库操作，包括用户管理、会话管理、职位查询等
 */

import { createClient } from '@supabase/supabase-js';
import { DATABASE_CONFIG, ENV_CONFIG } from '../config/app-config.js';

// ==================== 数据库管理器 ====================

export class DatabaseManager {
  constructor() {
    this.supabase = createClient(
      ENV_CONFIG.SUPABASE_URL,
      ENV_CONFIG.SUPABASE_ANON_KEY
    );
    this.tables = DATABASE_CONFIG.tables;
  }

  // ==================== 会话管理 ====================

  /**
   * 获取或创建会话
   * 对应业务逻辑: getOrCreateSession(sessionUuid)
   */
  async getOrCreateSession(sessionUuid) {
    try {
      // 1. 尝试获取现有会话
      const { data: existingSession } = await this.supabase
        .from(this.tables.chatSessions)
        .select('*')
        .eq('session_uuid', sessionUuid)
        .single();

      if (existingSession) {
        return existingSession;
      }

      // 2. 创建新会话
      const { data: newSession, error } = await this.supabase
        .from(this.tables.chatSessions)
        .insert({
          session_uuid: sessionUuid,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return newSession;
    } catch (error) {
      console.error('获取或创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取对话历史
   * 对应业务逻辑: getConversationHistory(sessionUuid, limit)
   */
  async getConversationHistory(sessionUuid, limit = 20) {
    try {
      // 1. 获取会话ID
      const session = await this.getOrCreateSession(sessionUuid);
      
      // 2. 查询消息历史
      const { data, error } = await this.supabase
        .from(this.tables.chatMessages)
        .select('*')
        .eq('session_id', session.id)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data ? data.reverse() : [];
    } catch (error) {
      console.error('获取对话历史失败:', error);
      return [];
    }
  }

  /**
   * 保存对话消息
   * 对应业务逻辑: saveConversation(sessionUuid, userMessage, botResponse, metadata)
   */
  async saveConversation(sessionUuid, userMessage, botResponse, metadata = {}) {
    try {
      const session = await this.getOrCreateSession(sessionUuid);
      
      const messages = [
        {
          session_id: session.id,
          message_type: 'user',
          message_content: userMessage,
          timestamp: new Date().toISOString(),
          metadata_json: {}
        },
        {
          session_id: session.id,
          message_type: 'assistant',
          message_content: botResponse,
          timestamp: new Date().toISOString(),
          metadata_json: metadata
        }
      ];

      const { error } = await this.supabase
        .from(this.tables.chatMessages)
        .insert(messages);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('保存对话失败:', error);
      return false;
    }
  }

  // ==================== 用户和候选人管理 ====================

  /**
   * 获取候选人信息
   * 对应业务逻辑: getCandidateInfo(userId)
   */
  async getCandidateInfo(userId) {
    try {
      // 1. 查询用户信息
      let { data: user } = await this.supabase
        .from(this.tables.users)
        .select('*')
        .eq('id', userId)
        .single();

      // 2. 如果用户不存在，创建新用户
      if (!user) {
        const { data: newUser, error: userError } = await this.supabase
          .from(this.tables.users)
          .insert({
            id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (userError) throw userError;
        user = newUser;
      }

      // 3. 查询候选人档案
      let { data: candidate } = await this.supabase
        .from(this.tables.candidates)
        .select('*')
        .eq('user_id', userId)
        .single();

      // 4. 如果候选人档案不存在，创建新档案
      if (!candidate) {
        const { data: newCandidate, error: candidateError } = await this.supabase
          .from(this.tables.candidates)
          .insert({
            user_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (candidateError) throw candidateError;
        candidate = newCandidate;
      }

      return { user, candidate };
    } catch (error) {
      console.error('获取候选人信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新候选人档案
   * 对应业务逻辑: updateCandidateProfile(userId, updateData)
   */
  async updateCandidateProfile(userId, updateData) {
    try {
      const { data, error } = await this.supabase
        .from(this.tables.candidates)
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('更新候选人档案失败:', error);
      throw error;
    }
  }

  // ==================== 职位查询 ====================

  /**
   * 查询匹配的职位
   * 对应业务逻辑: queryMatchingJobs(candidateInfo, recommendationType)
   */
  async queryMatchingJobs(candidateInfo, recommendationType = 'passive') {
    try {
      let query = this.supabase
        .from(this.tables.jobListings)
        .select(`
          *,
          companies (
            company_name,
            company_type,
            company_size
          ),
          tech_tree (
            tech_name,
            parent_id
          )
        `)
        .eq('is_active', true);

      // 根据候选人信息添加过滤条件
      if (candidateInfo.primary_tech_direction_id) {
        query = query.eq('primary_tech_direction_id', candidateInfo.primary_tech_direction_id);
      }

      if (candidateInfo.expected_compensation_min) {
        query = query.gte('salary_max', candidateInfo.expected_compensation_min);
      }

      if (candidateInfo.expected_compensation_max) {
        query = query.lte('salary_min', candidateInfo.expected_compensation_max);
      }

      if (candidateInfo.desired_location_raw) {
        query = query.ilike('job_location', `%${candidateInfo.desired_location_raw}%`);
      }

      // 限制结果数量
      query = query.limit(100);

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('查询匹配职位失败:', error);
      return [];
    }
  }

  /**
   * 获取公司信息
   * 对应业务逻辑: getCompanyInfo(companyName)
   */
  async getCompanyInfo(companyName) {
    try {
      const { data, error } = await this.supabase
        .from(this.tables.companies)
        .select('*')
        .ilike('company_name', `%${companyName}%`)
        .limit(10);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('获取公司信息失败:', error);
      return [];
    }
  }

  /**
   * 获取技术方向信息
   * 对应业务逻辑: getTechDirectionInfo(techName)
   */
  async getTechDirectionInfo(techName) {
    try {
      const { data, error } = await this.supabase
        .from(this.tables.techTree)
        .select('*')
        .or(`tech_name.ilike.%${techName}%,description.ilike.%${techName}%`)
        .limit(20);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('获取技术方向信息失败:', error);
      return [];
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 检查数据库连接
   */
  async checkConnection() {
    try {
      const { data, error } = await this.supabase
        .from(this.tables.users)
        .select('count')
        .limit(1);

      return !error;
    } catch (error) {
      console.error('数据库连接检查失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const databaseManager = new DatabaseManager();
