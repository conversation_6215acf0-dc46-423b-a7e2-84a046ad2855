// 空文件 - 等待业务逻辑指令
    job_recommendation: "handleJobRecommendation",
    job_question: "handleJobQuestion",
    preference_expression: "handlePreferenceExpression",
    info_collection: "handleInfoCollection",
    third_party_inquiry: "handleThirdPartyInquiry",
    background_update: "handleUserBackgroundUpdate",
    resume_upload: "handleResumeUpload",
    general_conversation: "handleGeneralChat",
    job_related_inquiry: "handleJobRelatedInquiry",
    company_inquiry: "handleCompanyInquiry",
    detailed_jd_request: "handleDetailedJDInquiry",
    second_recommendation: "handleSecondRecommendationInquiry",
  };

  return handlerMap[intent] || "handleGeneralChat";
}

/**
 * 检查对话历史中是否已存在推荐记录
 * @param {Array} conversationHistory - 对话历史
 * @returns {boolean} 是否存在推荐记录
 */
function checkRecommendationsInHistory(conversationHistory) {
  if (!Array.isArray(conversationHistory)) return false;

  return conversationHistory.some((message) => {
    // 检查消息中是否包含职位推荐的标识
    return (
      message.type === "recommendation" ||
      message.metadata?.hasRecommendations === true ||
      (message.response && message.response.includes("为您推荐")) ||
      (message.response && message.response.includes("职位"))
    );
  });
}

// ==================== 辅助函数 ====================

/**
 * 识别问题类型
 * @param {string} userMessage - 用户消息
 * @returns {string} 问题类型
 */
function identifyQuestionType(userMessage) {
  const message = userMessage.toLowerCase();

  if (message.includes("公司") || message.includes("企业")) {
    return "company_inquiry";
  }
  if (
    message.includes("薪资") ||
    message.includes("工资") ||
    message.includes("薪水")
  ) {
    return "salary_inquiry";
  }
  if (
    message.includes("详细") ||
    message.includes("具体") ||
    message.includes("JD")
  ) {
    return "job_detail_inquiry";
  }

  return "general_inquiry";
}

/**
 * 从用户问题中提取公司名称
 * @param {string} userMessage - 用户消息
 * @returns {string} 公司名称
 */
function extractCompanyFromQuestion(userMessage) {
  // 简化实现，实际应该使用更复杂的NLP
  const companyPatterns = [
    /(\w+)公司/,
    /(\w+)科技/,
    /(\w+)集团/,
    /(阿里|腾讯|百度|字节|美团|滴滴|京东)/,
  ];

  for (const pattern of companyPatterns) {
    const match = userMessage.match(pattern);
    if (match) {
      return match[1] || match[0];
    }
  }

  return "";
}

/**
 * 从消息中提取职位ID
 * @param {string} userMessage - 用户消息
 * @returns {string} 职位ID
 */
function extractJobIdFromMessage(userMessage) {
  const jobIdPattern = /职位(\d+)|第(\d+)个|(\d+)号/;
  const match = userMessage.match(jobIdPattern);
  return match ? match[1] || match[2] || match[3] : "";
}

/**
 * 格式化公司信息
 * @param {Object} companyInfo - 公司信息
 * @returns {string} 格式化的公司信息
 */
function formatCompanyInfo(companyInfo) {
  if (!companyInfo) {
    return "抱歉，我没有找到该公司的详细信息。";
  }

  return `${companyInfo.name}是一家${companyInfo.type}，主要业务包括${companyInfo.business}。公司规模约${companyInfo.size}人，发展前景${companyInfo.prospect}。`;
}

/**
 * 格式化薪资信息
 * @param {Object} salaryInfo - 薪资信息
 * @returns {string} 格式化的薪资信息
 */
function formatSalaryInfo(salaryInfo) {
  if (!salaryInfo) {
    return "抱歉，暂时没有该技术方向的薪资数据。";
  }

  return `根据市场数据，${salaryInfo.techDirection}在${salaryInfo.location}的薪资范围大约是${salaryInfo.minSalary}-${salaryInfo.maxSalary}万元。`;
}

/**
 * 格式化职位详情
 * @param {Object} jobDetail - 职位详情
 * @returns {string} 格式化的职位详情
 */
function formatJobDetail(jobDetail) {
  if (!jobDetail) {
    return "抱歉，没有找到该职位的详细信息。";
  }

  return `职位：${jobDetail.title}\n公司：${jobDetail.company}\n薪资：${jobDetail.salary}\n要求：${jobDetail.requirements}\n职责：${jobDetail.responsibilities}`;
}

/**
 * 生成偏好确认消息
 * @param {Object} preferences - 偏好信息
 * @returns {string} 确认消息
 */
function generatePreferenceConfirmation(preferences) {
  const confirmations = [];

  if (preferences.companyType) {
    confirmations.push(`您偏好${preferences.companyType}类型的公司`);
  }
  if (preferences.location) {
    confirmations.push(`工作地点倾向于${preferences.location}`);
  }
  if (preferences.salary) {
    confirmations.push(`期望薪资${preferences.salary}`);
  }

  return confirmations.length > 0
    ? `我已记录您的偏好：${confirmations.join("，")}。`
    : "我已记录您的偏好信息。";
}

/**
 * 验证提取的信息
 * @param {Object} extractedInfo - 提取的信息
 * @returns {Object} 验证结果
 */
function validateExtractedInfo(extractedInfo) {
  const errors = [];

  if (
    extractedInfo.workExperience &&
    (extractedInfo.workExperience < 0 || extractedInfo.workExperience > 50)
  ) {
    errors.push("工作经验年限不合理");
  }

  if (extractedInfo.expectedSalary && extractedInfo.expectedSalary < 0) {
    errors.push("期望薪资不能为负数");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 生成下一个问题
 * @param {Object} candidateInfo - 候选人信息
 * @param {Object} extractedInfo - 新提取的信息
 * @returns {string} 下一个问题
 */
function generateNextQuestion(candidateInfo, extractedInfo) {
  const combined = { ...candidateInfo, ...extractedInfo };

  if (!combined.techDirection) {
    return "请告诉我您的技术方向，比如前端、后端、移动端等？";
  }
  if (!combined.workExperience) {
    return "您有多少年的工作经验？";
  }
  if (!combined.location) {
    return "您希望在哪个城市工作？";
  }
  if (!combined.expectedSalary) {
    return "您的期望薪资范围是多少？";
  }

  return "信息收集完成，我现在可以为您推荐合适的职位了！";
}

/**
 * 提取关系信息
 * @param {string} userMessage - 用户消息
 * @returns {string} 关系描述
 */
function extractRelationship(userMessage) {
  const relationships = {
    朋友: ["朋友", "同学", "室友"],
    家人: ["老婆", "老公", "妻子", "丈夫", "儿子", "女儿", "父亲", "母亲"],
    同事: ["同事", "同事", "领导", "下属"],
  };

  for (const [type, keywords] of Object.entries(relationships)) {
    if (keywords.some((keyword) => userMessage.includes(keyword))) {
      return type;
    }
  }

  return "朋友";
}

/**
 * 提取目标人员信息
 * @param {string} userMessage - 用户消息
 * @returns {Object} 目标人员信息
 */
function extractTargetPersonInfo(userMessage) {
  // 简化实现，实际应该使用更复杂的NLP
  const info = {
    techDirection: null,
    workExperience: null,
    location: null,
    isComplete: false,
  };

  // 提取技术方向
  const techKeywords = ["前端", "后端", "Java", "Python", "React", "Vue"];
  for (const tech of techKeywords) {
    if (userMessage.includes(tech)) {
      info.techDirection = tech;
      break;
    }
  }

  // 提取工作经验
  const expMatch = userMessage.match(/(\d+)年/);
  if (expMatch) {
    info.workExperience = parseInt(expMatch[1]);
  }

  // 提取地点
  const cities = ["北京", "上海", "深圳", "杭州", "广州"];
  for (const city of cities) {
    if (userMessage.includes(city)) {
      info.location = city;
      break;
    }
  }

  info.isComplete =
    info.techDirection && info.workExperience !== null && info.location;

  return info;
}

/**
 * 识别缺失信息
 * @param {Object} targetPersonInfo - 目标人员信息
 * @returns {Array} 缺失信息列表
 */
function identifyMissingInfo(targetPersonInfo) {
  const missing = [];

  if (!targetPersonInfo.techDirection) {
    missing.push("技术方向");
  }
  if (targetPersonInfo.workExperience === null) {
    missing.push("工作经验");
  }
  if (!targetPersonInfo.location) {
    missing.push("工作地点");
  }

  return missing;
}

/**
 * 格式化推荐结果
 * @param {Object} recommendations - 推荐结果
 * @returns {string} 格式化的推荐文本
 */
function formatRecommendations(recommendations) {
  if (!recommendations || !recommendations.jobs) {
    return "暂时没有合适的职位推荐。";
  }

  return recommendations.jobs
    .map(
      (job, index) =>
        `${index + 1}. ${job.title} - ${job.company} (${job.salary})`
    )
    .join("\n");
}

/**
 * 解析简历内容
 * @param {string} userMessage - 用户消息
 * @returns {Object} 解析的简历信息
 */
async function parseResumeContent(userMessage) {
  // 简化实现，实际应该使用更复杂的简历解析
  return {
    name: "用户",
    techDirection: "Web开发",
    workExperience: 3,
    skills: ["JavaScript", "React", "Node.js"],
    education: "本科",
  };
}

/**
 * 执行对应的处理器
 * @param {string} handlerName - 处理器名称
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 处理结果
 */
async function executeHandler(
  handlerName,
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  const handlers = {
    handleJobRecommendation,
    handleJobQuestion,
    handlePreferenceExpression,
    handleInfoCollection,
    handleThirdPartyInquiry,
    handleUserBackgroundUpdate,
    handleResumeUpload,
    handleGeneralChat,
    handleJobRelatedInquiry,
    handleCompanyInquiry,
    handleDetailedJDInquiry: require("./active-recommender")
      .handleDetailedJDInquiry,
    handleSecondRecommendationInquiry: require("./active-recommender")
      .handleSecondRecommendationInquiry,
  };

  const handler = handlers[handlerName];
  if (!handler) {
    return { response: "抱歉，我无法理解您的请求，请重新描述。" };
  }

  return await handler(userMessage, contextAnalysis, candidateInfo, userId);
}

/**
 * 计算候选人信息完整度
 * @param {Object} candidateInfo - 候选人信息
 * @returns {number} 完整度百分比
 */
function calculateCandidateCompleteness(candidateInfo) {
  if (!candidateInfo) return 0;

  const requiredFields = [
    "name",
    "techDirection",
    "workExperience",
    "expectedSalary",
    "location",
  ];
  const completedFields = requiredFields.filter(
    (field) => candidateInfo[field]
  );

  return Math.round((completedFields.length / requiredFields.length) * 100);
}

// ==================== 2. 业务处理器集群 ====================

// ==================== 2.1 核心业务处理器 ====================

/**
 * 职位推荐处理器，处理推荐请求
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 推荐结果
 */
async function handleJobRecommendation(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 1. 检查推荐触发条件
    const activeRecommender = require("./active-recommender");
    const triggerCheck = await activeRecommender.checkRecommendationTrigger(
      candidateInfo,
      userMessage
    );

    if (!triggerCheck.shouldRecommend) {
      return {
        response:
          triggerCheck.reason ||
          "根据您当前的信息，我需要了解更多细节才能为您推荐合适的职位。",
        needMoreInfo: true,
      };
    }

    // 2. 生成职位推荐
    const userPreference = await activeRecommender.detectUserPreference(
      userMessage
    );
    const recommendations = await activeRecommender.generateJobRecommendations(
      candidateInfo,
      "standard",
      userPreference
    );

    // 3. 格式化推荐结果
    const formattedResponse =
      await activeRecommender.generateRecommendationText(
        candidateInfo,
        recommendations,
        "standard",
        userPreference
      );

    // 4. 记录推荐历史
    await activeRecommender.recordRecommendedJobs(userId, recommendations.jobs);

    return {
      response: formattedResponse,
      recommendations: recommendations.jobs,
      hasRecommendations: true,
      type: "job_recommendation",
    };
  } catch (error) {
    console.error("职位推荐处理失败:", error);
    return {
      response: "抱歉，生成职位推荐时出现了问题，请稍后再试。",
      error: error.message,
    };
  }
}

/**
 * 职位问题处理器，处理职位相关询问
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 问题处理结果
 */
async function handleJobQuestion(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 1. 识别问题类型
    const questionType = identifyQuestionType(userMessage);

    // 2. 查询相关信息
    let response = "";
    switch (questionType) {
      case "company_inquiry":
        const companyName = extractCompanyFromQuestion(userMessage);
        const companyInfo = await databaseManager.getCompanyInfo(companyName);
        response = formatCompanyInfo(companyInfo);
        break;

      case "salary_inquiry":
        const salaryInfo = await databaseManager.getSalaryInfo(
          candidateInfo.techDirection,
          candidateInfo.location
        );
        response = formatSalaryInfo(salaryInfo);
        break;

      case "job_detail_inquiry":
        const jobId = extractJobIdFromMessage(userMessage);
        const jobDetail = await databaseManager.getJobDetail(jobId);
        response = formatJobDetail(jobDetail);
        break;

      default:
        response = "请您具体描述一下您想了解的职位信息，我会为您详细解答。";
    }

    return {
      response,
      questionType,
      type: "job_question",
    };
  } catch (error) {
    console.error("职位问题处理失败:", error);
    return {
      response: "抱歉，处理您的问题时出现了错误，请重新提问。",
      error: error.message,
    };
  }
}

/**
 * 偏好表达处理器，处理用户偏好声明
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 偏好处理结果
 */
async function handlePreferenceExpression(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 1. 提取偏好信息
    const extractedPreferences = await userManager.extractInformation(
      userMessage,
      candidateInfo
    );

    // 2. 更新候选人档案
    const updateResult = await userManager.updateCandidateProfile(
      userId,
      extractedPreferences
    );

    // 3. 生成确认回复
    const confirmationMessage =
      generatePreferenceConfirmation(extractedPreferences);

    return {
      response: confirmationMessage,
      updatedInfo: extractedPreferences,
      profileUpdated: updateResult.success,
      type: "preference_expression",
    };
  } catch (error) {
    console.error("偏好表达处理失败:", error);
    return {
      response: "我已记录您的偏好，请继续告诉我其他要求。",
      error: error.message,
    };
  }
}

/**
 * 信息收集处理器，收集候选人信息
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 信息收集结果
 */
async function handleInfoCollection(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 1. 提取信息
    const extractedInfo = await userManager.extractInformation(
      userMessage,
      candidateInfo
    );

    // 2. 验证信息
    const validationResult = validateExtractedInfo(extractedInfo);
    if (!validationResult.isValid) {
      return {
        response: `请确认以下信息：${validationResult.errors.join("、")}`,
        needClarification: true,
      };
    }

    // 3. 更新档案
    const updateResult = await userManager.updateCandidateProfile(
      userId,
      extractedInfo
    );

    // 4. 生成后续问题
    const nextQuestion = generateNextQuestion(candidateInfo, extractedInfo);

    return {
      response: nextQuestion,
      extractedInfo,
      profileUpdated: updateResult.success,
      completeness: calculateCandidateCompleteness({
        ...candidateInfo,
        ...extractedInfo,
      }),
      type: "info_collection",
    };
  } catch (error) {
    console.error("信息收集处理失败:", error);
    return {
      response: "感谢您提供的信息，请继续告诉我更多详情。",
      error: error.message,
    };
  }
}

/**
 * 第三方询问处理器，处理为他人询问职位
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 第三方询问结果
 */
async function handleThirdPartyInquiry(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 1. 识别第三方关系
    const relationship = extractRelationship(userMessage);

    // 2. 收集目标人员信息
    const targetPersonInfo = extractTargetPersonInfo(userMessage);

    // 3. 生成推荐或信息收集请求
    if (targetPersonInfo.isComplete) {
      // 直接生成推荐
      const activeRecommender = require("./active-recommender");
      const recommendations =
        await activeRecommender.generateJobRecommendations(
          targetPersonInfo,
          "third_party",
          null
        );

      return {
        response: `为您${relationship}推荐以下职位：\n${formatRecommendations(
          recommendations
        )}`,
        recommendations: recommendations.jobs,
        type: "third_party_recommendation",
      };
    } else {
      // 需要更多信息
      const missingInfo = identifyMissingInfo(targetPersonInfo);
      return {
        response: `为了给您${relationship}推荐合适的职位，我还需要了解：${missingInfo.join(
          "、"
        )}`,
        needMoreInfo: true,
        missingFields: missingInfo,
        type: "third_party_info_collection",
      };
    }
  } catch (error) {
    console.error("第三方询问处理失败:", error);
    return {
      response:
        "我理解您是在为他人咨询，请提供更多关于对方的信息，比如技术方向、工作经验等。",
      error: error.message,
    };
  }
}

// ==================== 2.2 辅助业务处理器 ====================

/**
 * 用户背景更新处理器
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 更新结果
 */
async function handleUserBackgroundUpdate(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  return await handleInfoCollection(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  );
}

/**
 * 简历上传处理器
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 上传处理结果
 */
async function handleResumeUpload(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 解析简历内容（这里简化处理）
    const resumeInfo = await parseResumeContent(userMessage);

    // 更新候选人档案
    const updateResult = await userManager.updateCandidateProfile(
      userId,
      resumeInfo
    );

    return {
      response: "简历信息已更新，我现在可以为您推荐更合适的职位了！",
      extractedInfo: resumeInfo,
      profileUpdated: updateResult.success,
      type: "resume_upload",
    };
  } catch (error) {
    console.error("简历上传处理失败:", error);
    return {
      response: "简历处理完成，请告诉我您的求职意向。",
      error: error.message,
    };
  }
}

/**
 * 通用对话处理器
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 对话结果
 */
async function handleGeneralChat(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  try {
    // 使用AI生成回复
    const aiResponse = await aiServices.generateChatResponse(
      userMessage,
      candidateInfo
    );

    return {
      response:
        aiResponse ||
        "我是您的AI招聘助手，可以为您推荐职位、解答求职问题。请告诉我您的需求。",
      type: "general_chat",
    };
  } catch (error) {
    console.error("通用对话处理失败:", error);
    return {
      response: "我是您的AI招聘助手，有什么可以帮助您的吗？",
      error: error.message,
    };
  }
}

/**
 * 职位相关询问处理器
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @param {string} userId - 用户ID
 * @returns {Object} 询问处理结果
 */
async function handleJobRelatedInquiry(
  userMessage,
  contextAnalysis,
  candidateInfo,
  userId
) {
  return await handleJobQuestion(
    userMessage,
    contextAnalysis,
    candidateInfo,
    userId
  );
}

/**
 * 公司询问处理器
 * @param {string} userMessage - 用户消息
 * @param {Object} contextAnalysis - 上下文分析结果
 * @param {Object} candidateInfo - 候选人信息
 * @returns {Object} 公司询问结果
 */
async function handleCompanyInquiry(
  userMessage,
  contextAnalysis,
  candidateInfo
) {
  try {
    const companyName = extractCompanyFromQuestion(userMessage);
    if (!companyName) {
      return {
        response: "请告诉我您想了解哪家公司的信息？",
        needClarification: true,
      };
    }

    const companyInfo = await databaseManager.getCompanyInfo(companyName);
    const formattedInfo = formatCompanyInfo(companyInfo);

    return {
      response: formattedInfo,
      companyName,
      type: "company_inquiry",
    };
  } catch (error) {
    console.error("公司询问处理失败:", error);
    return {
      response: "抱歉，查询公司信息时出现问题，请重新提问。",
      error: error.message,
    };
  }
}

// ==================== 导出模块 ====================

module.exports = {
  // 1. 消息路由和流程控制
  processMessage,
  analyzeContext,
  selectHandler,
  checkRecommendationsInHistory,

  // 2.1 核心业务处理器
  handleJobRecommendation,
  handleJobQuestion,
  handlePreferenceExpression,
  handleInfoCollection,
  handleThirdPartyInquiry,

  // 2.2 辅助业务处理器
  handleUserBackgroundUpdate,
  handleResumeUpload,
  handleGeneralChat,
  handleJobRelatedInquiry,
  handleCompanyInquiry,

  // 辅助函数
  executeHandler,
  calculateCandidateCompleteness,
  identifyQuestionType,
  extractCompanyFromQuestion,
  formatCompanyInfo,
  formatSalaryInfo,
  formatJobDetail,
  generatePreferenceConfirmation,
  validateExtractedInfo,
  generateNextQuestion,
};
