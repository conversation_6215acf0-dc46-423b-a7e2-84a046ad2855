/**
 * 消息处理器
 * 负责消息路由、意图分析和业务流程编排
 */

import { databaseManager } from "./database-manager.js";
import { aiServices } from "./ai-services.js";
import { passiveRecommender } from "./passive-recommender.js";
import { activeRecommender } from "./active-recommender.js";
import { techMapper } from "./tech-mapper.js";
import { FIXED_RESPONSES, INTENT_TYPES } from "../config/mapping-tables.js";

// ==================== 消息处理器 ====================

export class MessageProcessor {
  constructor() {
    this.databaseManager = databaseManager;
    this.aiServices = aiServices;
    this.passiveRecommender = passiveRecommender;
    this.activeRecommender = activeRecommender;
    this.techMapper = techMapper;
    this.fixedResponses = FIXED_RESPONSES;
  }

  // ==================== 主处理流程 ====================

  /**
   * 处理用户消息 - 主入口
   * 对应业务逻辑: processMessage(userMessage, sessionUuid)
   */
  async processMessage(userMessage, sessionUuid) {
    try {
      // 1. 检查固定话术回复
      const fixedReply = this.checkFixedResponses(userMessage);
      if (fixedReply) {
        await this.databaseManager.saveConversation(
          sessionUuid,
          userMessage,
          fixedReply
        );
        return {
          reply: fixedReply,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 2. 获取会话和候选人信息
      const session = await this.databaseManager.getOrCreateSession(
        sessionUuid
      );
      const conversationHistory =
        await this.databaseManager.getConversationHistory(sessionUuid);
      const { candidate: candidateInfo } =
        await this.databaseManager.getCandidateInfo(
          session.user_id || sessionUuid
        );

      // 3. AI驱动的上下文分析
      const contextAnalysis = await this.aiServices.analyzeContext(
        userMessage,
        conversationHistory,
        candidateInfo
      );

      // 4. 智能路由选择
      const handlerName = this.selectHandler(contextAnalysis, userMessage);

      // 5. 调用对应的处理器
      const result = await this[handlerName](
        userMessage,
        candidateInfo,
        session.user_id || sessionUuid,
        contextAnalysis
      );

      // 6. 保存对话记录
      await this.databaseManager.saveConversation(
        sessionUuid,
        userMessage,
        result.reply,
        result.metadata || {}
      );

      return result;
    } catch (error) {
      console.error("消息处理失败:", error);
      return {
        reply: "抱歉，处理您的消息时遇到了问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  // ==================== 路由选择 ====================

  /**
   * 智能路由选择
   * 对应业务逻辑: selectHandler(contextAnalysis, userMessage)
   */
  selectHandler(contextAnalysis, userMessage) {
    const intent = contextAnalysis.intent;

    // 根据意图选择处理器
    const handlerMap = {
      [INTENT_TYPES.JOB_RECOMMENDATION]: "handleJobRecommendation",
      [INTENT_TYPES.JOB_QUESTION]: "handleJobQuestion",
      [INTENT_TYPES.PREFERENCE_EXPRESSION]: "handlePreferenceExpression",
      [INTENT_TYPES.INFO_COLLECTION]: "handleInfoCollection",
      [INTENT_TYPES.THIRD_PARTY_INQUIRY]: "handleThirdPartyInquiry",
      [INTENT_TYPES.USER_BACKGROUND_UPDATE]: "handleUserBackgroundUpdate",
      [INTENT_TYPES.RESUME_UPLOAD]: "handleResumeUpload",
      [INTENT_TYPES.GENERAL_CHAT]: "handleGeneralChat",
      [INTENT_TYPES.JOB_RELATED_INQUIRY]: "handleJobRelatedInquiry",
      [INTENT_TYPES.COMPANY_INQUIRY]: "handleCompanyInquiry",
      [INTENT_TYPES.DETAILED_JD_INQUIRY]: "handleDetailedJDInquiry",
      [INTENT_TYPES.SECOND_RECOMMENDATION]: "handleSecondRecommendationInquiry",
    };

    return handlerMap[intent] || "handleGeneralChat";
  }

  // ==================== 处理器方法 ====================

  /**
   * 处理职位推荐请求
   * 对应业务逻辑: handleJobRecommendation(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleJobRecommendation(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      // 1. 检查候选人信息完整性
      if (!candidateInfo.primary_tech_direction_id) {
        // 尝试从消息中提取技术方向
        const extractedInfo = await this.aiServices.extractInformation(
          userMessage,
          candidateInfo
        );

        if (extractedInfo.techDirection) {
          // 检测技术方向歧义
          const ambiguityResult =
            await this.techMapper.detectTechAmbiguityIntelligently(
              extractedInfo.techDirection
            );

          if (ambiguityResult.needsClarification) {
            const ambiguityQuestion = this.techMapper.generateAmbiguityQuestion(
              ambiguityResult.originalTech,
              ambiguityResult.options
            );

            // 保存歧义状态
            this.techMapper.saveAmbiguityState(userId, ambiguityResult);

            return {
              reply: ambiguityQuestion,
              tokensUsed: 0,
              apiTier: "rules_engine",
              metadata: {
                hasAmbiguity: true,
                ambiguityType: "tech_direction",
                options: ambiguityResult.options,
              },
            };
          }
        }

        return {
          reply:
            "为了给您推荐合适的职位，我需要了解您的技术方向。请告诉我您主要从事什么技术领域？",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 2. 生成被动推荐
      const recommendations =
        await this.passiveRecommender.generateRecommendations(candidateInfo, {
          userPreference: contextAnalysis.entities?.preference,
        });

      // 3. 生成推荐文本
      const recommendationText = await this.generateRecommendationText(
        candidateInfo,
        recommendations,
        "passive"
      );

      return {
        reply: recommendationText,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          hasRecommendations: true,
          recommendations: recommendations,
          recommendationType: "passive",
        },
      };
    } catch (error) {
      console.error("处理职位推荐失败:", error);
      return {
        reply: "抱歉，获取职位推荐时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理第三方询问
   * 对应业务逻辑: handleThirdPartyInquiry(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleThirdPartyInquiry(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      const thirdPartyInfo = contextAnalysis.thirdPartyInfo;

      if (!thirdPartyInfo.techDirection) {
        return {
          reply:
            "要为您的朋友推荐合适的职位，我需要了解他/她的技术方向。请告诉我他/她主要从事什么技术领域？",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 检测第三方技术方向歧义
      const ambiguityResult =
        await this.techMapper.detectTechAmbiguityIntelligently(
          thirdPartyInfo.techDirection
        );

      if (ambiguityResult.needsClarification) {
        return await this.generateThirdPartyAmbiguityResponse(
          userMessage,
          ambiguityResult,
          thirdPartyInfo.targetPerson,
          thirdPartyInfo.relationship,
          userId
        );
      }

      // 构建第三方档案
      const thirdPartyProfile = {
        ...candidateInfo,
        primary_tech_direction_id: await this.techMapper.getTechDirectionId(
          thirdPartyInfo.techDirection,
          candidateInfo
        ),
      };

      // 生成第三方推荐
      return await this.generateThirdPartyRecommendations(
        thirdPartyProfile,
        thirdPartyInfo.targetPerson,
        thirdPartyInfo.relationship
      );
    } catch (error) {
      console.error("处理第三方询问失败:", error);
      return {
        reply: "抱歉，处理第三方推荐时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理用户背景更新
   * 对应业务逻辑: handleUserBackgroundUpdate(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleUserBackgroundUpdate(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      // 提取用户信息
      const extractedInfo = await this.aiServices.extractInformation(
        userMessage,
        candidateInfo
      );

      // 更新候选人档案
      const updateData = {};

      if (extractedInfo.company) {
        updateData.current_company_name_raw = extractedInfo.company;
      }

      if (extractedInfo.level) {
        updateData.candidate_level_raw = extractedInfo.level;
      }

      if (extractedInfo.techDirection) {
        updateData.candidate_tech_direction_raw = extractedInfo.techDirection;
        const techId = await this.techMapper.getTechDirectionId(
          extractedInfo.techDirection,
          candidateInfo
        );
        if (techId) {
          updateData.primary_tech_direction_id = techId;
        }
      }

      if (extractedInfo.expectedSalary) {
        updateData.expected_compensation_raw = extractedInfo.expectedSalary;
        // 这里可以添加薪资解析逻辑
      }

      if (extractedInfo.location) {
        updateData.desired_location_raw = extractedInfo.location;
      }

      if (Object.keys(updateData).length > 0) {
        await this.databaseManager.updateCandidateProfile(userId, updateData);

        return {
          reply:
            "感谢您提供的信息，我已经更新了您的档案。现在我可以为您推荐更合适的职位了。",
          tokensUsed: extractedInfo.tokensUsed || 0,
          apiTier: extractedInfo.model || "rules_engine",
        };
      } else {
        return {
          reply:
            "感谢您的信息，请告诉我更多关于您的技术背景、工作经验或期望的详细信息。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }
    } catch (error) {
      console.error("处理用户背景更新失败:", error);
      return {
        reply: "抱歉，更新您的信息时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理通用对话
   * 对应业务逻辑: handleGeneralChat(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleGeneralChat(userMessage, candidateInfo, userId, contextAnalysis) {
    try {
      // 使用AI生成对话回复
      const conversationReply = await this.aiServices.generateConversationReply(
        userMessage,
        candidateInfo
      );

      return {
        reply: conversationReply.reply,
        tokensUsed: conversationReply.tokensUsed,
        apiTier: conversationReply.apiTier,
      };
    } catch (error) {
      console.error("处理通用对话失败:", error);
      return {
        reply: "感谢您的消息，我会尽力为您提供帮助。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 检查固定话术回复
   * 对应业务逻辑: checkFixedResponses(userMessage)
   */
  checkFixedResponses(userMessage) {
    const normalizedMessage = userMessage.toLowerCase().trim();
    return this.fixedResponses[normalizedMessage] || null;
  }

  /**
   * 生成推荐文本
   * 对应业务逻辑: generateRecommendationText(candidateInfo, recommendations, type)
   */
  async generateRecommendationText(candidateInfo, recommendations, type) {
    try {
      let recommendationText = "";

      // 生成开头
      const greeting = this.generateRecommendationGreeting(candidateInfo, type);
      recommendationText += greeting + "\n\n";

      // 生成各分类的推荐
      for (const [categoryName, jobs] of Object.entries(recommendations)) {
        if (jobs && jobs.length > 0) {
          recommendationText += `**${categoryName}：**\n`;

          for (let i = 0; i < Math.min(jobs.length, 4); i++) {
            const job = jobs[i];
            const jobText = this.formatJobRecommendation(
              job,
              candidateInfo,
              i + 1
            );
            recommendationText += jobText + "\n";
          }

          recommendationText += "\n";
        }
      }

      // 生成结尾
      const closing = this.generateRecommendationClosing();
      recommendationText += closing;

      return recommendationText;
    } catch (error) {
      console.error("推荐文本生成失败:", error);
      return "基于您的背景，我为您推荐了一些合适的职位机会，请查看详细信息。";
    }
  }

  /**
   * 生成推荐开头
   */
  generateRecommendationGreeting(candidateInfo, type) {
    const greetings = [
      "基于您的技术背景，我为您推荐了以下职位：",
      "根据您的经验和技能，这些职位可能适合您：",
      "为您精选了一些匹配的职位机会：",
    ];

    return greetings[Math.floor(Math.random() * greetings.length)];
  }

  /**
   * 格式化职位推荐
   */
  formatJobRecommendation(job, candidateInfo, index) {
    return `${index}. ${job.companies?.company_name} - ${job.job_title}
   薪资：${job.salary_min || "面议"}-${job.salary_max || "面议"}万
   地点：${job.job_location || "待定"}`;
  }

  /**
   * 生成推荐结尾
   */
  generateRecommendationClosing() {
    return "如需了解详细JD，请告诉我您想了解哪个职位。";
  }

  // ==================== 其他处理器方法 ====================

  /**
   * 处理公司询问
   * 对应业务逻辑: handleCompanyInquiry(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleCompanyInquiry(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      const companyName = contextAnalysis.entities?.company;

      if (!companyName) {
        return {
          reply: "请告诉我您想了解哪家公司的信息？",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      const companyInfo = await this.databaseManager.getCompanyInfo(
        companyName
      );

      if (companyInfo.length === 0) {
        return {
          reply: `抱歉，暂时没有找到关于"${companyName}"的详细信息。`,
          tokensUsed: 0,
          apiTier: "database_query",
        };
      }

      const company = companyInfo[0];
      let reply = `**${company.company_name}**\n\n`;

      if (company.company_description) {
        reply += `公司介绍：${company.company_description}\n`;
      }

      if (company.company_type) {
        reply += `公司类型：${company.company_type}\n`;
      }

      if (company.company_size) {
        reply += `公司规模：${company.company_size}人\n`;
      }

      reply += "\n如果您对该公司的职位感兴趣，我可以为您推荐相关职位。";

      return {
        reply: reply,
        tokensUsed: 0,
        apiTier: "database_query",
      };
    } catch (error) {
      console.error("处理公司询问失败:", error);
      return {
        reply: "抱歉，获取公司信息时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理详细JD询问
   * 对应业务逻辑: handleDetailedJDInquiry(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleDetailedJDInquiry(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      // 获取最近推荐的职位
      const recentJobs = await this.passiveRecommender.getRecentRecommendedJobs(
        userId
      );

      if (!recentJobs || recentJobs.length === 0) {
        return {
          reply:
            "抱歉，我没有找到最近为您推荐的职位。请先让我为您推荐一些合适的职位。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 识别用户询问的目标职位
      const targetJob = await this.identifyTargetJob(userMessage, recentJobs);

      if (!targetJob) {
        return {
          reply:
            "请告诉我您想了解哪个公司的职位详情？比如：第1个、第2个，或者直接说公司名称。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 获取详细职位信息
      const detailedJob = await this.getDetailedJobInfo(targetJob.id);

      if (!detailedJob) {
        return {
          reply: "抱歉，暂时无法获取该职位的详细信息，请稍后再试。",
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 格式化详细JD
      const formattedJD = this.formatDetailedJD(detailedJob);

      return {
        reply: formattedJD,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          jobId: targetJob.id,
          companyName: detailedJob.companies?.company_name,
          hasDetailedJD: true,
        },
      };
    } catch (error) {
      console.error("处理详细JD询问失败:", error);
      return {
        reply: "抱歉，获取职位详情时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理第二次推荐询问
   * 对应业务逻辑: handleSecondRecommendationInquiry(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleSecondRecommendationInquiry(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      // 提取用户的偏好类型
      const preference =
        this.activeRecommender.extractSecondRecommendationPreference(
          userMessage
        );

      // 获取已推荐的公司列表
      const excludeCompanies =
        await this.passiveRecommender.getRecentRecommendedJobs(userId);

      // 生成第二次推荐
      const secondRecommendations =
        await this.activeRecommender.generateSecondRecommendations(
          candidateInfo,
          preference,
          excludeCompanies
        );

      if (
        !secondRecommendations ||
        Object.values(secondRecommendations).every((jobs) => jobs.length === 0)
      ) {
        return {
          reply:
            "抱歉，基于您的条件，暂时没有更多合适的职位推荐。建议您可以适当调整期望条件，我会为您寻找更多机会。",
          tokensUsed: 0,
          apiTier: "database_query",
        };
      }

      // 生成推荐文本
      const recommendationText = await this.generateRecommendationText(
        candidateInfo,
        secondRecommendations,
        "second_recommendation"
      );

      return {
        reply: recommendationText,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          hasRecommendations: true,
          recommendations: secondRecommendations,
          recommendationType: "second_recommendation",
          userPreference: preference,
        },
      };
    } catch (error) {
      console.error("处理第二次推荐询问失败:", error);
      return {
        reply: "抱歉，生成更多推荐时遇到问题，请稍后再试。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理职位相关询问
   * 对应业务逻辑: handleJobRelatedInquiry(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleJobRelatedInquiry(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    // 根据具体询问类型路由到不同处理器
    const requestType = this.activeRecommender.parseActiveRequest(userMessage);

    switch (requestType.type) {
      case "general":
        return await this.activeRecommender.handleGeneralRequest(
          candidateInfo,
          { userMessage }
        );
      case "specific":
        return await this.activeRecommender.handleSpecificRequest(
          candidateInfo,
          { userMessage }
        );
      case "filtered":
        return await this.activeRecommender.handleFilteredRequest(
          candidateInfo,
          { userMessage }
        );
      default:
        return await this.handleGeneralChat(
          userMessage,
          candidateInfo,
          userId,
          contextAnalysis
        );
    }
  }

  /**
   * 处理偏好表达
   * 对应业务逻辑: handlePreferenceExpression(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handlePreferenceExpression(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    try {
      // 提取偏好信息并更新档案
      const extractedInfo = await this.aiServices.extractInformation(
        userMessage,
        candidateInfo
      );

      const updateData = {};
      if (extractedInfo.location) {
        updateData.desired_location_raw = extractedInfo.location;
      }
      if (extractedInfo.expectedSalary) {
        updateData.expected_compensation_raw = extractedInfo.expectedSalary;
      }

      if (Object.keys(updateData).length > 0) {
        await this.databaseManager.updateCandidateProfile(userId, updateData);
      }

      return {
        reply: "我已经记录了您的偏好，这将帮助我为您推荐更合适的职位。",
        tokensUsed: extractedInfo.tokensUsed || 0,
        apiTier: extractedInfo.model || "rules_engine",
      };
    } catch (error) {
      console.error("处理偏好表达失败:", error);
      return {
        reply: "感谢您告诉我您的偏好，我会在推荐时考虑这些因素。",
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 处理信息收集
   * 对应业务逻辑: handleInfoCollection(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleInfoCollection(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    // 与用户背景更新处理逻辑相同
    return await this.handleUserBackgroundUpdate(
      userMessage,
      candidateInfo,
      userId,
      contextAnalysis
    );
  }

  /**
   * 处理简历上传
   * 对应业务逻辑: handleResumeUpload(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleResumeUpload(
    userMessage,
    candidateInfo,
    userId,
    contextAnalysis
  ) {
    return {
      reply:
        "感谢您提供简历信息。目前我主要通过对话了解您的背景，请告诉我您的技术方向、工作经验等信息。",
      tokensUsed: 0,
      apiTier: "rules_engine",
    };
  }

  /**
   * 处理职位问题
   * 对应业务逻辑: handleJobQuestion(userMessage, candidateInfo, userId, contextAnalysis)
   */
  async handleJobQuestion(userMessage, candidateInfo, userId, contextAnalysis) {
    // 使用AI生成针对性回复
    const conversationReply = await this.aiServices.generateConversationReply(
      userMessage,
      candidateInfo
    );

    return {
      reply: conversationReply.reply,
      tokensUsed: conversationReply.tokensUsed,
      apiTier: conversationReply.apiTier,
    };
  }

  // ==================== 辅助方法 ====================

  /**
   * 识别目标职位
   * 对应业务逻辑: identifyTargetJob(userMessage, recentJobs)
   */
  async identifyTargetJob(userMessage, recentJobs) {
    try {
      // 1. 检查数字序号
      const numberMatch = userMessage.match(/第?(\d+)[个]?/);
      if (numberMatch) {
        const index = parseInt(numberMatch[1]) - 1;
        if (index >= 0 && index < recentJobs.length) {
          return recentJobs[index];
        }
      }

      // 2. 检查公司名称
      for (const job of recentJobs) {
        if (userMessage.includes(job.companies?.company_name)) {
          return job;
        }
      }

      // 3. 如果只有一个推荐，默认返回
      if (recentJobs.length === 1) {
        return recentJobs[0];
      }

      return null;
    } catch (error) {
      console.error("识别目标职位失败:", error);
      return null;
    }
  }

  /**
   * 获取详细职位信息
   * 对应业务逻辑: getDetailedJobInfo(jobId)
   */
  async getDetailedJobInfo(jobId) {
    try {
      const { data } = await this.databaseManager.supabase
        .from(this.databaseManager.tables.jobListings)
        .select(
          `
          *,
          companies (
            company_name,
            company_type,
            company_size,
            company_description
          ),
          tech_tree (
            tech_name,
            parent_id
          )
        `
        )
        .eq("id", jobId)
        .single();

      return data;
    } catch (error) {
      console.error("获取详细职位信息失败:", error);
      return null;
    }
  }

  /**
   * 格式化详细JD
   * 对应业务逻辑: formatDetailedJD(jobData)
   */
  formatDetailedJD(jobData) {
    try {
      let jdText = `**${jobData.companies?.company_name} - ${jobData.job_title}**\n\n`;

      jdText += `**基本信息：**\n`;
      jdText += `• 薪资范围：${jobData.salary_min || "面议"}-${
        jobData.salary_max || "面议"
      }万\n`;
      jdText += `• 工作地点：${jobData.job_location || "待定"}\n`;
      jdText += `• 职级要求：${jobData.job_standard_level_min || "不限"}-${
        jobData.job_standard_level_max || "不限"
      }\n`;
      jdText += `• 经验要求：${jobData.experience_min || "不限"}-${
        jobData.experience_max || "不限"
      }年\n\n`;

      if (jobData.job_description) {
        jdText += `**职位描述：**\n${jobData.job_description}\n\n`;
      }

      if (jobData.job_requirements) {
        jdText += `**任职要求：**\n${jobData.job_requirements}\n\n`;
      }

      if (jobData.companies?.company_description) {
        jdText += `**公司介绍：**\n${jobData.companies.company_description}\n\n`;
      }

      jdText += `如果您对这个职位感兴趣，我可以为您提供更多信息或安排进一步沟通。`;

      return jdText;
    } catch (error) {
      console.error("格式化详细JD失败:", error);
      return "职位详情格式化失败，请稍后再试。";
    }
  }

  /**
   * 生成第三方推荐
   * 对应业务逻辑: generateThirdPartyRecommendations(thirdPartyProfile, targetPerson, relationship)
   */
  async generateThirdPartyRecommendations(
    thirdPartyProfile,
    targetPerson,
    relationship
  ) {
    try {
      // 验证第三方档案信息完整性
      if (!thirdPartyProfile.primary_tech_direction_id) {
        return {
          reply: `要为您的${relationship}推荐合适的职位，我需要了解他/她的技术方向。请告诉我他/她主要从事什么技术领域？`,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 查询匹配的职位
      const matchingJobs = await this.databaseManager.queryMatchingJobs(
        thirdPartyProfile,
        "third_party"
      );

      if (!matchingJobs || matchingJobs.length === 0) {
        return {
          reply: `抱歉，暂时没有找到适合您${relationship}的职位。建议提供更多背景信息，比如期望薪资、工作地点等。`,
          tokensUsed: 0,
          apiTier: "database_query",
        };
      }

      // 按4x4规则分类
      const categorizedJobs = this.passiveRecommender.categorizeJobsBy4x4Rule(
        matchingJobs,
        null
      );

      // 生成第三方推荐文本
      const recommendationText =
        await this.generateThirdPartyRecommendationText(
          thirdPartyProfile,
          categorizedJobs,
          targetPerson,
          relationship
        );

      return {
        reply: recommendationText,
        tokensUsed: 0,
        apiTier: "database_query",
        metadata: {
          hasRecommendations: true,
          recommendations: categorizedJobs,
          recommendationType: "third_party",
          targetPerson: targetPerson,
          relationship: relationship,
        },
      };
    } catch (error) {
      console.error("生成第三方推荐失败:", error);
      return {
        reply: `抱歉，为您的${relationship}推荐职位时遇到问题，请稍后再试。`,
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }

  /**
   * 生成第三方推荐文本
   * 对应业务逻辑: generateThirdPartyRecommendationText(thirdPartyProfile, categorizedJobs, targetPerson, relationship)
   */
  async generateThirdPartyRecommendationText(
    thirdPartyProfile,
    categorizedJobs,
    targetPerson,
    relationship
  ) {
    try {
      let recommendationText = `基于您${relationship}的背景，我为他/她推荐了以下职位：\n\n`;

      for (const [categoryName, jobs] of Object.entries(categorizedJobs)) {
        if (jobs && jobs.length > 0) {
          recommendationText += `**${categoryName}：**\n`;

          for (let i = 0; i < Math.min(jobs.length, 4); i++) {
            const job = jobs[i];
            recommendationText += `${i + 1}. ${job.companies?.company_name} - ${
              job.job_title
            }\n`;
            recommendationText += `   薪资：${job.salary_min || "面议"}-${
              job.salary_max || "面议"
            }万\n`;
            recommendationText += `   地点：${job.job_location || "待定"}\n\n`;
          }
        }
      }

      recommendationText += `这些职位都比较适合您${relationship}的背景。如需了解详细JD，请告诉我具体想了解哪个职位。`;

      return recommendationText;
    } catch (error) {
      console.error("生成第三方推荐文本失败:", error);
      return `为您的${relationship}推荐了一些合适的职位，请查看详细信息。`;
    }
  }

  /**
   * 生成第三方歧义响应
   * 对应业务逻辑: generateThirdPartyAmbiguityResponse(userMessage, ambiguityResult, targetPerson, relationship, userId)
   */
  async generateThirdPartyAmbiguityResponse(
    userMessage,
    ambiguityResult,
    targetPerson,
    relationship,
    userId
  ) {
    try {
      if (
        !ambiguityResult.needsClarification ||
        !ambiguityResult.options ||
        ambiguityResult.options.length === 0
      ) {
        return {
          reply: `关于您${relationship}的技术方向，我需要更多信息。请告诉我他/她具体从事什么技术领域？`,
          tokensUsed: 0,
          apiTier: "rules_engine",
        };
      }

      // 生成第三方歧义澄清问题
      let question = `关于您${relationship}的"${ambiguityResult.originalTech}"技术方向，可能指以下几个方向中的哪一个？\n\n`;

      for (const option of ambiguityResult.options) {
        question += `${option.index}. ${option.parentName}`;
        if (option.description && option.description !== option.parentName) {
          question += ` - ${option.description}`;
        }
        question += "\n";
      }

      question += `\n请告诉我您的${relationship}具体是哪个方向？这样我可以为他/她推荐更精准的职位。`;

      // 保存歧义状态
      this.techMapper.saveAmbiguityState(userId, {
        targetPerson,
        relationship,
        ambiguityResult,
        originalMessage: userMessage,
      });

      return {
        reply: question,
        tokensUsed: 0,
        apiTier: "rules_engine",
        metadata: {
          hasAmbiguity: true,
          ambiguityType: "third_party_tech_direction",
          targetPerson: targetPerson,
          relationship: relationship,
          options: ambiguityResult.options,
        },
      };
    } catch (error) {
      console.error("生成第三方歧义响应失败:", error);
      return {
        reply: `关于您${relationship}的技术方向，我需要更多信息。请告诉我他/她具体从事什么技术领域？`,
        tokensUsed: 0,
        apiTier: "rules_engine",
      };
    }
  }
}

// 导出单例实例
export const messageProcessor = new MessageProcessor();
