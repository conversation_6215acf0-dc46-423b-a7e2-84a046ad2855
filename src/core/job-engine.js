/**
 * Katrina AI 职位推荐和匹配引擎
 * 智能职位匹配、推荐算法、4x4推荐矩阵
 */

import { jobRepo } from "../data/database.js";
import { contextAnalyzer, informationExtractor } from "./ai-services.js";
import {
  calculateSimilarity,
  normalizeText,
  formatSalary,
} from "../utils/utilities.js";
import { JobEngineError } from "../utils/error-handlers.js";
import {
  TECH_MAPPINGS,
  JOB_CATEGORIES,
  RECOMMENDATION_CONFIG,
} from "../utils/config.js";

// ==================== 职位匹配引擎 ====================

export class JobMatchingEngine {
  constructor() {
    this.matchingWeights = this.buildMatchingWeights();
    this.skillMappings = this.buildSkillMappings();
  }

  /**
   * 计算候选人与职位的匹配度
   */
  calculateJobMatch(candidateProfile, jobPosition) {
    try {
      const scores = {
        skillMatch: this.calculateSkillMatch(candidateProfile, jobPosition),
        experienceMatch: this.calculateExperienceMatch(
          candidateProfile,
          jobPosition
        ),
        locationMatch: this.calculateLocationMatch(
          candidateProfile,
          jobPosition
        ),
        salaryMatch: this.calculateSalaryMatch(candidateProfile, jobPosition),
        levelMatch: this.calculateLevelMatch(candidateProfile, jobPosition),
      };

      // 加权计算总分
      const totalScore = Object.entries(scores).reduce(
        (total, [key, score]) => {
          return total + score * this.matchingWeights[key];
        },
        0
      );

      return {
        totalScore: Math.round(totalScore * 100) / 100,
        scores,
        recommendation: this.generateMatchRecommendation(scores, totalScore),
      };
    } catch (error) {
      throw new JobEngineError(`计算匹配度失败: ${error.message}`);
    }
  }

  /**
   * 技能匹配度计算
   */
  calculateSkillMatch(candidate, job) {
    const candidateSkills = this.normalizeSkills(candidate.skills || []);
    const requiredSkills = this.normalizeSkills(job.required_skills || []);
    const preferredSkills = this.normalizeSkills(job.preferred_skills || []);

    if (requiredSkills.length === 0) return 0.5;

    // 必需技能匹配
    const requiredMatches = requiredSkills.filter((skill) =>
      candidateSkills.some((cSkill) => this.isSkillMatch(cSkill, skill))
    );

    // 优选技能匹配
    const preferredMatches = preferredSkills.filter((skill) =>
      candidateSkills.some((cSkill) => this.isSkillMatch(cSkill, skill))
    );

    const requiredScore = requiredMatches.length / requiredSkills.length;
    const preferredScore =
      preferredSkills.length > 0
        ? preferredMatches.length / preferredSkills.length
        : 0;

    return requiredScore * 0.8 + preferredScore * 0.2;
  }

  /**
   * 经验匹配度计算
   */
  calculateExperienceMatch(candidate, job) {
    const candidateExp = candidate.experience_years || 0;
    const minExp = job.min_experience || 0;
    const maxExp = job.max_experience || 10;

    if (candidateExp < minExp) {
      // 经验不足，按比例扣分
      const deficit = minExp - candidateExp;
      return Math.max(0, 1 - deficit * 0.2);
    } else if (candidateExp > maxExp) {
      // 经验过多，轻微扣分
      const excess = candidateExp - maxExp;
      return Math.max(0.7, 1 - excess * 0.05);
    } else {
      // 经验在范围内，满分
      return 1.0;
    }
  }

  /**
   * 地点匹配度计算
   */
  calculateLocationMatch(candidate, job) {
    const candidateLocation = candidate.location;
    const jobLocation = job.location;

    if (!candidateLocation || !jobLocation) return 0.5;

    // 完全匹配
    if (candidateLocation === jobLocation) return 1.0;

    // 同城市群匹配
    const cityGroups = {
      北京: ["北京"],
      上海: ["上海"],
      深圳: ["深圳", "广州"],
      杭州: ["杭州", "苏州", "南京"],
    };

    for (const [group, cities] of Object.entries(cityGroups)) {
      if (cities.includes(candidateLocation) && cities.includes(jobLocation)) {
        return 0.8;
      }
    }

    // 远程工作
    if (job.remote_allowed) return 0.9;

    return 0.3;
  }

  /**
   * 薪资匹配度计算
   */
  calculateSalaryMatch(candidate, job) {
    const candidateExpectation = candidate.salary_expectation;
    const jobSalaryMin = job.salary_min;
    const jobSalaryMax = job.salary_max;

    if (!candidateExpectation || !jobSalaryMin) return 0.5;

    const expectationTarget =
      candidateExpectation.target || candidateExpectation;

    if (
      expectationTarget <= jobSalaryMax &&
      expectationTarget >= jobSalaryMin
    ) {
      return 1.0; // 完美匹配
    } else if (expectationTarget < jobSalaryMin) {
      // 期望低于职位薪资，好事
      return 0.9;
    } else {
      // 期望高于职位薪资
      const gap = expectationTarget - jobSalaryMax;
      const gapRatio = gap / jobSalaryMax;
      return Math.max(0.2, 1 - gapRatio);
    }
  }

  /**
   * 职级匹配度计算
   */
  calculateLevelMatch(candidate, job) {
    const candidateLevel = this.inferCandidateLevel(candidate);
    const jobLevel = job.level || "mid";

    const levelMapping = {
      junior: 1,
      mid: 2,
      senior: 3,
      lead: 4,
      architect: 5,
    };

    const candidateLevelNum = levelMapping[candidateLevel] || 2;
    const jobLevelNum = levelMapping[jobLevel] || 2;

    const diff = Math.abs(candidateLevelNum - jobLevelNum);

    if (diff === 0) return 1.0;
    if (diff === 1) return 0.8;
    if (diff === 2) return 0.5;
    return 0.2;
  }

  /**
   * 推断候选人职级
   */
  inferCandidateLevel(candidate) {
    const experience = candidate.experience_years || 0;
    const position = candidate.current_position || "";

    // 基于职位名称推断
    if (position.includes("架构师") || position.includes("技术总监"))
      return "architect";
    if (position.includes("高级") || position.includes("资深")) return "senior";
    if (position.includes("初级") || position.includes("助理")) return "junior";
    if (position.includes("主管") || position.includes("团队")) return "lead";

    // 基于经验推断
    if (experience >= 8) return "architect";
    if (experience >= 5) return "senior";
    if (experience >= 2) return "mid";
    return "junior";
  }

  /**
   * 技能标准化
   */
  normalizeSkills(skills) {
    return skills.map((skill) => normalizeText(skill.toLowerCase()));
  }

  /**
   * 技能匹配判断
   */
  isSkillMatch(candidateSkill, requiredSkill) {
    // 直接匹配
    if (candidateSkill === requiredSkill) return true;

    // 相似度匹配
    if (calculateSimilarity(candidateSkill, requiredSkill) > 0.8) return true;

    // 技能映射匹配
    const mappings = this.skillMappings[requiredSkill] || [];
    return mappings.includes(candidateSkill);
  }

  /**
   * 生成匹配建议
   */
  generateMatchRecommendation(scores, totalScore) {
    if (totalScore >= 0.8) {
      return {
        level: "excellent",
        message: "非常匹配，强烈推荐申请",
        confidence: 0.9,
      };
    } else if (totalScore >= 0.6) {
      return {
        level: "good",
        message: "较好匹配，建议申请",
        confidence: 0.7,
      };
    } else if (totalScore >= 0.4) {
      return {
        level: "fair",
        message: "一般匹配，可以尝试",
        confidence: 0.5,
      };
    } else {
      return {
        level: "poor",
        message: "匹配度较低，不太推荐",
        confidence: 0.3,
      };
    }
  }

  /**
   * 构建匹配权重
   */
  buildMatchingWeights() {
    return {
      skillMatch: 0.4, // 技能匹配最重要
      experienceMatch: 0.25, // 经验匹配
      locationMatch: 0.15, // 地点匹配
      salaryMatch: 0.15, // 薪资匹配
      levelMatch: 0.05, // 职级匹配
    };
  }

  /**
   * 构建技能映射
   */
  buildSkillMappings() {
    return {
      javascript: ["js", "node.js", "nodejs", "react", "vue"],
      java: ["spring", "springboot", "mybatis"],
      python: ["django", "flask", "fastapi"],
      react: ["javascript", "js", "frontend"],
      vue: ["javascript", "js", "frontend"],
      spring: ["java", "springboot"],
      mysql: ["sql", "database", "db"],
      redis: ["cache", "nosql"],
      docker: ["容器", "kubernetes", "k8s"],
    };
  }
}

// ==================== 推荐引擎 ====================

export class RecommendationEngine {
  constructor() {
    this.matchingEngine = new JobMatchingEngine();
    this.recommendationCache = new Map();
  }

  /**
   * 生成职位推荐
   */
  async generateRecommendations(candidateProfile, options = {}) {
    try {
      const {
        limit = 10,
        includePartialMatch = true,
        techDirection = null,
        forceRefresh = false,
      } = options;

      // 检查缓存
      const cacheKey = this.buildCacheKey(candidateProfile, options);
      if (!forceRefresh && this.recommendationCache.has(cacheKey)) {
        return this.recommendationCache.get(cacheKey);
      }

      // 构建搜索条件
      const searchCriteria = this.buildSearchCriteria(
        candidateProfile,
        techDirection
      );

      // 搜索职位
      const jobs = await jobRepo.searchJobs(searchCriteria);

      // 计算匹配度并排序
      const recommendations = jobs
        .map((job) => {
          const match = this.matchingEngine.calculateJobMatch(
            candidateProfile,
            job
          );
          return {
            job,
            match,
            score: match.totalScore,
          };
        })
        .filter((rec) => includePartialMatch || rec.score >= 0.4)
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      // 生成4x4推荐矩阵
      const matrix = this.generate4x4Matrix(recommendations, candidateProfile);

      const result = {
        recommendations,
        matrix,
        metadata: {
          totalJobs: jobs.length,
          filteredJobs: recommendations.length,
          searchCriteria,
          generatedAt: new Date().toISOString(),
        },
      };

      // 缓存结果
      this.recommendationCache.set(cacheKey, result);

      console.log(`🎯 生成推荐完成: ${recommendations.length}个职位`);
      return result;
    } catch (error) {
      throw new JobEngineError(`生成推荐失败: ${error.message}`);
    }
  }

  /**
   * 生成4x4推荐矩阵
   */
  generate4x4Matrix(recommendations, candidateProfile) {
    const matrix = {
      perfectMatch: [], // 完美匹配
      goodMatch: [], // 良好匹配
      potentialMatch: [], // 潜在匹配
      stretchGoals: [], // 挑战目标
    };

    recommendations.forEach((rec) => {
      if (rec.score >= 0.8) {
        matrix.perfectMatch.push(rec);
      } else if (rec.score >= 0.6) {
        matrix.goodMatch.push(rec);
      } else if (rec.score >= 0.4) {
        matrix.potentialMatch.push(rec);
      } else {
        matrix.stretchGoals.push(rec);
      }
    });

    // 确保每个类别最多4个
    Object.keys(matrix).forEach((key) => {
      matrix[key] = matrix[key].slice(0, 4);
    });

    return matrix;
  }

  /**
   * 构建搜索条件
   */
  buildSearchCriteria(candidateProfile, techDirection) {
    const criteria = {
      limit: 50, // 先获取更多候选，再筛选
    };

    // 技能搜索
    if (candidateProfile.skills && candidateProfile.skills.length > 0) {
      criteria.skills = candidateProfile.skills;
    }

    // 技术方向搜索
    if (techDirection) {
      const techSkills = TECH_MAPPINGS[techDirection] || [];
      criteria.skills = [...(criteria.skills || []), ...techSkills];
    }

    // 经验范围
    if (candidateProfile.experience_years) {
      criteria.experience = candidateProfile.experience_years;
    }

    // 地点偏好
    if (candidateProfile.location) {
      criteria.location = candidateProfile.location;
    }

    // 薪资范围
    if (candidateProfile.salary_expectation) {
      criteria.salaryRange = {
        min:
          candidateProfile.salary_expectation.min ||
          candidateProfile.salary_expectation * 0.8,
        max:
          candidateProfile.salary_expectation.max ||
          candidateProfile.salary_expectation * 1.2,
      };
    }

    return criteria;
  }

  /**
   * 构建缓存键
   */
  buildCacheKey(candidateProfile, options) {
    const keyData = {
      email: candidateProfile.email,
      skills: candidateProfile.skills?.sort(),
      experience: candidateProfile.experience_years,
      location: candidateProfile.location,
      techDirection: options.techDirection,
      limit: options.limit,
    };

    return `rec_${JSON.stringify(keyData)}`;
  }

  /**
   * 清除推荐缓存
   */
  clearCache(candidateEmail = null) {
    if (candidateEmail) {
      // 清除特定用户的缓存
      for (const [key, value] of this.recommendationCache.entries()) {
        if (key.includes(candidateEmail)) {
          this.recommendationCache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.recommendationCache.clear();
    }
  }
}

// ==================== 歧义处理引擎 ====================

export class AmbiguityResolver {
  constructor() {
    this.ambiguityPatterns = this.buildAmbiguityPatterns();
  }

  /**
   * 检测技术方向歧义
   */
  detectTechAmbiguity(userMessage, candidateProfile) {
    try {
      const analysis = contextAnalyzer.analyzeTechDirection(
        normalizeText(userMessage)
      );

      if (analysis.hasMultiple && analysis.allDetected.length > 1) {
        // 检查是否有明显的主导方向
        const topConfidence = analysis.confidence;
        const similarConfidences = analysis.allDetected.filter(
          (tech) => Math.abs(tech.confidence - topConfidence) < 0.2
        );

        if (similarConfidences.length > 1) {
          return {
            hasAmbiguity: true,
            type: "tech_direction",
            options: similarConfidences.map((tech) => ({
              direction: tech.direction,
              confidence: tech.confidence,
              keywords: tech.matchedKeywords,
            })),
            clarificationNeeded: true,
          };
        }
      }

      return { hasAmbiguity: false };
    } catch (error) {
      throw new JobEngineError(`歧义检测失败: ${error.message}`);
    }
  }

  /**
   * 生成澄清问题
   */
  generateClarificationQuestion(ambiguityData) {
    try {
      if (ambiguityData.type === "tech_direction") {
        const options = ambiguityData.options
          .map((opt, index) => `${index + 1}. ${opt.direction}`)
          .join("\n");

        return {
          question: `我注意到您提到了多个技术方向，为了给您更精准的推荐，请问您主要想找哪个方向的工作？\n\n${options}\n\n请回复对应的数字。`,
          options: ambiguityData.options,
          type: "tech_direction_choice",
        };
      }

      return {
        question: "请您再详细说明一下您的需求，这样我能为您提供更准确的建议。",
        options: [],
        type: "general_clarification",
      };
    } catch (error) {
      throw new JobEngineError(`生成澄清问题失败: ${error.message}`);
    }
  }

  /**
   * 解析用户选择
   */
  parseUserChoice(userMessage, ambiguityContext) {
    try {
      const normalizedMessage = normalizeText(userMessage);

      if (ambiguityContext.type === "tech_direction_choice") {
        // 尝试解析数字选择
        const numberMatch = normalizedMessage.match(/(\d+)/);
        if (numberMatch) {
          const choice = parseInt(numberMatch[1]) - 1;
          if (choice >= 0 && choice < ambiguityContext.options.length) {
            return {
              resolved: true,
              choice: ambiguityContext.options[choice],
              method: "number_selection",
            };
          }
        }

        // 尝试解析文本匹配
        for (const [index, option] of ambiguityContext.options.entries()) {
          if (normalizedMessage.includes(option.direction.toLowerCase())) {
            return {
              resolved: true,
              choice: option,
              method: "text_match",
            };
          }
        }
      }

      return {
        resolved: false,
        reason: "unable_to_parse",
      };
    } catch (error) {
      throw new JobEngineError(`解析用户选择失败: ${error.message}`);
    }
  }

  /**
   * 构建歧义模式
   */
  buildAmbiguityPatterns() {
    return {
      techDirection: [/前端.*后端/, /java.*python/, /移动.*web/, /数据.*开发/],
      experienceLevel: [/初级.*高级/, /新手.*资深/, /入门.*专家/],
      jobType: [/全职.*兼职/, /远程.*现场/, /创业.*大厂/],
    };
  }
}

// ==================== 职位分析器 ====================

export class JobAnalyzer {
  constructor() {
    this.analysisCache = new Map();
  }

  /**
   * 分析职位详情
   */
  async analyzeJobDetails(jobId) {
    try {
      // 检查缓存
      if (this.analysisCache.has(jobId)) {
        return this.analysisCache.get(jobId);
      }

      // 获取职位信息
      const job = await jobRepo.getJobById(jobId);
      if (!job) {
        throw new JobEngineError(`职位不存在: ${jobId}`);
      }

      // 分析职位特征
      const analysis = {
        techStack: this.analyzeTechStack(job),
        difficulty: this.analyzeDifficulty(job),
        growth: this.analyzeGrowthPotential(job),
        workLife: this.analyzeWorkLifeBalance(job),
        compensation: this.analyzeCompensation(job),
      };

      // 缓存结果
      this.analysisCache.set(jobId, analysis);

      return analysis;
    } catch (error) {
      throw new JobEngineError(`分析职位失败: ${error.message}`);
    }
  }

  /**
   * 分析技术栈
   */
  analyzeTechStack(job) {
    const requiredSkills = job.required_skills || [];
    const preferredSkills = job.preferred_skills || [];

    return {
      primary: requiredSkills.slice(0, 3),
      secondary: preferredSkills.slice(0, 3),
      modernness: this.calculateTechModernness(requiredSkills),
      complexity: requiredSkills.length + preferredSkills.length * 0.5,
    };
  }

  /**
   * 分析难度
   */
  analyzeDifficulty(job) {
    let difficultyScore = 0;

    // 基于经验要求
    difficultyScore += (job.min_experience || 0) * 0.2;

    // 基于技能数量
    const skillCount = (job.required_skills || []).length;
    difficultyScore += skillCount * 0.1;

    // 基于职位级别
    const levelMapping = { junior: 0.2, mid: 0.5, senior: 0.8, lead: 1.0 };
    difficultyScore += levelMapping[job.level] || 0.5;

    return {
      score: Math.min(difficultyScore, 1.0),
      level:
        difficultyScore > 0.7
          ? "high"
          : difficultyScore > 0.4
          ? "medium"
          : "low",
    };
  }

  /**
   * 分析成长潜力
   */
  analyzeGrowthPotential(job) {
    let growthScore = 0.5; // 基础分

    // 公司规模影响
    if (job.company_size === "startup") growthScore += 0.2;
    if (job.company_size === "large") growthScore += 0.1;

    // 技术栈现代化程度
    const modernness = this.calculateTechModernness(job.required_skills || []);
    growthScore += modernness * 0.3;

    return {
      score: Math.min(growthScore, 1.0),
      factors: ["技术栈现代化", "公司发展阶段", "职业发展路径"],
    };
  }

  /**
   * 分析工作生活平衡
   */
  analyzeWorkLifeBalance(job) {
    let balanceScore = 0.5;

    if (job.remote_allowed) balanceScore += 0.2;
    if (job.flexible_hours) balanceScore += 0.1;
    if (job.overtime_required === false) balanceScore += 0.2;

    return {
      score: Math.min(balanceScore, 1.0),
      remote: job.remote_allowed || false,
      flexible: job.flexible_hours || false,
    };
  }

  /**
   * 分析薪酬
   */
  analyzeCompensation(job) {
    const salaryMin = job.salary_min || 0;
    const salaryMax = job.salary_max || salaryMin;
    const salaryAvg = (salaryMin + salaryMax) / 2;

    return {
      range: { min: salaryMin, max: salaryMax, avg: salaryAvg },
      formatted: formatSalary(salaryAvg),
      competitiveness: this.calculateSalaryCompetitiveness(
        salaryAvg,
        job.level
      ),
    };
  }

  /**
   * 计算技术现代化程度
   */
  calculateTechModernness(skills) {
    const modernTechs = [
      "react",
      "vue",
      "node.js",
      "python",
      "go",
      "kubernetes",
      "docker",
      "aws",
    ];
    const modernCount = skills.filter((skill) =>
      modernTechs.some((modern) => skill.toLowerCase().includes(modern))
    ).length;

    return skills.length > 0 ? modernCount / skills.length : 0;
  }

  /**
   * 计算薪资竞争力
   */
  calculateSalaryCompetitiveness(salary, level) {
    const benchmarks = {
      junior: 15000,
      mid: 25000,
      senior: 40000,
      lead: 60000,
    };

    const benchmark = benchmarks[level] || benchmarks["mid"];
    const ratio = salary / benchmark;

    if (ratio >= 1.2) return "high";
    if (ratio >= 0.9) return "competitive";
    return "below_market";
  }
}

// ==================== 导出实例 ====================

export const jobMatchingEngine = new JobMatchingEngine();
export const recommendationEngine = new RecommendationEngine();
export const ambiguityResolver = new AmbiguityResolver();
export const jobAnalyzer = new JobAnalyzer();

export default {
  JobMatchingEngine,
  RecommendationEngine,
  AmbiguityResolver,
  JobAnalyzer,
  jobMatchingEngine,
  recommendationEngine,
  ambiguityResolver,
  jobAnalyzer,
};
