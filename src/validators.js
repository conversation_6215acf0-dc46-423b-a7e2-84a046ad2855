/**
 * 数据验证器 - 输入数据验证
 * 
 * 核心职责：
 * - 输入数据验证
 * - 数据格式检查
 * - 安全性验证
 * - 错误信息生成
 * 
 * 主要功能模块：
 * - 用户输入验证
 * - API参数验证
 * - 数据类型检查
 * - 业务规则验证
 * - 安全过滤器
 */

// ==================== 用户输入验证 ====================

/**
 * 验证用户消息
 * @param {string} message - 用户消息
 * @returns {Object} 验证结果
 */
function validateUserMessage(message) {
    // 实现用户消息验证逻辑
    const errors = [];
    
    if (!message || typeof message !== 'string') {
        errors.push('消息不能为空');
    } else {
        if (message.trim().length === 0) {
            errors.push('消息内容不能为空');
        }
        if (message.length > 1000) {
            errors.push('消息长度不能超过1000个字符');
        }
        if (containsMaliciousContent(message)) {
            errors.push('消息包含不当内容');
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors,
        sanitized: sanitizeMessage(message)
    };
}

/**
 * 验证候选人信息
 * @param {Object} candidateInfo - 候选人信息
 * @returns {Object} 验证结果
 */
function validateCandidateInfo(candidateInfo) {
    // 实现候选人信息验证逻辑
    const errors = [];
    const warnings = [];
    
    if (!candidateInfo || typeof candidateInfo !== 'object') {
        errors.push('候选人信息格式错误');
        return { isValid: false, errors, warnings };
    }
    
    // 验证必填字段
    if (!candidateInfo.name || candidateInfo.name.trim().length === 0) {
        errors.push('姓名不能为空');
    }
    
    if (candidateInfo.email && !isValidEmail(candidateInfo.email)) {
        errors.push('邮箱格式不正确');
    }
    
    if (candidateInfo.phone && !isValidPhone(candidateInfo.phone)) {
        errors.push('手机号格式不正确');
    }
    
    // 验证工作经验
    if (candidateInfo.workExperience !== undefined) {
        if (!Number.isInteger(candidateInfo.workExperience) || candidateInfo.workExperience < 0) {
            errors.push('工作经验必须是非负整数');
        }
        if (candidateInfo.workExperience > 50) {
            warnings.push('工作经验超过50年，请确认是否正确');
        }
    }
    
    // 验证薪资期望
    if (candidateInfo.expectedSalary) {
        if (!validateSalaryRange(candidateInfo.expectedSalary)) {
            errors.push('薪资期望格式不正确');
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 验证用户注册信息
 * @param {Object} userInfo - 用户注册信息
 * @returns {Object} 验证结果
 */
function validateUserRegistration(userInfo) {
    // 实现用户注册信息验证逻辑
    const errors = [];
    
    if (!userInfo || typeof userInfo !== 'object') {
        errors.push('用户信息格式错误');
        return { isValid: false, errors };
    }
    
    // 验证用户名
    if (!userInfo.username || userInfo.username.trim().length === 0) {
        errors.push('用户名不能为空');
    } else if (userInfo.username.length < 3 || userInfo.username.length > 20) {
        errors.push('用户名长度必须在3-20个字符之间');
    } else if (!/^[a-zA-Z0-9_]+$/.test(userInfo.username)) {
        errors.push('用户名只能包含字母、数字和下划线');
    }
    
    // 验证邮箱
    if (!userInfo.email || !isValidEmail(userInfo.email)) {
        errors.push('请提供有效的邮箱地址');
    }
    
    // 验证密码
    if (!userInfo.password) {
        errors.push('密码不能为空');
    } else if (!validatePassword(userInfo.password)) {
        errors.push('密码必须包含至少8个字符，包括大小写字母、数字和特殊字符');
    }
    
    // 验证确认密码
    if (userInfo.password !== userInfo.confirmPassword) {
        errors.push('两次输入的密码不一致');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// ==================== API参数验证 ====================

/**
 * 验证聊天API参数
 * @param {Object} params - API参数
 * @returns {Object} 验证结果
 */
function validateChatAPIParams(params) {
    // 实现聊天API参数验证逻辑
    const errors = [];
    
    if (!params || typeof params !== 'object') {
        errors.push('参数格式错误');
        return { isValid: false, errors };
    }
    
    // 验证用户ID
    if (!params.userId || !isValidUUID(params.userId)) {
        errors.push('用户ID格式不正确');
    }
    
    // 验证消息内容
    const messageValidation = validateUserMessage(params.message);
    if (!messageValidation.isValid) {
        errors.push(...messageValidation.errors);
    }
    
    // 验证会话ID（可选）
    if (params.sessionId && !isValidUUID(params.sessionId)) {
        errors.push('会话ID格式不正确');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * 验证职位搜索参数
 * @param {Object} params - 搜索参数
 * @returns {Object} 验证结果
 */
function validateJobSearchParams(params) {
    // 实现职位搜索参数验证逻辑
    const errors = [];
    
    if (!params || typeof params !== 'object') {
        errors.push('搜索参数格式错误');
        return { isValid: false, errors };
    }
    
    // 验证关键词
    if (params.keyword && typeof params.keyword !== 'string') {
        errors.push('搜索关键词必须是字符串');
    }
    
    // 验证地理位置
    if (params.location && !Array.isArray(params.location)) {
        errors.push('地理位置必须是数组');
    }
    
    // 验证薪资范围
    if (params.salaryRange && !validateSalaryRange(params.salaryRange)) {
        errors.push('薪资范围格式不正确');
    }
    
    // 验证分页参数
    if (params.page !== undefined) {
        if (!Number.isInteger(params.page) || params.page < 1) {
            errors.push('页码必须是正整数');
        }
    }
    
    if (params.limit !== undefined) {
        if (!Number.isInteger(params.limit) || params.limit < 1 || params.limit > 100) {
            errors.push('每页数量必须在1-100之间');
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * 验证用户档案更新参数
 * @param {Object} params - 更新参数
 * @returns {Object} 验证结果
 */
function validateProfileUpdateParams(params) {
    // 实现用户档案更新参数验证逻辑
    const errors = [];
    
    if (!params || typeof params !== 'object') {
        errors.push('更新参数格式错误');
        return { isValid: false, errors };
    }
    
    // 验证用户ID
    if (!params.userId || !isValidUUID(params.userId)) {
        errors.push('用户ID格式不正确');
    }
    
    // 验证更新数据
    if (!params.updateData || typeof params.updateData !== 'object') {
        errors.push('更新数据格式错误');
    } else {
        const candidateValidation = validateCandidateInfo(params.updateData);
        if (!candidateValidation.isValid) {
            errors.push(...candidateValidation.errors);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// ==================== 数据类型检查 ====================

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
function isValidEmail(email) {
    // 实现邮箱格式验证逻辑
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
function isValidPhone(phone) {
    // 实现手机号格式验证逻辑
    if (!phone || typeof phone !== 'string') return false;
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone.trim());
}

/**
 * 验证UUID格式
 * @param {string} uuid - UUID字符串
 * @returns {boolean} 是否有效
 */
function isValidUUID(uuid) {
    // 实现UUID格式验证逻辑
    if (!uuid || typeof uuid !== 'string') return false;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {boolean} 是否符合要求
 */
function validatePassword(password) {
    // 实现密码强度验证逻辑
    if (!password || typeof password !== 'string') return false;
    
    // 至少8个字符
    if (password.length < 8) return false;
    
    // 包含大写字母
    if (!/[A-Z]/.test(password)) return false;
    
    // 包含小写字母
    if (!/[a-z]/.test(password)) return false;
    
    // 包含数字
    if (!/\d/.test(password)) return false;
    
    // 包含特殊字符
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return false;
    
    return true;
}

/**
 * 验证薪资范围
 * @param {Object} salaryRange - 薪资范围
 * @returns {boolean} 是否有效
 */
function validateSalaryRange(salaryRange) {
    // 实现薪资范围验证逻辑
    if (!salaryRange || typeof salaryRange !== 'object') return false;
    
    const { min, max } = salaryRange;
    
    // 检查是否为数字
    if (!Number.isInteger(min) || !Number.isInteger(max)) return false;
    
    // 检查范围合理性
    if (min < 0 || max < 0) return false;
    if (min > max) return false;
    if (max > 1000000) return false; // 最高100万
    
    return true;
}

// ==================== 业务规则验证 ====================

/**
 * 验证技术方向
 * @param {string} techDirection - 技术方向
 * @returns {boolean} 是否有效
 */
function validateTechDirection(techDirection) {
    // 实现技术方向验证逻辑
    if (!techDirection || typeof techDirection !== 'string') return false;
    
    const validTechDirections = [
        'frontend', 'backend', 'mobile', 'devops', 'data',
        'ai_ml', 'game', 'blockchain', 'security', 'testing'
    ];
    
    return validTechDirections.includes(techDirection.toLowerCase());
}

/**
 * 验证公司类型
 * @param {string} companyType - 公司类型
 * @returns {boolean} 是否有效
 */
function validateCompanyType(companyType) {
    // 实现公司类型验证逻辑
    if (!companyType || typeof companyType !== 'string') return false;
    
    const validCompanyTypes = [
        'big_tech', 'state_owned', 'medium', 'startup', 'foreign', 'unicorn'
    ];
    
    return validCompanyTypes.includes(companyType.toLowerCase());
}

/**
 * 验证工作经验年限
 * @param {number} experience - 工作经验年限
 * @returns {boolean} 是否有效
 */
function validateWorkExperience(experience) {
    // 实现工作经验验证逻辑
    if (experience === undefined || experience === null) return true; // 可选字段
    
    return Number.isInteger(experience) && experience >= 0 && experience <= 50;
}

/**
 * 验证地理位置
 * @param {Array} locations - 地理位置列表
 * @returns {boolean} 是否有效
 */
function validateLocations(locations) {
    // 实现地理位置验证逻辑
    if (!Array.isArray(locations)) return false;
    
    const validCities = [
        '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都',
        '重庆', '天津', '苏州', '西安', '长沙', '沈阳', '大连', '青岛'
    ];
    
    return locations.every(location => 
        typeof location === 'string' && validCities.includes(location)
    );
}

// ==================== 安全过滤器 ====================

/**
 * 检查恶意内容
 * @param {string} content - 内容
 * @returns {boolean} 是否包含恶意内容
 */
function containsMaliciousContent(content) {
    // 实现恶意内容检查逻辑
    if (!content || typeof content !== 'string') return false;
    
    const maliciousPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /eval\s*\(/gi,
        /document\.cookie/gi,
        /window\.location/gi
    ];
    
    return maliciousPatterns.some(pattern => pattern.test(content));
}

/**
 * 清理用户消息
 * @param {string} message - 原始消息
 * @returns {string} 清理后的消息
 */
function sanitizeMessage(message) {
    // 实现消息清理逻辑
    if (!message || typeof message !== 'string') return '';
    
    return message
        .trim()
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/[<>]/g, '');
}

/**
 * 验证文件上传
 * @param {Object} file - 文件对象
 * @returns {Object} 验证结果
 */
function validateFileUpload(file) {
    // 实现文件上传验证逻辑
    const errors = [];
    
    if (!file) {
        errors.push('文件不能为空');
        return { isValid: false, errors };
    }
    
    // 验证文件大小（最大5MB）
    if (file.size > 5 * 1024 * 1024) {
        errors.push('文件大小不能超过5MB');
    }
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
    if (!allowedTypes.includes(file.type)) {
        errors.push('不支持的文件类型');
    }
    
    // 验证文件名
    if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
        errors.push('文件名包含非法字符');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// ==================== 导出模块 ====================

module.exports = {
    // 用户输入验证
    validateUserMessage,
    validateCandidateInfo,
    validateUserRegistration,
    
    // API参数验证
    validateChatAPIParams,
    validateJobSearchParams,
    validateProfileUpdateParams,
    
    // 数据类型检查
    isValidEmail,
    isValidPhone,
    isValidUUID,
    validatePassword,
    validateSalaryRange,
    
    // 业务规则验证
    validateTechDirection,
    validateCompanyType,
    validateWorkExperience,
    validateLocations,
    
    // 安全过滤器
    containsMaliciousContent,
    sanitizeMessage,
    validateFileUpload
};
