/**
 * Katrina AI 数据库操作和连接管理
 * 统一的数据访问层，支持 Supabase 和缓存
 */

import { createClient } from "@supabase/supabase-js";
import { SimpleCache } from "../utils/utilities.js";
import { DatabaseError } from "../utils/error-handlers.js";
import { DB_SCHEMA, CACHE_CONFIG } from "../utils/config.js";

// ==================== 数据库连接管理 ====================

class DatabaseManager {
  constructor() {
    this.supabase = null;
    this.cache = new SimpleCache(CACHE_CONFIG.MAX_SIZE);
    this.connectionPool = new Map();
    this.isConnected = false;
  }

  /**
   * 初始化数据库连接
   */
  async initialize() {
    try {
      this.supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
        }
      );

      // 测试连接
      const { data, error } = await this.supabase
        .from("candidates")
        .select("count")
        .limit(1);

      if (error) throw error;

      this.isConnected = true;
      console.log("✅ 数据库连接成功");
    } catch (error) {
      console.error("❌ 数据库连接失败:", error);
      throw new DatabaseError("数据库初始化失败", "initialize", "connection");
    }
  }

  /**
   * 获取数据库实例
   */
  getClient() {
    if (!this.isConnected || !this.supabase) {
      throw new DatabaseError("数据库未连接", "getClient", "connection");
    }
    return this.supabase;
  }
}

// ==================== 候选人数据操作 ====================

export class CandidateRepository {
  constructor(dbManager) {
    this.db = dbManager;
    this.tableName = "candidates";
  }

  /**
   * 创建或更新候选人档案
   */
  async upsertCandidate(email, profileData) {
    try {
      const client = this.db.getClient();

      const candidateData = {
        email: email.toLowerCase(),
        name: profileData.name,
        phone: profileData.phone,
        experience_years: profileData.experience,
        current_position: profileData.position,
        current_company: profileData.company,
        skills: profileData.skills || [],
        education: profileData.education,
        location: profileData.location,
        salary_expectation: profileData.salaryExpectation,
        job_preferences: profileData.preferences || {},
        notes: profileData.notes || "",
        status: profileData.status || "active",
        last_contact_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await client
        .from(this.tableName)
        .upsert(candidateData, {
          onConflict: "email",
          returning: "representation",
        })
        .select()
        .single();

      if (error) throw error;

      // 清除缓存
      this.db.cache.clear();

      return data;
    } catch (error) {
      throw new DatabaseError(
        `候选人档案操作失败: ${error.message}`,
        "upsert",
        this.tableName
      );
    }
  }

  /**
   * 根据邮箱获取候选人档案
   */
  async getCandidateByEmail(email) {
    try {
      const cacheKey = `candidate_${email.toLowerCase()}`;

      // 先检查缓存
      const cached = this.db.cache.get(cacheKey);
      if (cached) return cached;

      const client = this.db.getClient();

      const { data, error } = await client
        .from(this.tableName)
        .select("*")
        .eq("email", email.toLowerCase())
        .single();

      if (error && error.code !== "PGRST116") {
        // PGRST116 = 未找到记录
        throw error;
      }

      // 缓存结果
      if (data) {
        this.db.cache.set(cacheKey, data, CACHE_CONFIG.TTL);
      }

      return data;
    } catch (error) {
      throw new DatabaseError(
        `获取候选人档案失败: ${error.message}`,
        "select",
        this.tableName
      );
    }
  }

  /**
   * 搜索候选人
   */
  async searchCandidates(filters = {}) {
    try {
      const client = this.db.getClient();
      let query = client.from(this.tableName).select("*");

      // 应用过滤条件
      if (filters.skills && filters.skills.length > 0) {
        query = query.contains("skills", filters.skills);
      }

      if (filters.experience) {
        query = query.gte("experience_years", filters.experience.min || 0);
        if (filters.experience.max) {
          query = query.lte("experience_years", filters.experience.max);
        }
      }

      if (filters.location) {
        query = query.ilike("location", `%${filters.location}%`);
      }

      if (filters.status) {
        query = query.eq("status", filters.status);
      }

      // 排序和分页
      query = query
        .order("last_contact_date", { ascending: false })
        .limit(filters.limit || 50);

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
    } catch (error) {
      throw new DatabaseError(
        `搜索候选人失败: ${error.message}`,
        "search",
        this.tableName
      );
    }
  }
}

// ==================== 对话记录操作 ====================

export class ConversationRepository {
  constructor(dbManager) {
    this.db = dbManager;
    this.tableName = "conversations";
  }

  /**
   * 保存对话记录
   */
  async saveConversation(sessionId, email, message, response, metadata = {}) {
    try {
      const client = this.db.getClient();

      const conversationData = {
        session_id: sessionId,
        candidate_email: email.toLowerCase(),
        user_message: message,
        bot_response: response,
        message_type: metadata.messageType || "chat",
        intent_detected: metadata.intent || null,
        recommendations_shown: metadata.recommendations || [],
        tokens_used: metadata.tokensUsed || 0,
        response_time_ms: metadata.responseTime || 0,
        created_at: new Date().toISOString(),
      };

      const { data, error } = await client
        .from(this.tableName)
        .insert(conversationData)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      throw new DatabaseError(
        `保存对话记录失败: ${error.message}`,
        "insert",
        this.tableName
      );
    }
  }

  /**
   * 获取对话历史
   */
  async getConversationHistory(email, limit = 20) {
    try {
      const cacheKey = `conversation_${email.toLowerCase()}_${limit}`;

      // 检查缓存
      const cached = this.db.cache.get(cacheKey);
      if (cached) return cached;

      const client = this.db.getClient();

      const { data, error } = await client
        .from(this.tableName)
        .select("*")
        .eq("candidate_email", email.toLowerCase())
        .order("created_at", { ascending: false })
        .limit(limit);

      if (error) throw error;

      const history = data || [];

      // 缓存结果
      this.db.cache.set(cacheKey, history, CACHE_CONFIG.TTL);

      return history;
    } catch (error) {
      throw new DatabaseError(
        `获取对话历史失败: ${error.message}`,
        "select",
        this.tableName
      );
    }
  }
}

// ==================== 职位数据操作 ====================

export class JobRepository {
  constructor(dbManager) {
    this.db = dbManager;
    this.tableName = "job_positions";
  }

  /**
   * 搜索职位
   */
  async searchJobs(criteria = {}) {
    try {
      const client = this.db.getClient();
      let query = client.from(this.tableName).select("*");

      // 应用搜索条件
      if (criteria.keywords) {
        query = query.or(
          `title.ilike.%${criteria.keywords}%,description.ilike.%${criteria.keywords}%`
        );
      }

      if (criteria.skills && criteria.skills.length > 0) {
        query = query.overlaps("required_skills", criteria.skills);
      }

      if (criteria.location) {
        query = query.ilike("location", `%${criteria.location}%`);
      }

      if (criteria.experience) {
        query = query.lte("min_experience", criteria.experience);
        query = query.gte("max_experience", criteria.experience);
      }

      if (criteria.salaryRange) {
        if (criteria.salaryRange.min) {
          query = query.gte("salary_max", criteria.salaryRange.min);
        }
        if (criteria.salaryRange.max) {
          query = query.lte("salary_min", criteria.salaryRange.max);
        }
      }

      // 只返回活跃职位
      query = query.eq("status", "active");

      // 排序和分页
      query = query
        .order("created_at", { ascending: false })
        .limit(criteria.limit || 20);

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
    } catch (error) {
      throw new DatabaseError(
        `搜索职位失败: ${error.message}`,
        "search",
        this.tableName
      );
    }
  }

  /**
   * 获取推荐职位
   */
  async getRecommendedJobs(candidateProfile, limit = 10) {
    try {
      // 基于候选人技能和经验推荐职位
      const criteria = {
        skills: candidateProfile.skills || [],
        experience: candidateProfile.experience_years || 0,
        location: candidateProfile.location,
        limit,
      };

      return await this.searchJobs(criteria);
    } catch (error) {
      throw new DatabaseError(
        `获取推荐职位失败: ${error.message}`,
        "recommend",
        this.tableName
      );
    }
  }
}

// ==================== 数据库管理器实例 ====================

const dbManager = new DatabaseManager();

// 初始化数据库连接
if (typeof window === "undefined") {
  // 只在服务端初始化
  dbManager.initialize().catch(console.error);
}

// ==================== 导出仓库实例 ====================

export const candidateRepo = new CandidateRepository(dbManager);
export const conversationRepo = new ConversationRepository(dbManager);
export const jobRepo = new JobRepository(dbManager);

export default dbManager;
