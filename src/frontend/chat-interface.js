/**
 * 聊天界面逻辑
 * 负责前端聊天界面的交互逻辑和状态管理
 */

import { generateUUID } from '../config/utilities.js';

// ==================== 聊天界面管理器 ====================

export class ChatInterface {
  constructor() {
    this.sessionUuid = this.getOrCreateSessionUuid();
    this.messageHistory = [];
    this.isLoading = false;
    this.apiBaseUrl = '/api';
    
    // DOM元素引用
    this.chatContainer = null;
    this.messageInput = null;
    this.sendButton = null;
    this.loadingIndicator = null;
    
    // 事件监听器
    this.eventListeners = new Map();
  }

  // ==================== 初始化 ====================

  /**
   * 初始化聊天界面
   * 对应业务逻辑: initialize(containerId)
   */
  initialize(containerId) {
    try {
      this.chatContainer = document.getElementById(containerId);
      if (!this.chatContainer) {
        throw new Error(`找不到容器元素: ${containerId}`);
      }

      this.createChatInterface();
      this.bindEvents();
      this.loadChatHistory();
      
      console.log('聊天界面初始化成功');
    } catch (error) {
      console.error('聊天界面初始化失败:', error);
    }
  }

  /**
   * 创建聊天界面HTML结构
   * 对应业务逻辑: createChatInterface()
   */
  createChatInterface() {
    this.chatContainer.innerHTML = `
      <div class="chat-interface">
        <div class="chat-header">
          <h3>AI招聘顾问 Katrina</h3>
          <div class="chat-status">
            <span class="status-indicator online"></span>
            <span class="status-text">在线</span>
          </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
          <div class="welcome-message">
            <div class="message bot-message">
              <div class="message-content">
                <p>您好！我是AI招聘顾问Katrina，专注于为技术人才推荐合适的职位机会。</p>
                <p>请告诉我您的技术方向和求职需求，我会为您推荐最匹配的职位。</p>
              </div>
              <div class="message-time">${this.formatTime(new Date())}</div>
            </div>
          </div>
        </div>
        
        <div class="chat-input-container">
          <div class="loading-indicator" id="loadingIndicator" style="display: none;">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span class="loading-text">Katrina正在思考...</span>
          </div>
          
          <div class="chat-input-wrapper">
            <textarea 
              id="messageInput" 
              class="message-input" 
              placeholder="请输入您的消息..."
              rows="1"
              maxlength="2000"
            ></textarea>
            <button id="sendButton" class="send-button" title="发送消息">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </div>
          
          <div class="input-footer">
            <span class="char-count">0/2000</span>
            <span class="tips">按 Enter 发送，Shift+Enter 换行</span>
          </div>
        </div>
      </div>
    `;

    // 获取DOM元素引用
    this.messageInput = document.getElementById('messageInput');
    this.sendButton = document.getElementById('sendButton');
    this.loadingIndicator = document.getElementById('loadingIndicator');
    this.messagesContainer = document.getElementById('chatMessages');
  }

  /**
   * 绑定事件监听器
   * 对应业务逻辑: bindEvents()
   */
  bindEvents() {
    // 发送按钮点击事件
    this.sendButton.addEventListener('click', () => {
      this.sendMessage();
    });

    // 输入框键盘事件
    this.messageInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 输入框内容变化事件
    this.messageInput.addEventListener('input', () => {
      this.updateCharCount();
      this.autoResizeTextarea();
    });

    // 输入框焦点事件
    this.messageInput.addEventListener('focus', () => {
      this.scrollToBottom();
    });
  }

  // ==================== 消息处理 ====================

  /**
   * 发送消息
   * 对应业务逻辑: sendMessage()
   */
  async sendMessage() {
    const message = this.messageInput.value.trim();
    
    if (!message || this.isLoading) {
      return;
    }

    try {
      // 显示用户消息
      this.addMessage(message, 'user');
      
      // 清空输入框
      this.messageInput.value = '';
      this.updateCharCount();
      this.autoResizeTextarea();
      
      // 显示加载状态
      this.setLoading(true);
      
      // 发送API请求
      const response = await this.callChatAPI(message);
      
      if (response.success) {
        // 显示AI回复
        this.addMessage(response.data.reply, 'bot', response.data.metadata);
        
        // 更新会话UUID
        if (response.data.sessionUuid) {
          this.sessionUuid = response.data.sessionUuid;
          this.saveSessionUuid();
        }
      } else {
        // 显示错误消息
        this.addMessage('抱歉，处理您的消息时遇到了问题，请稍后再试。', 'bot', { error: true });
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      this.addMessage('网络连接异常，请检查网络后重试。', 'bot', { error: true });
    } finally {
      this.setLoading(false);
      this.messageInput.focus();
    }
  }

  /**
   * 添加消息到界面
   * 对应业务逻辑: addMessage(content, type, metadata)
   */
  addMessage(content, type, metadata = {}) {
    const messageElement = this.createMessageElement(content, type, metadata);
    this.messagesContainer.appendChild(messageElement);
    
    // 添加到消息历史
    this.messageHistory.push({
      content: content,
      type: type,
      timestamp: new Date(),
      metadata: metadata
    });
    
    // 滚动到底部
    this.scrollToBottom();
    
    // 添加动画效果
    setTimeout(() => {
      messageElement.classList.add('message-appear');
    }, 10);
  }

  /**
   * 创建消息元素
   * 对应业务逻辑: createMessageElement(content, type, metadata)
   */
  createMessageElement(content, type, metadata = {}) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    // 处理推荐消息的特殊格式
    let formattedContent = content;
    if (metadata.hasRecommendations) {
      formattedContent = this.formatRecommendationMessage(content);
    } else if (metadata.hasAmbiguity) {
      formattedContent = this.formatAmbiguityMessage(content, metadata.options);
    } else {
      formattedContent = this.formatRegularMessage(content);
    }
    
    messageDiv.innerHTML = `
      <div class="message-content">
        ${formattedContent}
      </div>
      <div class="message-time">${this.formatTime(new Date())}</div>
      ${metadata.tokensUsed ? `<div class="message-meta">Token: ${metadata.tokensUsed}</div>` : ''}
    `;
    
    return messageDiv;
  }

  /**
   * 格式化推荐消息
   * 对应业务逻辑: formatRecommendationMessage(content)
   */
  formatRecommendationMessage(content) {
    // 将推荐消息中的**标题**转换为HTML格式
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong class="recommendation-category">$1</strong>')
      .replace(/\n/g, '<br>')
      .replace(/(\d+\.\s.*?)(?=\n|$)/g, '<div class="job-item">$1</div>');
  }

  /**
   * 格式化歧义消息
   * 对应业务逻辑: formatAmbiguityMessage(content, options)
   */
  formatAmbiguityMessage(content, options) {
    let formatted = content.replace(/\n/g, '<br>');
    
    if (options && options.length > 0) {
      // 为选项添加点击事件
      options.forEach(option => {
        const optionPattern = new RegExp(`(${option.index}\\. ${option.parentName})`, 'g');
        formatted = formatted.replace(optionPattern, 
          `<span class="ambiguity-option" data-option="${option.index}">$1</span>`
        );
      });
    }
    
    return formatted;
  }

  /**
   * 格式化普通消息
   * 对应业务逻辑: formatRegularMessage(content)
   */
  formatRegularMessage(content) {
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  }

  // ==================== API调用 ====================

  /**
   * 调用聊天API
   * 对应业务逻辑: callChatAPI(message)
   */
  async callChatAPI(message) {
    const response = await fetch(`${this.apiBaseUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        sessionUuid: this.sessionUuid
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 加载聊天历史
   * 对应业务逻辑: loadChatHistory()
   */
  async loadChatHistory() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/chat/history/${this.sessionUuid}`);
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.success && data.data.messages.length > 0) {
          // 清除欢迎消息
          this.messagesContainer.innerHTML = '';
          
          // 添加历史消息
          data.data.messages.forEach(msg => {
            const type = msg.message_type === 'user' ? 'user' : 'bot';
            this.addMessage(msg.message_content, type, msg.metadata_json || {});
          });
        }
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    }
  }

  // ==================== 界面控制 ====================

  /**
   * 设置加载状态
   * 对应业务逻辑: setLoading(loading)
   */
  setLoading(loading) {
    this.isLoading = loading;
    
    if (loading) {
      this.loadingIndicator.style.display = 'flex';
      this.sendButton.disabled = true;
      this.messageInput.disabled = true;
    } else {
      this.loadingIndicator.style.display = 'none';
      this.sendButton.disabled = false;
      this.messageInput.disabled = false;
    }
  }

  /**
   * 滚动到底部
   * 对应业务逻辑: scrollToBottom()
   */
  scrollToBottom() {
    setTimeout(() => {
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }, 100);
  }

  /**
   * 更新字符计数
   * 对应业务逻辑: updateCharCount()
   */
  updateCharCount() {
    const charCount = this.messageInput.value.length;
    const charCountElement = this.chatContainer.querySelector('.char-count');
    if (charCountElement) {
      charCountElement.textContent = `${charCount}/2000`;
      
      if (charCount > 1800) {
        charCountElement.style.color = '#ff4444';
      } else if (charCount > 1500) {
        charCountElement.style.color = '#ff8800';
      } else {
        charCountElement.style.color = '#666';
      }
    }
  }

  /**
   * 自动调整文本框高度
   * 对应业务逻辑: autoResizeTextarea()
   */
  autoResizeTextarea() {
    this.messageInput.style.height = 'auto';
    const newHeight = Math.min(this.messageInput.scrollHeight, 120); // 最大高度120px
    this.messageInput.style.height = newHeight + 'px';
  }

  // ==================== 工具方法 ====================

  /**
   * 获取或创建会话UUID
   * 对应业务逻辑: getOrCreateSessionUuid()
   */
  getOrCreateSessionUuid() {
    let sessionUuid = localStorage.getItem('chatSessionUuid');
    if (!sessionUuid) {
      sessionUuid = generateUUID();
      this.saveSessionUuid(sessionUuid);
    }
    return sessionUuid;
  }

  /**
   * 保存会话UUID
   * 对应业务逻辑: saveSessionUuid(uuid)
   */
  saveSessionUuid(uuid = null) {
    const uuidToSave = uuid || this.sessionUuid;
    localStorage.setItem('chatSessionUuid', uuidToSave);
  }

  /**
   * 格式化时间
   * 对应业务逻辑: formatTime(date)
   */
  formatTime(date) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * 清除聊天记录
   * 对应业务逻辑: clearChat()
   */
  clearChat() {
    this.messagesContainer.innerHTML = '';
    this.messageHistory = [];
    this.sessionUuid = generateUUID();
    this.saveSessionUuid();
    
    // 重新显示欢迎消息
    this.addMessage(
      '您好！我是AI招聘顾问Katrina，专注于为技术人才推荐合适的职位机会。\n\n请告诉我您的技术方向和求职需求，我会为您推荐最匹配的职位。',
      'bot'
    );
  }

  /**
   * 销毁聊天界面
   * 对应业务逻辑: destroy()
   */
  destroy() {
    // 移除事件监听器
    this.eventListeners.forEach((listener, element) => {
      element.removeEventListener(listener.event, listener.handler);
    });
    this.eventListeners.clear();
    
    // 清空容器
    if (this.chatContainer) {
      this.chatContainer.innerHTML = '';
    }
  }
}

// 导出聊天界面类
export default ChatInterface;
