/**
 * Katrina AI 前端主要逻辑和状态管理
 * React应用核心逻辑、状态管理、API调用
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import axios from 'axios';

// ==================== 全局状态管理 ====================

/**
 * 应用状态管理Hook
 */
function useAppState() {
  // 核心状态
  const [user, setUser] = useState(null);
  const [currentSession, setCurrentSession] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // UI状态
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [recommendations, setRecommendations] = useState([]);
  
  // 配置状态
  const [apiConfig, setApiConfig] = useState({
    baseURL: '/api',
    timeout: 30000
  });

  return {
    // 核心状态
    user, setUser,
    currentSession, setCurrentSession,
    messages, setMessages,
    isLoading, setIsLoading,
    error, setError,
    
    // UI状态
    isSidebarOpen, setIsSidebarOpen,
    isTyping, setIsTyping,
    inputValue, setInputValue,
    recommendations, setRecommendations,
    
    // 配置
    apiConfig, setApiConfig
  };
}

/**
 * API服务管理Hook
 */
function useApiService(apiConfig) {
  const apiClient = useMemo(() => {
    return axios.create({
      baseURL: apiConfig.baseURL,
      timeout: apiConfig.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }, [apiConfig]);

  // 请求拦截器
  useEffect(() => {
    const requestInterceptor = apiClient.interceptors.request.use(
      (config) => {
        console.log('🚀 API请求:', config.method?.toUpperCase(), config.url);
        return config;
      },
      (error) => {
        console.error('❌ 请求错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    const responseInterceptor = apiClient.interceptors.response.use(
      (response) => {
        console.log('✅ API响应:', response.status, response.config.url);
        return response;
      },
      (error) => {
        console.error('❌ 响应错误:', error.response?.status, error.message);
        return Promise.reject(error);
      }
    );

    return () => {
      apiClient.interceptors.request.eject(requestInterceptor);
      apiClient.interceptors.response.eject(responseInterceptor);
    };
  }, [apiClient]);

  // API方法
  const sendMessage = useCallback(async (message, userEmail, sessionId) => {
    try {
      const response = await apiClient.post('/chat', {
        message,
        userEmail,
        sessionId
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || '发送消息失败');
    }
  }, [apiClient]);

  const createSession = useCallback(async (userEmail) => {
    try {
      const response = await apiClient.post('/chat/session', {
        userEmail
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || '创建会话失败');
    }
  }, [apiClient]);

  const getChatHistory = useCallback(async (sessionId, options = {}) => {
    try {
      const response = await apiClient.get(`/chat/history/${sessionId}`, {
        params: options
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || '获取历史记录失败');
    }
  }, [apiClient]);

  const getUserProfile = useCallback(async (email) => {
    try {
      const response = await apiClient.get(`/user/${email}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // 用户不存在
      }
      throw new Error(error.response?.data?.error || '获取用户信息失败');
    }
  }, [apiClient]);

  const updateUserProfile = useCallback(async (email, profileData) => {
    try {
      const response = await apiClient.put(`/user/${email}`, profileData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || '更新用户信息失败');
    }
  }, [apiClient]);

  const getRecommendations = useCallback(async (candidateEmail, options = {}) => {
    try {
      const response = await apiClient.post('/recommendations', {
        candidateEmail,
        options
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.error || '获取推荐失败');
    }
  }, [apiClient]);

  return {
    sendMessage,
    createSession,
    getChatHistory,
    getUserProfile,
    updateUserProfile,
    getRecommendations
  };
}

/**
 * 聊天逻辑管理Hook
 */
function useChatLogic(apiService, state) {
  const {
    user, setUser,
    currentSession, setCurrentSession,
    messages, setMessages,
    isLoading, setIsLoading,
    error, setError,
    isTyping, setIsTyping,
    recommendations, setRecommendations
  } = state;

  // 初始化用户和会话
  const initializeChat = useCallback(async (userEmail) => {
    try {
      setIsLoading(true);
      setError(null);

      // 获取或创建用户
      let userProfile = await apiService.getUserProfile(userEmail);
      if (!userProfile) {
        // 用户不存在，创建新会话时会自动创建用户
        console.log('新用户，将在发送第一条消息时创建');
      }

      // 创建新会话
      const sessionResult = await apiService.createSession(userEmail);
      if (sessionResult.success) {
        setCurrentSession({
          id: sessionResult.data.sessionId,
          createdAt: sessionResult.data.createdAt
        });
        setUser({ email: userEmail });
        setMessages([]);
      }

    } catch (error) {
      console.error('初始化聊天失败:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [apiService, setUser, setCurrentSession, setMessages, setIsLoading, setError]);

  // 发送消息
  const sendMessage = useCallback(async (messageText) => {
    if (!messageText.trim() || !user?.email || !currentSession?.id) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setIsTyping(true);

      // 添加用户消息到界面
      const userMessage = {
        id: Date.now(),
        role: 'user',
        content: messageText,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, userMessage]);

      // 发送到后端
      const result = await apiService.sendMessage(
        messageText,
        user.email,
        currentSession.id
      );

      if (result.success) {
        // 添加助手回复
        const assistantMessage = {
          id: result.data.messageId,
          role: 'assistant',
          content: result.data.response,
          timestamp: new Date().toISOString(),
          context: result.data.context
        };
        setMessages(prev => [...prev, assistantMessage]);

        // 更新推荐
        if (result.data.recommendations) {
          setRecommendations(result.data.recommendations);
        }
      }

    } catch (error) {
      console.error('发送消息失败:', error);
      setError(error.message);
      
      // 添加错误消息
      const errorMessage = {
        id: Date.now() + 1,
        role: 'error',
        content: '抱歉，发送消息时出现了问题，请稍后再试。',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, [user, currentSession, apiService, setMessages, setIsLoading, setError, setIsTyping, setRecommendations]);

  // 加载历史消息
  const loadChatHistory = useCallback(async (sessionId) => {
    try {
      setIsLoading(true);
      const result = await apiService.getChatHistory(sessionId);
      
      if (result.success) {
        setMessages(result.data.messages);
      }
    } catch (error) {
      console.error('加载历史消息失败:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [apiService, setMessages, setIsLoading, setError]);

  // 重新开始对话
  const restartChat = useCallback(async () => {
    if (!user?.email) return;

    try {
      setIsLoading(true);
      await initializeChat(user.email);
    } catch (error) {
      console.error('重新开始对话失败:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [user, initializeChat, setIsLoading, setError]);

  return {
    initializeChat,
    sendMessage,
    loadChatHistory,
    restartChat
  };
}

/**
 * 本地存储管理Hook
 */
function useLocalStorage() {
  const saveUserEmail = useCallback((email) => {
    try {
      localStorage.setItem('katrina_user_email', email);
    } catch (error) {
      console.warn('保存用户邮箱失败:', error);
    }
  }, []);

  const getUserEmail = useCallback(() => {
    try {
      return localStorage.getItem('katrina_user_email');
    } catch (error) {
      console.warn('获取用户邮箱失败:', error);
      return null;
    }
  }, []);

  const saveSessionId = useCallback((sessionId) => {
    try {
      localStorage.setItem('katrina_session_id', sessionId);
    } catch (error) {
      console.warn('保存会话ID失败:', error);
    }
  }, []);

  const getSessionId = useCallback(() => {
    try {
      return localStorage.getItem('katrina_session_id');
    } catch (error) {
      console.warn('获取会话ID失败:', error);
      return null;
    }
  }, []);

  const clearStorage = useCallback(() => {
    try {
      localStorage.removeItem('katrina_user_email');
      localStorage.removeItem('katrina_session_id');
    } catch (error) {
      console.warn('清除存储失败:', error);
    }
  }, []);

  return {
    saveUserEmail,
    getUserEmail,
    saveSessionId,
    getSessionId,
    clearStorage
  };
}

// ==================== 主要组件 ====================

/**
 * 消息组件
 */
function Message({ message, isTyping }) {
  const messageRef = useRef(null);

  useEffect(() => {
    if (messageRef.current) {
      messageRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  const getMessageClass = () => {
    const baseClass = 'message';
    if (message.role === 'user') return `${baseClass} user-message`;
    if (message.role === 'error') return `${baseClass} error-message`;
    return `${baseClass} assistant-message`;
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div ref={messageRef} className={getMessageClass()}>
      <div className="message-content">
        {message.content}
        {isTyping && message.role === 'assistant' && (
          <span className="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </span>
        )}
      </div>
      <div className="message-timestamp">
        {formatTimestamp(message.timestamp)}
      </div>
      {message.context && (
        <div className="message-context">
          <small>
            Tokens: {message.context.tokensUsed} | 
            Tier: {message.context.apiTier}
          </small>
        </div>
      )}
    </div>
  );
}

/**
 * 推荐卡片组件
 */
function RecommendationCard({ recommendation, onApply }) {
  return (
    <div className="recommendation-card">
      <div className="recommendation-header">
        <h4>{recommendation.title}</h4>
        <span className="company-name">{recommendation.company_name}</span>
      </div>
      
      <div className="recommendation-details">
        <div className="detail-item">
          <span className="label">💰 薪资:</span>
          <span className="value">{recommendation.salary_range}</span>
        </div>
        <div className="detail-item">
          <span className="label">📍 地点:</span>
          <span className="value">{recommendation.location}</span>
        </div>
        <div className="detail-item">
          <span className="label">🎯 匹配度:</span>
          <span className="value">{recommendation.match_score}%</span>
        </div>
      </div>

      {recommendation.match_reasons && (
        <div className="match-reasons">
          <p><strong>匹配原因:</strong></p>
          <ul>
            {recommendation.match_reasons.slice(0, 3).map((reason, index) => (
              <li key={index}>{reason}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="recommendation-actions">
        <button 
          className="apply-button"
          onClick={() => onApply(recommendation)}
        >
          了解详情
        </button>
      </div>
    </div>
  );
}

/**
 * 推荐面板组件
 */
function RecommendationPanel({ recommendations, onApply, onClose }) {
  if (!recommendations || recommendations.length === 0) {
    return null;
  }

  return (
    <div className="recommendation-panel">
      <div className="panel-header">
        <h3>💼 职位推荐</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      
      <div className="recommendations-list">
        {recommendations.map((rec, index) => (
          <RecommendationCard
            key={rec.id || index}
            recommendation={rec}
            onApply={onApply}
          />
        ))}
      </div>
    </div>
  );
}

/**
 * 输入框组件
 */
function ChatInput({ value, onChange, onSend, disabled, placeholder }) {
  const inputRef = useRef(null);

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  };

  const handleSend = () => {
    if (value.trim() && !disabled) {
      onSend();
    }
  };

  useEffect(() => {
    if (inputRef.current && !disabled) {
      inputRef.current.focus();
    }
  }, [disabled]);

  return (
    <div className="chat-input-container">
      <div className="input-wrapper">
        <textarea
          ref={inputRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder || "输入您的问题..."}
          disabled={disabled}
          rows={1}
          className="chat-input"
        />
        <button
          onClick={handleSend}
          disabled={disabled || !value.trim()}
          className="send-button"
        >
          发送
        </button>
      </div>
    </div>
  );
}

/**
 * 侧边栏组件
 */
function Sidebar({ isOpen, onClose, user, onRestart, onClearHistory }) {
  return (
    <div className={`sidebar ${isOpen ? 'open' : ''}`}>
      <div className="sidebar-header">
        <h3>Katrina AI</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      
      <div className="sidebar-content">
        {user && (
          <div className="user-info">
            <h4>用户信息</h4>
            <p>邮箱: {user.email}</p>
          </div>
        )}
        
        <div className="sidebar-actions">
          <button onClick={onRestart} className="action-button">
            🔄 重新开始对话
          </button>
          <button onClick={onClearHistory} className="action-button">
            🗑️ 清除历史记录
          </button>
        </div>
        
        <div className="sidebar-info">
          <h4>功能介绍</h4>
          <ul>
            <li>💼 智能职位推荐</li>
            <li>📄 简历分析优化</li>
            <li>💰 薪资市场分析</li>
            <li>🎯 职业发展建议</li>
            <li>🤝 面试指导准备</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * 错误提示组件
 */
function ErrorMessage({ error, onDismiss }) {
  if (!error) return null;

  return (
    <div className="error-message">
      <span>❌ {error}</span>
      <button onClick={onDismiss} className="dismiss-button">×</button>
    </div>
  );
}

/**
 * 加载指示器组件
 */
function LoadingIndicator({ isLoading, message = "处理中..." }) {
  if (!isLoading) return null;

  return (
    <div className="loading-indicator">
      <div className="spinner"></div>
      <span>{message}</span>
    </div>
  );
}

/**
 * 欢迎界面组件
 */
function WelcomeScreen({ onStart }) {
  const [email, setEmail] = useState('');
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    setIsValid(emailRegex.test(email));
  }, [email]);

  const handleStart = () => {
    if (isValid) {
      onStart(email);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && isValid) {
      handleStart();
    }
  };

  return (
    <div className="welcome-screen">
      <div className="welcome-content">
        <div className="logo">
          <h1>🤖 Katrina AI</h1>
          <p>您的专业AI招聘顾问</p>
        </div>
        
        <div className="welcome-description">
          <h2>我能为您提供：</h2>
          <ul>
            <li>💼 个性化职位推荐</li>
            <li>📊 简历分析和优化建议</li>
            <li>💰 薪资市场分析</li>
            <li>🎯 职业发展规划</li>
            <li>🤝 面试技巧指导</li>
          </ul>
        </div>
        
        <div className="email-input-section">
          <h3>请输入您的邮箱开始对话：</h3>
          <div className="email-input-wrapper">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="<EMAIL>"
              className="email-input"
            />
            <button
              onClick={handleStart}
              disabled={!isValid}
              className="start-button"
            >
              开始对话
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 主应用组件
 */
function KatrinaApp() {
  // 状态管理
  const state = useAppState();
  const localStorage = useLocalStorage();
  const apiService = useApiService(state.apiConfig);
  const chatLogic = useChatLogic(apiService, state);

  const {
    user,
    currentSession,
    messages,
    isLoading,
    error,
    isSidebarOpen,
    isTyping,
    inputValue,
    recommendations,
    setError,
    setIsSidebarOpen,
    setInputValue,
    setRecommendations
  } = state;

  // 应用初始化
  useEffect(() => {
    const savedEmail = localStorage.getUserEmail();
    if (savedEmail) {
      chatLogic.initializeChat(savedEmail);
    }
  }, []);

  // 保存用户邮箱
  useEffect(() => {
    if (user?.email) {
      localStorage.saveUserEmail(user.email);
    }
  }, [user, localStorage]);

  // 保存会话ID
  useEffect(() => {
    if (currentSession?.id) {
      localStorage.saveSessionId(currentSession.id);
    }
  }, [currentSession, localStorage]);

  // 事件处理
  const handleStartChat = useCallback((email) => {
    chatLogic.initializeChat(email);
  }, [chatLogic]);

  const handleSendMessage = useCallback(() => {
    if (inputValue.trim()) {
      chatLogic.sendMessage(inputValue);
      setInputValue('');
    }
  }, [inputValue, chatLogic, setInputValue]);

  const handleRecommendationApply = useCallback((recommendation) => {
    const message = `我对${recommendation.company_name}的${recommendation.title}职位很感兴趣，能告诉我更多详情吗？`;
    setInputValue(message);
  }, [setInputValue]);

  const handleRestartChat = useCallback(() => {
    chatLogic.restartChat();
    setIsSidebarOpen(false);
  }, [chatLogic, setIsSidebarOpen]);

  const handleClearHistory = useCallback(() => {
    localStorage.clearStorage();
    window.location.reload();
  }, [localStorage]);

  // 渲染
  if (!user) {
    return <WelcomeScreen onStart={handleStartChat} />;
  }

  return (
    <div className="katrina-app">
      {/* 侧边栏 */}
      <Sidebar
        isOpen={isSidebarOpen}
        onClose={() => setIsSidebarOpen(false)}
        user={user}
        onRestart={handleRestartChat}
        onClearHistory={handleClearHistory}
      />

      {/* 主聊天区域 */}
      <div className="chat-container">
        {/* 头部 */}
        <div className="chat-header">
          <button
            className="menu-button"
            onClick={() => setIsSidebarOpen(true)}
          >
            ☰
          </button>
          <h1>Katrina AI 招聘顾问</h1>
          <div className="header-status">
            {isLoading && <span className="status-indicator loading">●</span>}
            {!isLoading && <span className="status-indicator online">●</span>}
          </div>
        </div>

        {/* 错误提示 */}
        <ErrorMessage
          error={error}
          onDismiss={() => setError(null)}
        />

        {/* 消息列表 */}
        <div className="messages-container">
          {messages.length === 0 && !isLoading && (
            <div className="welcome-message">
              <h3>👋 您好！我是Katrina</h3>
              <p>我是您的专业AI招聘顾问，很高兴为您服务！</p>
              <p>您可以：</p>
              <ul>
                <li>告诉我您的技能和经验，我来推荐合适的职位</li>
                <li>询问特定公司或职位的信息</li>
                <li>上传简历让我帮您分析</li>
                <li>咨询薪资和职业发展建议</li>
              </ul>
            </div>
          )}
          
          {messages.map((message) => (
            <Message
              key={message.id}
              message={message}
              isTyping={isTyping && message === messages[messages.length - 1]}
            />
          ))}
          
          <LoadingIndicator isLoading={isLoading} />
        </div>

        {/* 推荐面板 */}
        <RecommendationPanel
          recommendations={recommendations}
          onApply={handleRecommendationApply}
          onClose={() => setRecommendations([])}
        />

        {/* 输入区域 */}
        <ChatInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSendMessage}
          disabled={isLoading}
          placeholder="输入您的问题..."
        />
      </div>
    </div>
  );
}

// ==================== 应用启动 ====================

/**
 * 应用入口
 */
function initializeApp() {
  const container = document.getElementById('root');
  if (!container) {
    console.error('找不到根容器元素');
    return;
  }

  const root = createRoot(container);
  root.render(<KatrinaApp />);
  
  console.log('🚀 Katrina AI 应用已启动');
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// ==================== 导出 ====================

export {
  KatrinaApp,
  useAppState,
  useApiService,
  useChatLogic,
  useLocalStorage
};

export default KatrinaApp;