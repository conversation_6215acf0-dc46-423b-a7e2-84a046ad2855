/**
 * Katrina AI 前端UI组件库
 * 可复用的React组件集合，包含样式和交互逻辑
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';

// ==================== 基础组件 ====================

/**
 * 按钮组件
 */
export function Button({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  disabled = false, 
  loading = false,
  icon = null,
  onClick,
  className = '',
  ...props 
}) {
  const getButtonClass = () => {
    const baseClass = 'btn';
    const variantClass = `btn-${variant}`;
    const sizeClass = `btn-${size}`;
    const disabledClass = disabled ? 'btn-disabled' : '';
    const loadingClass = loading ? 'btn-loading' : '';
    
    return [baseClass, variantClass, sizeClass, disabledClass, loadingClass, className]
      .filter(Boolean)
      .join(' ');
  };

  return (
    <button
      className={getButtonClass()}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && <span className="btn-spinner"></span>}
      {icon && !loading && <span className="btn-icon">{icon}</span>}
      <span className="btn-text">{children}</span>
    </button>
  );
}

/**
 * 输入框组件
 */
export function Input({ 
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  error = null,
  label = null,
  required = false,
  className = '',
  ...props 
}) {
  const inputId = useMemo(() => `input-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={`input-group ${className}`}>
      {label && (
        <label htmlFor={inputId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      <input
        id={inputId}
        type={type}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={`input ${error ? 'input-error' : ''}`}
        {...props}
      />
      {error && <span className="input-error-text">{error}</span>}
    </div>
  );
}

/**
 * 文本域组件
 */
export function Textarea({ 
  value,
  onChange,
  placeholder,
  disabled = false,
  error = null,
  label = null,
  required = false,
  rows = 3,
  autoResize = false,
  className = '',
  ...props 
}) {
  const textareaRef = useRef(null);
  const textareaId = useMemo(() => `textarea-${Math.random().toString(36).substr(2, 9)}`, []);

  // 自动调整高度
  useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value, autoResize]);

  return (
    <div className={`input-group ${className}`}>
      {label && (
        <label htmlFor={textareaId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      <textarea
        ref={textareaRef}
        id={textareaId}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        className={`textarea ${error ? 'input-error' : ''}`}
        {...props}
      />
      {error && <span className="input-error-text">{error}</span>}
    </div>
  );
}

/**
 * 选择框组件
 */
export function Select({ 
  value,
  onChange,
  options = [],
  placeholder = '请选择...',
  disabled = false,
  error = null,
  label = null,
  required = false,
  className = '',
  ...props 
}) {
  const selectId = useMemo(() => `select-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={`input-group ${className}`}>
      {label && (
        <label htmlFor={selectId} className="input-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
      )}
      <select
        id={selectId}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        className={`select ${error ? 'input-error' : ''}`}
        {...props}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <span className="input-error-text">{error}</span>}
    </div>
  );
}

/**
 * 复选框组件
 */
export function Checkbox({ 
  checked,
  onChange,
  label,
  disabled = false,
  className = '',
  ...props 
}) {
  const checkboxId = useMemo(() => `checkbox-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={`checkbox-group ${className}`}>
      <input
        id={checkboxId}
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange?.(e.target.checked)}
        disabled={disabled}
        className="checkbox"
        {...props}
      />
      {label && (
        <label htmlFor={checkboxId} className="checkbox-label">
          {label}
        </label>
      )}
    </div>
  );
}

// ==================== 布局组件 ====================

/**
 * 卡片组件
 */
export function Card({ 
  children, 
  title = null,
  subtitle = null,
  actions = null,
  hoverable = false,
  className = '',
  ...props 
}) {
  return (
    <div 
      className={`card ${hoverable ? 'card-hoverable' : ''} ${className}`}
      {...props}
    >
      {(title || subtitle || actions) && (
        <div className="card-header">
          <div className="card-title-section">
            {title && <h3 className="card-title">{title}</h3>}
            {subtitle && <p className="card-subtitle">{subtitle}</p>}
          </div>
          {actions && <div className="card-actions">{actions}</div>}
        </div>
      )}
      <div className="card-content">
        {children}
      </div>
    </div>
  );
}

/**
 * 模态框组件
 */
export function Modal({ 
  isOpen,
  onClose,
  title,
  children,
  footer = null,
  size = 'medium',
  closable = true,
  maskClosable = true,
  className = ''
}) {
  const modalRef = useRef(null);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape' && closable) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, closable, onClose]);

  // 处理点击遮罩关闭
  const handleMaskClick = (e) => {
    if (maskClosable && e.target === e.currentTarget) {
      onClose?.();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleMaskClick}>
      <div 
        ref={modalRef}
        className={`modal modal-${size} ${className}`}
        onClick={(e) => e.stopPropagation()}
      >
        {(title || closable) && (
          <div className="modal-header">
            {title && <h2 className="modal-title">{title}</h2>}
            {closable && (
              <button className="modal-close" onClick={onClose}>
                ×
              </button>
            )}
          </div>
        )}
        
        <div className="modal-content">
          {children}
        </div>
        
        {footer && (
          <div className="modal-footer">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 抽屉组件
 */
export function Drawer({ 
  isOpen,
  onClose,
  title,
  children,
  placement = 'right',
  width = '300px',
  closable = true,
  maskClosable = true,
  className = ''
}) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleMaskClick = () => {
    if (maskClosable) {
      onClose?.();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="drawer-overlay" onClick={handleMaskClick}>
      <div 
        className={`drawer drawer-${placement} ${className}`}
        style={{ width: placement === 'left' || placement === 'right' ? width : '100%' }}
        onClick={(e) => e.stopPropagation()}
      >
        {(title || closable) && (
          <div className="drawer-header">
            {title && <h3 className="drawer-title">{title}</h3>}
            {closable && (
              <button className="drawer-close" onClick={onClose}>
                ×
              </button>
            )}
          </div>
        )}
        
        <div className="drawer-content">
          {children}
        </div>
      </div>
    </div>
  );
}

// ==================== 反馈组件 ====================

/**
 * 消息提示组件
 */
export function Message({ 
  type = 'info',
  content,
  duration = 3000,
  onClose,
  closable = true,
  className = ''
}) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setVisible(false);
        onClose?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const handleClose = () => {
    setVisible(false);
    onClose?.();
  };

  if (!visible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info':
      default: return 'ℹ️';
    }
  };

  return (
    <div className={`message message-${type} ${className}`}>
      <span className="message-icon">{getIcon()}</span>
      <span className="message-content">{content}</span>
      {closable && (
        <button className="message-close" onClick={handleClose}>
          ×
        </button>
      )}
    </div>
  );
}

/**
 * 加载组件
 */
export function Loading({ 
  size = 'medium',
  text = null,
  spinning = false,
  children = null,
  className = ''
}) {
  if (spinning && children) {
    return (
      <div className={`loading-wrapper ${className}`}>
        <div className="loading-overlay">
          <div className={`loading loading-${size}`}>
            <div className="loading-spinner"></div>
            {text && <div className="loading-text">{text}</div>}
          </div>
        </div>
        <div className="loading-content">{children}</div>
      </div>
    );
  }

  return (
    <div className={`loading loading-${size} ${className}`}>
      <div className="loading-spinner"></div>
      {text && <div className="loading-text">{text}</div>}
    </div>
  );
}

/**
 * 进度条组件
 */
export function Progress({ 
  percent = 0,
  status = 'normal',
  showInfo = true,
  strokeWidth = 8,
  className = ''
}) {
  const normalizedPercent = Math.max(0, Math.min(100, percent));

  const getStatusColor = () => {
    switch (status) {
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
      case 'warning': return '#faad14';
      case 'normal':
      default: return '#1890ff';
    }
  };

  return (
    <div className={`progress ${className}`}>
      <div className="progress-outer">
        <div 
          className="progress-inner"
          style={{ height: strokeWidth }}
        >
          <div 
            className={`progress-bg progress-${status}`}
            style={{ 
              width: `${normalizedPercent}%`,
              backgroundColor: getStatusColor()
            }}
          />
        </div>
      </div>
      {showInfo && (
        <span className="progress-text">
          {status === 'success' ? '✅' : `${normalizedPercent}%`}
        </span>
      )}
    </div>
  );
}

// ==================== 数据展示组件 ====================

/**
 * 标签组件
 */
export function Tag({ 
  children,
  color = 'default',
  size = 'medium',
  closable = false,
  onClose,
  className = ''
}) {
  const handleClose = (e) => {
    e.stopPropagation();
    onClose?.();
  };

  return (
    <span className={`tag tag-${color} tag-${size} ${className}`}>
      <span className="tag-content">{children}</span>
      {closable && (
        <button className="tag-close" onClick={handleClose}>
          ×
        </button>
      )}
    </span>
  );
}

/**
 * 头像组件
 */
export function Avatar({ 
  src = null,
  alt = '',
  size = 'medium',
  shape = 'circle',
  icon = null,
  children = null,
  className = ''
}) {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const renderContent = () => {
    if (src && !imageError) {
      return (
        <img 
          src={src} 
          alt={alt}
          onError={handleImageError}
          className="avatar-image"
        />
      );
    }
    
    if (icon) {
      return <span className="avatar-icon">{icon}</span>;
    }
    
    if (children) {
      return <span className="avatar-text">{children}</span>;
    }
    
    return <span className="avatar-icon">👤</span>;
  };

  return (
    <div className={`avatar avatar-${size} avatar-${shape} ${className}`}>
      {renderContent()}
    </div>
  );
}

/**
 * 徽章组件
 */
export function Badge({ 
  count = 0,
  showZero = false,
  overflowCount = 99,
  dot = false,
  status = null,
  text = null,
  children = null,
  className = ''
}) {
  const shouldShow = count > 0 || showZero || dot || status;
  
  if (!shouldShow && !children) return null;

  const getDisplayCount = () => {
    if (dot) return '';
    if (count > overflowCount) return `${overflowCount}+`;
    return count;
  };

  const badgeElement = shouldShow && (
    <span className={`badge ${dot ? 'badge-dot' : ''} ${status ? `badge-${status}` : ''}`}>
      {!dot && getDisplayCount()}
    </span>
  );

  if (children) {
    return (
      <span className={`badge-wrapper ${className}`}>
        {children}
        {badgeElement}
      </span>
    );
  }

  return (
    <span className={`badge-standalone ${className}`}>
      {badgeElement}
      {text && <span className="badge-text">{text}</span>}
    </span>
  );
}

// ==================== 聊天专用组件 ====================

/**
 * 聊天消息气泡
 */
export function ChatBubble({ 
  message,
  isOwn = false,
  avatar = null,
  timestamp = null,
  status = null,
  className = ''
}) {
  const formatTime = (time) => {
    if (!time) return '';
    return new Date(time).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`chat-bubble-wrapper ${isOwn ? 'own' : 'other'} ${className}`}>
      {!isOwn && avatar && (
        <div className="chat-avatar">
          {typeof avatar === 'string' ? (
            <Avatar src={avatar} size="small" />
          ) : (
            avatar
          )}
        </div>
      )}
      
      <div className="chat-bubble-content">
        <div className={`chat-bubble ${isOwn ? 'chat-bubble-own' : 'chat-bubble-other'}`}>
          {message}
        </div>
        
        <div className="chat-bubble-meta">
          {timestamp && (
            <span className="chat-timestamp">{formatTime(timestamp)}</span>
          )}
          {status && (
            <span className={`chat-status chat-status-${status}`}>
              {status === 'sending' && '⏳'}
              {status === 'sent' && '✓'}
              {status === 'delivered' && '✓✓'}
              {status === 'read' && '✓✓'}
              {status === 'failed' && '❌'}
            </span>
          )}
        </div>
      </div>
      
      {isOwn && avatar && (
        <div className="chat-avatar">
          {typeof avatar === 'string' ? (
            <Avatar src={avatar} size="small" />
          ) : (
            avatar
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 打字指示器
 */
export function TypingIndicator({ 
  users = [],
  className = ''
}) {
  if (users.length === 0) return null;

  const getText = () => {
    if (users.length === 1) {
      return `${users[0]} 正在输入...`;
    }
    return `${users.slice(0, 2).join(', ')} ${users.length > 2 ? `等${users.length}人` : ''} 正在输入...`;
  };

  return (
    <div className={`typing-indicator ${className}`}>
      <div className="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span className="typing-text">{getText()}</span>
    </div>
  );
}

/**
 * 职位卡片组件
 */
export function JobCard({ 
  job,
  onApply,
  onSave,
  onShare,
  isSaved = false,
  className = ''
}) {
  const {
    title,
    company_name,
    location,
    salary_range,
    experience_required,
    education_required,
    job_type,
    description,
    requirements,
    benefits,
    match_score,
    posted_date
  } = job;

  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('zh-CN');
  };

  return (
    <Card 
      className={`job-card ${className}`}
      hoverable
      title={
        <div className="job-card-header">
          <div className="job-title-section">
            <h3 className="job-title">{title}</h3>
            <p className="company-name">{company_name}</p>
          </div>
          {match_score && (
            <div className="match-score">
              <span className="score-value">{match_score}%</span>
              <span className="score-label">匹配度</span>
            </div>
          )}
        </div>
      }
      actions={
        <div className="job-card-actions">
          <Button 
            variant="primary" 
            size="small"
            onClick={() => onApply?.(job)}
          >
            申请职位
          </Button>
          <Button 
            variant="outline" 
            size="small"
            icon={isSaved ? '❤️' : '🤍'}
            onClick={() => onSave?.(job)}
          >
            {isSaved ? '已收藏' : '收藏'}
          </Button>
          <Button 
            variant="text" 
            size="small"
            icon="📤"
            onClick={() => onShare?.(job)}
          >
            分享
          </Button>
        </div>
      }
    >
      <div className="job-details">
        <div className="job-meta">
          <div className="meta-item">
            <span className="meta-label">📍 地点:</span>
            <span className="meta-value">{location}</span>
          </div>
          <div className="meta-item">
            <span className="meta-label">💰 薪资:</span>
            <span className="meta-value">{salary_range}</span>
          </div>
          <div className="meta-item">
            <span className="meta-label">💼 类型:</span>
            <span className="meta-value">{job_type}</span>
          </div>
          <div className="meta-item">
            <span className="meta-label">🎓 学历:</span>
            <span className="meta-value">{education_required}</span>
          </div>
          <div className="meta-item">
            <span className="meta-label">⏰ 经验:</span>
            <span className="meta-value">{experience_required}</span>
          </div>
          {posted_date && (
            <div className="meta-item">
              <span className="meta-label">📅 发布:</span>
              <span className="meta-value">{formatDate(posted_date)}</span>
            </div>
          )}
        </div>

        {description && (
          <div className="job-description">
            <h4>职位描述</h4>
            <p>{description}</p>
          </div>
        )}

        {requirements && requirements.length > 0 && (
          <div className="job-requirements">
            <h4>任职要求</h4>
            <ul>
              {requirements.slice(0, 3).map((req, index) => (
                <li key={index}>{req}</li>
              ))}
              {requirements.length > 3 && (
                <li className="more-items">还有 {requirements.length - 3} 项要求...</li>
              )}
            </ul>
          </div>
        )}

        {benefits && benefits.length > 0 && (
          <div className="job-benefits">
            <h4>福利待遇</h4>
            <div className="benefits-tags">
              {benefits.slice(0, 4).map((benefit, index) => (
                <Tag key={index} color="blue" size="small">
                  {benefit}
                </Tag>
              ))}
              {benefits.length > 4 && (
                <Tag color="gray" size="small">
                  +{benefits.length - 4}
                </Tag>
              )}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

/**
 * 文件上传组件
 */
export function FileUpload({ 
  accept = '*',
  multiple = false,
  maxSize = 10 * 1024 * 1024, // 10MB
  onUpload,
  onError,
  disabled = false,
  className = ''
}) {
  const fileInputRef = useRef(null);
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = (files) => {
    const fileList = Array.from(files);
    
    // 验证文件大小
    const oversizedFiles = fileList.filter(file => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      onError?.(`文件大小不能超过 ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
      return;
    }

    setUploading(true);
    onUpload?.(fileList)
      .finally(() => setUploading(false));
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (!disabled) {
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files);
      }
    }
  };

  return (
    <div 
      className={`file-upload ${dragOver ? 'drag-over' : ''} ${disabled ? 'disabled' : ''} ${className}`}
      onClick={handleClick}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        disabled={disabled}
        onChange={(e) => handleFileSelect(e.target.files)}
        style={{ display: 'none' }}
      />
      
      <div className="upload-content">
        {uploading ? (
          <Loading text="上传中..." />
        ) : (
          <>
            <div className="upload-icon">📁</div>
            <div className="upload-text">
              <p>点击或拖拽文件到此处上传</p>
              <p className="upload-hint">
                支持 {accept === '*' ? '所有格式' : accept}，
                最大 {(maxSize / 1024 / 1024).toFixed(1)}MB
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// ==================== 导出所有组件 ====================

export default {
  // 基础组件
  Button,
  Input,
  Textarea,
  Select,
  Checkbox,
  
  // 布局组件
  Card,
  Modal,
  Drawer,
  
  // 反馈组件
  Message,
  Loading,
  Progress,
  
  // 数据展示组件
  Tag,
  Avatar,
  Badge,
  
  // 聊天专用组件
  ChatBubble,
  TypingIndicator,
  JobCard,
  FileUpload
};