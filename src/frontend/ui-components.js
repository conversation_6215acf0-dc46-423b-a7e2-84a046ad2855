/**
 * UI组件库
 * 包含可复用的UI组件和样式定义
 */

// ==================== 基础组件 ====================

/**
 * 创建按钮组件
 * 对应业务逻辑: createButton(options)
 */
export function createButton(options = {}) {
  const {
    text = '按钮',
    type = 'primary',
    size = 'medium',
    disabled = false,
    onClick = null,
    icon = null,
    className = ''
  } = options;

  const button = document.createElement('button');
  button.className = `btn btn-${type} btn-${size} ${className}`.trim();
  button.disabled = disabled;

  if (icon) {
    const iconElement = document.createElement('span');
    iconElement.className = 'btn-icon';
    iconElement.innerHTML = icon;
    button.appendChild(iconElement);
  }

  const textElement = document.createElement('span');
  textElement.textContent = text;
  button.appendChild(textElement);

  if (onClick) {
    button.addEventListener('click', onClick);
  }

  return button;
}

/**
 * 创建输入框组件
 * 对应业务逻辑: createInput(options)
 */
export function createInput(options = {}) {
  const {
    type = 'text',
    placeholder = '',
    value = '',
    disabled = false,
    required = false,
    maxLength = null,
    className = '',
    onChange = null
  } = options;

  const input = document.createElement('input');
  input.type = type;
  input.placeholder = placeholder;
  input.value = value;
  input.disabled = disabled;
  input.required = required;
  input.className = `input ${className}`.trim();

  if (maxLength) {
    input.maxLength = maxLength;
  }

  if (onChange) {
    input.addEventListener('input', onChange);
  }

  return input;
}

/**
 * 创建文本域组件
 * 对应业务逻辑: createTextarea(options)
 */
export function createTextarea(options = {}) {
  const {
    placeholder = '',
    value = '',
    rows = 3,
    disabled = false,
    required = false,
    maxLength = null,
    className = '',
    onChange = null
  } = options;

  const textarea = document.createElement('textarea');
  textarea.placeholder = placeholder;
  textarea.value = value;
  textarea.rows = rows;
  textarea.disabled = disabled;
  textarea.required = required;
  textarea.className = `textarea ${className}`.trim();

  if (maxLength) {
    textarea.maxLength = maxLength;
  }

  if (onChange) {
    textarea.addEventListener('input', onChange);
  }

  return textarea;
}

/**
 * 创建选择框组件
 * 对应业务逻辑: createSelect(options)
 */
export function createSelect(options = {}) {
  const {
    options: selectOptions = [],
    value = '',
    disabled = false,
    required = false,
    className = '',
    onChange = null
  } = options;

  const select = document.createElement('select');
  select.disabled = disabled;
  select.required = required;
  select.className = `select ${className}`.trim();

  selectOptions.forEach(option => {
    const optionElement = document.createElement('option');
    optionElement.value = option.value;
    optionElement.textContent = option.text;
    optionElement.selected = option.value === value;
    select.appendChild(optionElement);
  });

  if (onChange) {
    select.addEventListener('change', onChange);
  }

  return select;
}

// ==================== 复合组件 ====================

/**
 * 创建表单字段组件
 * 对应业务逻辑: createFormField(options)
 */
export function createFormField(options = {}) {
  const {
    label = '',
    type = 'text',
    required = false,
    error = '',
    help = '',
    inputOptions = {}
  } = options;

  const fieldContainer = document.createElement('div');
  fieldContainer.className = 'form-field';

  // 标签
  if (label) {
    const labelElement = document.createElement('label');
    labelElement.className = 'form-label';
    labelElement.textContent = label;
    if (required) {
      labelElement.innerHTML += ' <span class="required">*</span>';
    }
    fieldContainer.appendChild(labelElement);
  }

  // 输入控件
  let inputElement;
  switch (type) {
    case 'textarea':
      inputElement = createTextarea(inputOptions);
      break;
    case 'select':
      inputElement = createSelect(inputOptions);
      break;
    default:
      inputElement = createInput({ type, ...inputOptions });
  }

  fieldContainer.appendChild(inputElement);

  // 帮助文本
  if (help) {
    const helpElement = document.createElement('div');
    helpElement.className = 'form-help';
    helpElement.textContent = help;
    fieldContainer.appendChild(helpElement);
  }

  // 错误信息
  if (error) {
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    errorElement.textContent = error;
    fieldContainer.appendChild(errorElement);
  }

  return {
    container: fieldContainer,
    input: inputElement,
    setError: (errorText) => {
      let errorElement = fieldContainer.querySelector('.form-error');
      if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        fieldContainer.appendChild(errorElement);
      }
      errorElement.textContent = errorText;
      fieldContainer.classList.add('has-error');
    },
    clearError: () => {
      const errorElement = fieldContainer.querySelector('.form-error');
      if (errorElement) {
        errorElement.remove();
      }
      fieldContainer.classList.remove('has-error');
    }
  };
}

/**
 * 创建卡片组件
 * 对应业务逻辑: createCard(options)
 */
export function createCard(options = {}) {
  const {
    title = '',
    content = '',
    footer = '',
    className = '',
    onClick = null
  } = options;

  const card = document.createElement('div');
  card.className = `card ${className}`.trim();

  if (title) {
    const header = document.createElement('div');
    header.className = 'card-header';
    header.innerHTML = `<h3 class="card-title">${title}</h3>`;
    card.appendChild(header);
  }

  if (content) {
    const body = document.createElement('div');
    body.className = 'card-body';
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else {
      body.appendChild(content);
    }
    card.appendChild(body);
  }

  if (footer) {
    const footerElement = document.createElement('div');
    footerElement.className = 'card-footer';
    if (typeof footer === 'string') {
      footerElement.innerHTML = footer;
    } else {
      footerElement.appendChild(footer);
    }
    card.appendChild(footerElement);
  }

  if (onClick) {
    card.style.cursor = 'pointer';
    card.addEventListener('click', onClick);
  }

  return card;
}

/**
 * 创建模态框组件
 * 对应业务逻辑: createModal(options)
 */
export function createModal(options = {}) {
  const {
    title = '',
    content = '',
    size = 'medium',
    closable = true,
    onClose = null
  } = options;

  const overlay = document.createElement('div');
  overlay.className = 'modal-overlay';

  const modal = document.createElement('div');
  modal.className = `modal modal-${size}`;

  // 头部
  const header = document.createElement('div');
  header.className = 'modal-header';
  
  if (title) {
    const titleElement = document.createElement('h3');
    titleElement.className = 'modal-title';
    titleElement.textContent = title;
    header.appendChild(titleElement);
  }

  if (closable) {
    const closeButton = document.createElement('button');
    closeButton.className = 'modal-close';
    closeButton.innerHTML = '×';
    closeButton.addEventListener('click', () => {
      if (onClose) onClose();
      overlay.remove();
    });
    header.appendChild(closeButton);
  }

  modal.appendChild(header);

  // 内容
  const body = document.createElement('div');
  body.className = 'modal-body';
  if (typeof content === 'string') {
    body.innerHTML = content;
  } else {
    body.appendChild(content);
  }
  modal.appendChild(body);

  overlay.appendChild(modal);

  // 点击遮罩关闭
  if (closable) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        if (onClose) onClose();
        overlay.remove();
      }
    });
  }

  return {
    element: overlay,
    show: () => {
      document.body.appendChild(overlay);
      setTimeout(() => overlay.classList.add('show'), 10);
    },
    hide: () => {
      overlay.classList.remove('show');
      setTimeout(() => overlay.remove(), 300);
    },
    setContent: (newContent) => {
      if (typeof newContent === 'string') {
        body.innerHTML = newContent;
      } else {
        body.innerHTML = '';
        body.appendChild(newContent);
      }
    }
  };
}

// ==================== 通知组件 ====================

/**
 * 创建通知组件
 * 对应业务逻辑: createNotification(options)
 */
export function createNotification(options = {}) {
  const {
    message = '',
    type = 'info',
    duration = 3000,
    closable = true
  } = options;

  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;

  const content = document.createElement('div');
  content.className = 'notification-content';
  content.textContent = message;
  notification.appendChild(content);

  if (closable) {
    const closeButton = document.createElement('button');
    closeButton.className = 'notification-close';
    closeButton.innerHTML = '×';
    closeButton.addEventListener('click', () => {
      notification.remove();
    });
    notification.appendChild(closeButton);
  }

  // 自动消失
  if (duration > 0) {
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, duration);
  }

  return notification;
}

/**
 * 显示通知
 * 对应业务逻辑: showNotification(message, type, duration)
 */
export function showNotification(message, type = 'info', duration = 3000) {
  let container = document.querySelector('.notification-container');
  if (!container) {
    container = document.createElement('div');
    container.className = 'notification-container';
    document.body.appendChild(container);
  }

  const notification = createNotification({ message, type, duration });
  container.appendChild(notification);

  // 添加动画
  setTimeout(() => notification.classList.add('show'), 10);

  return notification;
}

// ==================== 加载组件 ====================

/**
 * 创建加载指示器
 * 对应业务逻辑: createLoader(options)
 */
export function createLoader(options = {}) {
  const {
    size = 'medium',
    text = '加载中...',
    overlay = false
  } = options;

  const loader = document.createElement('div');
  loader.className = `loader loader-${size}`;

  if (overlay) {
    loader.classList.add('loader-overlay');
  }

  const spinner = document.createElement('div');
  spinner.className = 'loader-spinner';
  loader.appendChild(spinner);

  if (text) {
    const textElement = document.createElement('div');
    textElement.className = 'loader-text';
    textElement.textContent = text;
    loader.appendChild(textElement);
  }

  return loader;
}

// ==================== 工具函数 ====================

/**
 * 添加CSS样式
 * 对应业务逻辑: addStyles(css)
 */
export function addStyles(css) {
  const style = document.createElement('style');
  style.textContent = css;
  document.head.appendChild(style);
}

/**
 * 创建图标
 * 对应业务逻辑: createIcon(name, size)
 */
export function createIcon(name, size = 16) {
  const icons = {
    send: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>`,
    close: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`,
    check: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>`,
    warning: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>`,
    info: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>`
  };

  return icons[name] || '';
}

/**
 * 防抖函数
 * 对应业务逻辑: debounce(func, wait)
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * 对应业务逻辑: throttle(func, limit)
 */
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// ==================== 默认样式 ====================

/**
 * 获取默认CSS样式
 * 对应业务逻辑: getDefaultStyles()
 */
export function getDefaultStyles() {
  return `
    /* 基础样式重置 */
    .btn, .input, .textarea, .select {
      box-sizing: border-box;
      font-family: inherit;
      font-size: 14px;
      border: 1px solid #ddd;
      border-radius: 4px;
      outline: none;
      transition: all 0.2s ease;
    }

    /* 按钮样式 */
    .btn {
      padding: 8px 16px;
      cursor: pointer;
      background: #fff;
      color: #333;
    }

    .btn-primary {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }

    .btn-primary:hover {
      background: #0056b3;
      border-color: #0056b3;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* 输入框样式 */
    .input, .textarea, .select {
      padding: 8px 12px;
      width: 100%;
    }

    .input:focus, .textarea:focus, .select:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    /* 表单字段样式 */
    .form-field {
      margin-bottom: 16px;
    }

    .form-label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .required {
      color: #dc3545;
    }

    .form-help {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }

    .form-error {
      font-size: 12px;
      color: #dc3545;
      margin-top: 4px;
    }

    .has-error .input,
    .has-error .textarea,
    .has-error .select {
      border-color: #dc3545;
    }

    /* 卡片样式 */
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      background: white;
      overflow: hidden;
    }

    .card-header {
      padding: 16px;
      border-bottom: 1px solid #eee;
      background: #f8f9fa;
    }

    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .card-body {
      padding: 16px;
    }

    .card-footer {
      padding: 16px;
      border-top: 1px solid #eee;
      background: #f8f9fa;
    }

    /* 通知样式 */
    .notification-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }

    .notification {
      padding: 12px 16px;
      margin-bottom: 8px;
      border-radius: 4px;
      background: white;
      border: 1px solid #ddd;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification-info {
      border-left: 4px solid #007bff;
    }

    .notification-success {
      border-left: 4px solid #28a745;
    }

    .notification-warning {
      border-left: 4px solid #ffc107;
    }

    .notification-error {
      border-left: 4px solid #dc3545;
    }
  `;
}
