/**
 * 数据验证器
 * 包含各种数据验证和清理功能
 */

import { VALIDATION_CONFIG } from './app-config.js';

// ==================== 消息验证 ====================

/**
 * 验证用户消息
 * 对应业务逻辑: validateUserMessage(message)
 */
export function validateUserMessage(message) {
  const config = VALIDATION_CONFIG.message;
  
  if (!message || typeof message !== 'string') {
    return {
      valid: false,
      error: '消息不能为空',
      code: 'EMPTY_MESSAGE'
    };
  }
  
  const trimmed = message.trim();
  
  if (trimmed.length < config.minLength) {
    return {
      valid: false,
      error: `消息太短，至少需要${config.minLength}个字符`,
      code: 'MESSAGE_TOO_SHORT'
    };
  }
  
  if (trimmed.length > config.maxLength) {
    return {
      valid: false,
      error: `消息太长，请控制在${config.maxLength}字以内`,
      code: 'MESSAGE_TOO_LONG'
    };
  }
  
  if (!config.allowedChars.test(trimmed)) {
    return {
      valid: false,
      error: '消息包含非法字符',
      code: 'INVALID_CHARACTERS'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

/**
 * 清理用户消息
 * 对应业务逻辑: sanitizeUserMessage(message)
 */
export function sanitizeUserMessage(message) {
  if (!message) return '';
  
  return message
    .trim()
    .replace(/\s+/g, ' ') // 多个空格合并为一个
    .replace(/[<>]/g, '') // 移除可能的HTML标签
    .substring(0, VALIDATION_CONFIG.message.maxLength);
}

// ==================== 用户档案验证 ====================

/**
 * 验证用户姓名
 * 对应业务逻辑: validateUserName(name)
 */
export function validateUserName(name) {
  const config = VALIDATION_CONFIG.profile.name;
  
  if (!name || typeof name !== 'string') {
    return {
      valid: false,
      error: '姓名不能为空',
      code: 'EMPTY_NAME'
    };
  }
  
  const trimmed = name.trim();
  
  if (trimmed.length < config.minLength) {
    return {
      valid: false,
      error: `姓名太短，至少需要${config.minLength}个字符`,
      code: 'NAME_TOO_SHORT'
    };
  }
  
  if (trimmed.length > config.maxLength) {
    return {
      valid: false,
      error: `姓名太长，请控制在${config.maxLength}字以内`,
      code: 'NAME_TOO_LONG'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

/**
 * 验证邮箱地址
 * 对应业务逻辑: validateEmail(email)
 */
export function validateEmail(email) {
  const config = VALIDATION_CONFIG.profile.email;
  
  if (!email || typeof email !== 'string') {
    return {
      valid: false,
      error: '邮箱不能为空',
      code: 'EMPTY_EMAIL'
    };
  }
  
  const trimmed = email.trim().toLowerCase();
  
  if (!config.pattern.test(trimmed)) {
    return {
      valid: false,
      error: '邮箱格式不正确',
      code: 'INVALID_EMAIL_FORMAT'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

/**
 * 验证手机号码
 * 对应业务逻辑: validatePhone(phone)
 */
export function validatePhone(phone) {
  const config = VALIDATION_CONFIG.profile.phone;
  
  if (!phone || typeof phone !== 'string') {
    return {
      valid: false,
      error: '手机号不能为空',
      code: 'EMPTY_PHONE'
    };
  }
  
  const cleaned = phone.replace(/\s|-/g, ''); // 移除空格和横线
  
  if (!config.pattern.test(cleaned)) {
    return {
      valid: false,
      error: '手机号格式不正确',
      code: 'INVALID_PHONE_FORMAT'
    };
  }
  
  return {
    valid: true,
    cleaned: cleaned
  };
}

// ==================== 薪资验证 ====================

/**
 * 验证薪资范围
 * 对应业务逻辑: validateSalaryRange(salaryMin, salaryMax, unit)
 */
export function validateSalaryRange(salaryMin, salaryMax, unit) {
  const config = VALIDATION_CONFIG.salary;
  
  // 验证单位
  if (unit && !config.units.includes(unit)) {
    return {
      valid: false,
      error: `薪资单位不正确，支持的单位：${config.units.join('、')}`,
      code: 'INVALID_SALARY_UNIT'
    };
  }
  
  // 验证最小薪资
  if (salaryMin !== null && salaryMin !== undefined) {
    if (isNaN(salaryMin) || salaryMin < config.min || salaryMin > config.max) {
      return {
        valid: false,
        error: `最小薪资应在${config.min}-${config.max}万之间`,
        code: 'INVALID_MIN_SALARY'
      };
    }
  }
  
  // 验证最大薪资
  if (salaryMax !== null && salaryMax !== undefined) {
    if (isNaN(salaryMax) || salaryMax < config.min || salaryMax > config.max) {
      return {
        valid: false,
        error: `最大薪资应在${config.min}-${config.max}万之间`,
        code: 'INVALID_MAX_SALARY'
      };
    }
  }
  
  // 验证薪资范围逻辑
  if (salaryMin !== null && salaryMax !== null && salaryMin > salaryMax) {
    return {
      valid: false,
      error: '最小薪资不能大于最大薪资',
      code: 'INVALID_SALARY_RANGE'
    };
  }
  
  return {
    valid: true,
    cleaned: {
      min: salaryMin,
      max: salaryMax,
      unit: unit || '万'
    }
  };
}

/**
 * 验证薪资字符串
 * 对应业务逻辑: validateSalaryString(salaryString)
 */
export function validateSalaryString(salaryString) {
  if (!salaryString || typeof salaryString !== 'string') {
    return {
      valid: false,
      error: '薪资信息不能为空',
      code: 'EMPTY_SALARY'
    };
  }
  
  const trimmed = salaryString.trim();
  
  // 检查是否包含数字
  if (!/\d/.test(trimmed)) {
    return {
      valid: false,
      error: '薪资信息应包含数字',
      code: 'NO_SALARY_NUMBER'
    };
  }
  
  // 检查是否包含有效单位
  const validUnits = ['万', 'w', 'k', '千'];
  const hasValidUnit = validUnits.some(unit => trimmed.includes(unit));
  
  if (!hasValidUnit) {
    return {
      valid: false,
      error: '薪资信息应包含单位（万、k、千）',
      code: 'NO_SALARY_UNIT'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

// ==================== 技术方向验证 ====================

/**
 * 验证技术方向
 * 对应业务逻辑: validateTechDirection(techDirection)
 */
export function validateTechDirection(techDirection) {
  if (!techDirection || typeof techDirection !== 'string') {
    return {
      valid: false,
      error: '技术方向不能为空',
      code: 'EMPTY_TECH_DIRECTION'
    };
  }
  
  const trimmed = techDirection.trim();
  
  if (trimmed.length < 2) {
    return {
      valid: false,
      error: '技术方向太短',
      code: 'TECH_DIRECTION_TOO_SHORT'
    };
  }
  
  if (trimmed.length > 50) {
    return {
      valid: false,
      error: '技术方向太长',
      code: 'TECH_DIRECTION_TOO_LONG'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

// ==================== 地理位置验证 ====================

/**
 * 验证地理位置
 * 对应业务逻辑: validateLocation(location)
 */
export function validateLocation(location) {
  if (!location || typeof location !== 'string') {
    return {
      valid: false,
      error: '地理位置不能为空',
      code: 'EMPTY_LOCATION'
    };
  }
  
  const trimmed = location.trim();
  
  if (trimmed.length < 2) {
    return {
      valid: false,
      error: '地理位置太短',
      code: 'LOCATION_TOO_SHORT'
    };
  }
  
  if (trimmed.length > 30) {
    return {
      valid: false,
      error: '地理位置太长',
      code: 'LOCATION_TOO_LONG'
    };
  }
  
  return {
    valid: true,
    cleaned: trimmed
  };
}

// ==================== 会话验证 ====================

/**
 * 验证会话UUID
 * 对应业务逻辑: validateSessionUuid(sessionUuid)
 */
export function validateSessionUuid(sessionUuid) {
  if (!sessionUuid || typeof sessionUuid !== 'string') {
    return {
      valid: false,
      error: '会话ID不能为空',
      code: 'EMPTY_SESSION_UUID'
    };
  }
  
  // UUID格式验证
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidPattern.test(sessionUuid)) {
    return {
      valid: false,
      error: '会话ID格式不正确',
      code: 'INVALID_SESSION_UUID'
    };
  }
  
  return {
    valid: true,
    cleaned: sessionUuid.toLowerCase()
  };
}

// ==================== 文件验证 ====================

/**
 * 验证文件类型
 * 对应业务逻辑: validateFileType(fileName, allowedTypes)
 */
export function validateFileType(fileName, allowedTypes = []) {
  if (!fileName || typeof fileName !== 'string') {
    return {
      valid: false,
      error: '文件名不能为空',
      code: 'EMPTY_FILENAME'
    };
  }
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  if (!extension) {
    return {
      valid: false,
      error: '文件没有扩展名',
      code: 'NO_FILE_EXTENSION'
    };
  }
  
  if (allowedTypes.length > 0 && !allowedTypes.includes(extension)) {
    return {
      valid: false,
      error: `不支持的文件类型，支持的类型：${allowedTypes.join('、')}`,
      code: 'UNSUPPORTED_FILE_TYPE'
    };
  }
  
  return {
    valid: true,
    extension: extension
  };
}

/**
 * 验证文件大小
 * 对应业务逻辑: validateFileSize(fileSize, maxSize)
 */
export function validateFileSize(fileSize, maxSize = 10 * 1024 * 1024) { // 默认10MB
  if (!fileSize || isNaN(fileSize)) {
    return {
      valid: false,
      error: '文件大小无效',
      code: 'INVALID_FILE_SIZE'
    };
  }
  
  if (fileSize <= 0) {
    return {
      valid: false,
      error: '文件不能为空',
      code: 'EMPTY_FILE'
    };
  }
  
  if (fileSize > maxSize) {
    const maxSizeMB = Math.round(maxSize / 1024 / 1024);
    return {
      valid: false,
      error: `文件太大，最大支持${maxSizeMB}MB`,
      code: 'FILE_TOO_LARGE'
    };
  }
  
  return {
    valid: true,
    size: fileSize
  };
}

// ==================== 批量验证 ====================

/**
 * 批量验证候选人档案
 * 对应业务逻辑: validateCandidateProfile(profile)
 */
export function validateCandidateProfile(profile) {
  const errors = [];
  
  // 验证姓名
  if (profile.name) {
    const nameValidation = validateUserName(profile.name);
    if (!nameValidation.valid) {
      errors.push({ field: 'name', ...nameValidation });
    }
  }
  
  // 验证邮箱
  if (profile.email) {
    const emailValidation = validateEmail(profile.email);
    if (!emailValidation.valid) {
      errors.push({ field: 'email', ...emailValidation });
    }
  }
  
  // 验证手机号
  if (profile.phone) {
    const phoneValidation = validatePhone(profile.phone);
    if (!phoneValidation.valid) {
      errors.push({ field: 'phone', ...phoneValidation });
    }
  }
  
  // 验证技术方向
  if (profile.techDirection) {
    const techValidation = validateTechDirection(profile.techDirection);
    if (!techValidation.valid) {
      errors.push({ field: 'techDirection', ...techValidation });
    }
  }
  
  // 验证地理位置
  if (profile.location) {
    const locationValidation = validateLocation(profile.location);
    if (!locationValidation.valid) {
      errors.push({ field: 'location', ...locationValidation });
    }
  }
  
  // 验证薪资
  if (profile.expectedSalary) {
    const salaryValidation = validateSalaryString(profile.expectedSalary);
    if (!salaryValidation.valid) {
      errors.push({ field: 'expectedSalary', ...salaryValidation });
    }
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  };
}

// ==================== 导出验证结果格式化 ====================

/**
 * 格式化验证错误
 * 对应业务逻辑: formatValidationErrors(errors)
 */
export function formatValidationErrors(errors) {
  if (!errors || errors.length === 0) {
    return '验证通过';
  }
  
  return errors.map(error => {
    const fieldName = getFieldDisplayName(error.field);
    return `${fieldName}: ${error.error}`;
  }).join('; ');
}

/**
 * 获取字段显示名称
 * 对应业务逻辑: getFieldDisplayName(fieldName)
 */
function getFieldDisplayName(fieldName) {
  const fieldNames = {
    name: '姓名',
    email: '邮箱',
    phone: '手机号',
    techDirection: '技术方向',
    location: '地理位置',
    expectedSalary: '期望薪资',
    message: '消息内容'
  };
  
  return fieldNames[fieldName] || fieldName;
}
