/**
 * 通用工具函数
 * 包含数据处理、格式化、验证等通用功能
 */

import { STOP_WORDS, NOISE_WORDS } from './mapping-tables.js';

// ==================== 输入处理工具 ====================

/**
 * 标准化输入
 * 对应业务逻辑: normalizeInput(input)
 */
export function normalizeInput(input) {
  if (!input) return '';
  
  return input
    .toLowerCase()
    .trim()
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 只保留中文、英文、数字
    .replace(/\s+/g, ''); // 移除空格
}

/**
 * 清理噪声词
 * 对应业务逻辑: removeNoiseWords(input)
 */
export function removeNoiseWords(input) {
  if (!input) return '';
  
  let cleaned = input;
  for (const noise of NOISE_WORDS) {
    cleaned = cleaned.replace(new RegExp(noise, 'g'), '');
  }
  
  return cleaned.trim();
}

/**
 * 提取关键词
 * 对应业务逻辑: extractKeywords(input)
 */
export function extractKeywords(input) {
  if (!input) return [];

  // 简单的关键词提取，移除停用词
  const words = input.split(/\s+/);
  return words.filter(word => 
    word.length > 1 && !STOP_WORDS.includes(word)
  );
}

// ==================== 薪资处理工具 ====================

/**
 * 薪资解析 - 智能解析各种薪资表达格式
 * 对应业务逻辑: parseSalaryRange(salaryString)
 */
export function parseSalaryRange(salaryString) {
  if (!salaryString) {
    return { min: null, max: null, unit: "万", original: salaryString };
  }

  try {
    const normalized = salaryString.toLowerCase().replace(/\s+/g, '');
    
    // 1. 范围格式：20-30万、20k-30k、20-30k
    const rangePatterns = [
      /(\d+(?:\.\d+)?)[万w]-(\d+(?:\.\d+)?)[万w]/,
      /(\d+(?:\.\d+)?)[k千]-(\d+(?:\.\d+)?)[k千]/,
      /(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)[万w]/,
      /(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)[k千]/,
      /(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)万/,
      /(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)k/
    ];

    for (const pattern of rangePatterns) {
      const match = normalized.match(pattern);
      if (match) {
        const min = parseFloat(match[1]);
        const max = parseFloat(match[2]);
        const unit = detectSalaryUnit(normalized);
        
        return {
          min: unit === 'k' ? min : min,
          max: unit === 'k' ? max : max,
          unit: unit === 'k' ? 'k' : '万',
          original: salaryString
        };
      }
    }

    // 2. 单一数值：30万、25k、年薪50万
    const singlePatterns = [
      /年薪(\d+(?:\.\d+)?)[万w]/,
      /(\d+(?:\.\d+)?)[万w]/,
      /(\d+(?:\.\d+)?)[k千]/,
      /(\d+(?:\.\d+)?)万/,
      /(\d+(?:\.\d+)?)k/
    ];

    for (const pattern of singlePatterns) {
      const match = normalized.match(pattern);
      if (match) {
        const value = parseFloat(match[1]);
        const unit = detectSalaryUnit(normalized);
        
        // 单一数值时，设置一个合理的范围
        const range = unit === 'k' ? 2 : 5; // k单位±2，万单位±5
        
        return {
          min: Math.max(0, value - range),
          max: value + range,
          unit: unit === 'k' ? 'k' : '万',
          original: salaryString
        };
      }
    }

    // 3. 特殊格式：面议、待定等
    const specialCases = {
      '面议': { min: null, max: null, unit: '万', original: salaryString },
      '待定': { min: null, max: null, unit: '万', original: salaryString },
      '协商': { min: null, max: null, unit: '万', original: salaryString }
    };

    if (specialCases[normalized]) {
      return specialCases[normalized];
    }

    // 4. 默认返回
    return { min: null, max: null, unit: "万", original: salaryString };
  } catch (error) {
    console.error('薪资解析失败:', error);
    return { min: null, max: null, unit: "万", original: salaryString };
  }
}

/**
 * 检测薪资单位
 * 对应业务逻辑: detectSalaryUnit(salaryString)
 */
export function detectSalaryUnit(salaryString) {
  const kUnits = ['k', '千'];
  const wUnits = ['万', 'w'];
  
  for (const unit of kUnits) {
    if (salaryString.includes(unit)) return 'k';
  }
  
  for (const unit of wUnits) {
    if (salaryString.includes(unit)) return '万';
  }
  
  // 默认单位判断：数值大于100通常是k，小于100通常是万
  const numbers = salaryString.match(/\d+/g);
  if (numbers && numbers.length > 0) {
    const firstNumber = parseInt(numbers[0]);
    return firstNumber > 100 ? 'k' : '万';
  }
  
  return '万';
}

/**
 * 薪资标准化 - 统一转换为万元单位
 * 对应业务逻辑: normalizeSalary(salaryValue, unit)
 */
export function normalizeSalary(salaryValue, unit) {
  if (!salaryValue || isNaN(salaryValue)) return null;
  
  switch (unit) {
    case 'k':
    case '千':
      return salaryValue / 10; // k转万
    case '万':
    case 'w':
      return salaryValue;
    default:
      return salaryValue;
  }
}

// ==================== 地理位置工具 ====================

/**
 * 提取地理位置关键词
 * 对应业务逻辑: extractLocationKeywords(locationString)
 */
export function extractLocationKeywords(locationString, locationMap) {
  if (!locationString) return [];

  const keywords = [];
  const normalizedLocation = locationString.toLowerCase();

  for (const [city, variants] of Object.entries(locationMap)) {
    if (
      variants.some((variant) =>
        normalizedLocation.includes(variant.toLowerCase())
      )
    ) {
      keywords.push(...variants);
    }
  }

  // 如果没有匹配到预定义城市，直接使用原始输入
  if (keywords.length === 0) {
    keywords.push(locationString);
  }

  return [...new Set(keywords)];
}

// ==================== 数据验证工具 ====================

/**
 * 验证邮箱格式
 * 对应业务逻辑: validateEmail(email)
 */
export function validateEmail(email) {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

/**
 * 验证手机号格式
 * 对应业务逻辑: validatePhone(phone)
 */
export function validatePhone(phone) {
  const phonePattern = /^1[3-9]\d{9}$/;
  return phonePattern.test(phone);
}

/**
 * 验证消息内容
 * 对应业务逻辑: validateMessage(message)
 */
export function validateMessage(message) {
  if (!message || typeof message !== 'string') {
    return { valid: false, error: '消息不能为空' };
  }
  
  if (message.length < 1) {
    return { valid: false, error: '消息太短' };
  }
  
  if (message.length > 2000) {
    return { valid: false, error: '消息太长，请控制在2000字以内' };
  }
  
  // 检查是否包含非法字符
  const allowedPattern = /^[\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:()（）。，！？；：""'']+$/;
  if (!allowedPattern.test(message)) {
    return { valid: false, error: '消息包含非法字符' };
  }
  
  return { valid: true };
}

// ==================== 格式化工具 ====================

/**
 * 格式化时间戳
 * 对应业务逻辑: formatTimestamp(timestamp)
 */
export function formatTimestamp(timestamp) {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;
  
  // 小于1分钟
  if (diff < 60000) {
    return '刚刚';
  }
  
  // 小于1小时
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}分钟前`;
  }
  
  // 小于1天
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }
  
  // 大于1天
  return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化文件大小
 * 对应业务逻辑: formatFileSize(bytes)
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

// ==================== 字符串工具 ====================

/**
 * 截断文本
 * 对应业务逻辑: truncateText(text, maxLength)
 */
export function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
}

/**
 * 高亮关键词
 * 对应业务逻辑: highlightKeywords(text, keywords)
 */
export function highlightKeywords(text, keywords) {
  if (!text || !keywords || keywords.length === 0) return text;
  
  let highlighted = text;
  for (const keyword of keywords) {
    const regex = new RegExp(`(${keyword})`, 'gi');
    highlighted = highlighted.replace(regex, '<mark>$1</mark>');
  }
  
  return highlighted;
}

// ==================== 数组工具 ====================

/**
 * 数组去重
 * 对应业务逻辑: uniqueArray(array)
 */
export function uniqueArray(array) {
  return [...new Set(array)];
}

/**
 * 数组分块
 * 对应业务逻辑: chunkArray(array, size)
 */
export function chunkArray(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// ==================== 对象工具 ====================

/**
 * 深度克隆对象
 * 对应业务逻辑: deepClone(obj)
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj);
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
}

/**
 * 合并对象
 * 对应业务逻辑: mergeObjects(target, source)
 */
export function mergeObjects(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = mergeObjects(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

// ==================== 随机工具 ====================

/**
 * 生成随机ID
 * 对应业务逻辑: generateRandomId(length)
 */
export function generateRandomId(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成UUID
 * 对应业务逻辑: generateUUID()
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
