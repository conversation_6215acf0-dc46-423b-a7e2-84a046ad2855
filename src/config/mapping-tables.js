/**
 * 映射表和常量定义
 * 包含技术方向映射、语义映射、地理位置映射等
 */

// ==================== 技术方向硬编码映射表 ====================

export const TECH_HIERARCHY_MAP = {
  // 一级技术方向
  推荐算法: 725,
  广告算法: 726,
  搜索算法: 727,
  "CV算法（计算机视觉）": 728,
  "NLP算法（自然语言处理）": 729,
  多模态算法: 730,
  "大模型（LLM）算法": 731,
  语音算法: 732,
  视频算法: 733,
  "通用机器学习 / 深度学习算法": 734,

  // 二级技术方向（映射到对应的一级ID）
  召回策略: 725,
  排序优化: 725,
  特征工程与Embedding建模: 725,
  图像识别: 728,
  目标检测: 728,
  图像分割: 728,
  文本分类: 729,
  情感分析: 729,
  命名实体识别: 729,
  语音识别: 732,
  语音合成: 732,
  视频理解: 733,
  视频生成: 733,
};

// ==================== 语义映射规则 ====================

export const SEMANTIC_MAPPINGS = {
  // CV相关
  图像识别: "CV算法（计算机视觉）",
  计算机视觉: "CV算法（计算机视觉）",
  cv: "CV算法（计算机视觉）",

  // NLP相关
  自然语言处理: "NLP算法（自然语言处理）",
  nlp: "NLP算法（自然语言处理）",

  // 大模型相关
  大模型: "大模型（LLM）算法",
  llm: "大模型（LLM）算法",
  gpt: "大模型（LLM）算法",

  // 推荐相关
  推荐系统: "推荐算法",
  推荐: "推荐算法",

  // 搜索相关
  搜索: "搜索算法",
  搜索引擎: "搜索算法",

  // 广告相关
  广告: "广告算法",
  广告投放: "广告算法",
};

// ==================== 地理位置映射 ====================

export const LOCATION_MAP = {
  北京: ["北京", "北京市", "朝阳", "海淀", "西城", "东城"],
  上海: ["上海", "上海市", "浦东", "徐汇", "黄浦", "静安"],
  深圳: ["深圳", "深圳市", "南山", "福田", "罗湖", "宝安"],
  杭州: ["杭州", "杭州市", "西湖", "滨江", "余杭", "萧山"],
  广州: ["广州", "广州市", "天河", "越秀", "海珠", "番禺"],
  成都: ["成都", "成都市", "锦江", "青羊", "金牛", "武侯"],
  南京: ["南京", "南京市", "玄武", "秦淮", "建邺", "鼓楼"],
  武汉: ["武汉", "武汉市", "江岸", "江汉", "硚口", "汉阳"],
  西安: ["西安", "西安市", "新城", "碑林", "莲湖", "灞桥"],
  苏州: ["苏州", "苏州市", "姑苏", "虎丘", "吴中", "相城"],
};

// ==================== 固定话术映射 ====================

export const FIXED_RESPONSES = {
  你是ai吗:
    "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
  你是机器人吗:
    "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
  你是人工智能吗:
    "Katrina是由Felton团队创造的，希望创造出更多交互的可能性去拥抱候选人。",
  你能做什么:
    "我是专业的AI猎头顾问，可以为您推荐合适的技术职位，分析职业发展路径。",
  你的功能: "我专注于技术人才的职位推荐和职业咨询服务。",
};

// ==================== 用户偏好映射 ====================

export const USER_PREFERENCE_MAP = {
  大厂: "bigTech",
  头部公司: "bigTech",
  国企: "stateOwned",
  央企: "stateOwned",
  中型公司: "medium",
  创业公司: "startup",
  初创公司: "startup",
};

// ==================== 薪资职级映射表 ====================

export const SALARY_LEVEL_MAP = {
  "80万+": "P8+/T8+",
  "60-80万": "P7/T7",
  "45-60万": "P6/T6",
  "30-45万": "P5/T5",
  "20-30万": "P4/T4",
  "20万以下": "P3-P4/T3-T4",
};

// ==================== 公司类型映射 ====================

export const COMPANY_TYPE_MAP = {
  bigTech: ["头部大厂", "大厂", "互联网巨头"],
  stateOwned: ["国企", "央企", "国有企业"],
  medium: ["中型公司", "中等规模"],
  startup: ["创业型公司", "初创公司", "创业公司"],
};

// ==================== 停用词和噪声词 ====================

export const STOP_WORDS = [
  "的",
  "和",
  "与",
  "或",
  "算法",
  "技术",
  "方向",
  "领域",
];

export const NOISE_WORDS = [
  "算法",
  "方向",
  "领域",
  "技术",
  "专业",
  "做",
  "从事",
];

// ==================== 意图类型常量 ====================

export const INTENT_TYPES = {
  JOB_RECOMMENDATION: "job_recommendation",
  JOB_QUESTION: "job_question",
  PREFERENCE_EXPRESSION: "preference_expression",
  INFO_COLLECTION: "info_collection",
  THIRD_PARTY_INQUIRY: "third_party_inquiry",
  USER_BACKGROUND_UPDATE: "user_background_update",
  RESUME_UPLOAD: "resume_upload",
  GENERAL_CHAT: "general_chat",
  JOB_RELATED_INQUIRY: "job_related_inquiry",
  COMPANY_INQUIRY: "company_inquiry",
  DETAILED_JD_INQUIRY: "detailed_jd_inquiry",
  SECOND_RECOMMENDATION: "second_recommendation",
};

// ==================== 推荐类型常量 ====================

export const RECOMMENDATION_TYPES = {
  PASSIVE: "passive",
  ACTIVE_GENERAL: "active_general",
  ACTIVE_SPECIFIC: "active_specific",
  ACTIVE_FILTERED: "active_filtered",
  THIRD_PARTY: "third_party",
  SECOND_RECOMMENDATION: "second_recommendation",
};

// ==================== 4x4分类常量 ====================

export const JOB_CATEGORIES = {
  BIG_TECH: "头部大厂",
  STATE_OWNED: "国企",
  MEDIUM: "中型公司",
  STARTUP: "创业型公司",
};

// ==================== 推荐配置 ====================

export const RECOMMENDATION_CONFIG = {
  matrix4x4: {
    maxJobsPerCategory: 4,
    fallbackEnabled: true,
    cacheExpiryTime: 30 * 60 * 1000, // 30分钟
  },
  general: {
    maxRecommendations: 16,
    diversityFactor: 0.3,
  },
  thirdParty: {
    maxRecommendations: 12,
    includeCompanyInfo: true,
  },
};

// ==================== 薪资关键词 ====================

export const SALARY_KEYWORDS = {
  units: ["万", "k", "千", "w"],
  ranges: ["以上", "以下", "左右", "之间"],
  modifiers: ["年薪", "月薪", "总包", "base"],
};
