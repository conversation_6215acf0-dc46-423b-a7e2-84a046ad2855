/**
 * 应用配置和环境变量
 * 包含数据库配置、AI服务配置、缓存配置等
 */

// ==================== 环境变量配置 ====================

export const ENV_CONFIG = {
  NODE_ENV: process.env.NODE_ENV || "development",
  PORT: process.env.PORT || 3000,

  // Supabase配置
  SUPABASE_URL: process.env.SUPABASE_URL || "",
  SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || "",

  // DeepSeek AI配置
  DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || "",
  DEEPSEEK_BASE_URL:
    process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com",

  // 其他API配置
  API_TIMEOUT: parseInt(process.env.API_TIMEOUT) || 30000,
  MAX_RETRIES: parseInt(process.env.MAX_RETRIES) || 3,
};

// ==================== 数据库配置 ====================

export const DATABASE_CONFIG = {
  // 连接配置
  connection: {
    url: ENV_CONFIG.SUPABASE_URL,
    key: ENV_CONFIG.SUPABASE_ANON_KEY,
    timeout: 10000,
    retries: 3,
  },

  // 表名配置
  tables: {
    users: "users",
    candidates: "candidates",
    chatSessions: "chat_sessions",
    chatMessages: "chat_messages",
    jobListings: "job_listings",
    companies: "companies",
    techTree: "tech_tree",
  },

  // 查询配置
  query: {
    defaultLimit: 50,
    maxLimit: 1000,
    timeout: 5000,
  },
};

// ==================== AI服务配置 ====================

export const AI_CONFIG = {
  // DeepSeek配置
  deepseek: {
    apiKey: ENV_CONFIG.DEEPSEEK_API_KEY,
    baseUrl: ENV_CONFIG.DEEPSEEK_BASE_URL,
    model: "deepseek-chat",
    timeout: 30000,
    maxRetries: 3,
  },

  // API分层策略配置
  apiTiers: {
    deepseek_light: {
      name: "DeepSeek轻量",
      maxTokens: 150,
      temperature: 0.3,
      cost: "low",
    },
    deepseek_medium: {
      name: "DeepSeek中等",
      maxTokens: 300,
      temperature: 0.5,
      cost: "medium",
    },
    deepseek_heavy: {
      name: "DeepSeek重型",
      maxTokens: 500,
      temperature: 0.7,
      cost: "high",
    },
    rules_engine: {
      name: "规则引擎",
      maxTokens: 0,
      temperature: 0,
      cost: "free",
    },
  },
};

// ==================== 缓存配置 ====================

export const CACHE_CONFIG = {
  // 推荐缓存
  recommendation: {
    ttl: 30 * 60 * 1000, // 30分钟
    maxSize: 1000,
    cleanupInterval: 5 * 60 * 1000, // 5分钟清理一次
  },

  // 歧义状态缓存
  ambiguity: {
    ttl: 10 * 60 * 1000, // 10分钟
    maxSize: 500,
    cleanupInterval: 2 * 60 * 1000, // 2分钟清理一次
  },

  // 用户会话缓存
  session: {
    ttl: 60 * 60 * 1000, // 1小时
    maxSize: 2000,
    cleanupInterval: 10 * 60 * 1000, // 10分钟清理一次
  },
};

// ==================== 推荐引擎配置 ====================

export const RECOMMENDATION_CONFIG = {
  // 4x4推荐配置
  matrix4x4: {
    maxJobsPerCategory: 4,
    categories: ["头部大厂", "国企", "中型公司", "创业型公司"],
    fallbackEnabled: true,
  },

  // 匹配权重配置
  matchingWeights: {
    techDirection: 0.4,
    salary: 0.25,
    location: 0.15,
    experience: 0.15,
    companyType: 0.05,
  },

  // 推荐历史配置
  history: {
    excludeDays: 7, // 排除最近7天推荐的公司
    maxExcludeCompanies: 50,
  },
};

// ==================== 验证配置 ====================

export const VALIDATION_CONFIG = {
  // 消息验证
  message: {
    minLength: 1,
    maxLength: 2000,
    allowedChars: /^[\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:()（）。，！？；：""'']+$/,
  },

  // 用户档案验证
  profile: {
    name: {
      minLength: 2,
      maxLength: 50,
    },
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
    phone: {
      pattern: /^1[3-9]\d{9}$/,
    },
  },

  // 薪资验证
  salary: {
    min: 0,
    max: 1000, // 万元
    units: ["万", "k", "千"],
  },
};

// ==================== 错误配置 ====================

export const ERROR_CONFIG = {
  // 错误类型
  types: {
    VALIDATION_ERROR: "ValidationError",
    DATABASE_ERROR: "DatabaseError",
    AI_SERVICE_ERROR: "AIServiceError",
    API_ERROR: "APIError",
    BUSINESS_ERROR: "BusinessError",
  },

  // 日志级别
  logLevels: {
    ERROR: "error",
    WARN: "warn",
    INFO: "info",
    DEBUG: "debug",
  },

  // 重试配置
  retry: {
    maxAttempts: 3,
    backoffMs: 1000,
    backoffMultiplier: 2,
  },
};

// ==================== 性能监控配置 ====================

export const PERFORMANCE_CONFIG = {
  // 响应时间阈值
  responseTime: {
    warning: 1000, // 1秒
    error: 3000, // 3秒
  },

  // 内存使用阈值
  memory: {
    warning: 256 * 1024 * 1024, // 256MB
    error: 512 * 1024 * 1024, // 512MB
  },

  // 监控间隔
  monitoring: {
    interval: 60000, // 1分钟
    retention: 24 * 60 * 60 * 1000, // 24小时
  },
};

// ==================== 导出默认配置 ====================

export const APP_CONFIG = {
  env: ENV_CONFIG,
  database: DATABASE_CONFIG,
  ai: AI_CONFIG,
  cache: CACHE_CONFIG,
  recommendation: RECOMMENDATION_CONFIG,
  validation: VALIDATION_CONFIG,
  error: ERROR_CONFIG,
  performance: PERFORMANCE_CONFIG,
  version: "1.0.0",
};

// 向后兼容的别名
export const DEFAULT_CONFIG = APP_CONFIG;
