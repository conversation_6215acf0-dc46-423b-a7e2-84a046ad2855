# Katrina AI 猎头聊天机器人 🤖

> 高并发、智能化的AI招聘助手，基于Next.js + Supabase + DeepSeek构建

## ✨ 特性

- 🚀 **高并发架构** - 支持数千用户同时在线
- 🌍 **全球部署** - Vercel边缘计算，全球低延迟
- 🧠 **智能对话** - DeepSeek驱动的自然语言理解
- 📊 **实时数据** - Supabase实时数据库
- 🔄 **自动扩展** - 根据流量自动分配资源
- 💰 **成本优化** - 按使用量付费，基础免费

## 🚀 快速部署（推荐）

### 一键部署到Vercel
```bash
# 运行自动部署脚本
npm run deploy

# 或者手动部署
npm run deploy:vercel
```

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

## ⚙️ 环境配置

创建 `.env.local` 文件：
```env
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DEEPSEEK_API_KEY=your-deepseek-api-key
LLM_API_ENDPOINT=https://api.deepseek.com/v1
```

## 📊 高并发性能

### 架构优势
- **Serverless架构** - 无需管理服务器
- **自动扩展** - 流量峰值自动处理
- **边缘计算** - API在全球边缘节点运行
- **智能缓存** - 自动优化响应速度

### 性能指标
- **并发用户** - 支持1000+同时在线
- **响应时间** - 全球平均<100ms
- **可用性** - 99.9%正常运行时间
- **扩展性** - 自动处理流量峰值

## 🧪 性能测试

```bash
# 运行高并发测试
npm run test:concurrency

# 运行基础功能测试
npm test
```

## 📁 项目结构

```
katrina-ai-chatbot/
├── pages/
│   ├── api/           # API接口
│   ├── index.js       # 主聊天页面
│   └── _app.js        # 应用配置
├── lib/
│   ├── chatEngine.js  # 核心对话引擎
│   ├── deepseekAgent.js # DeepSeek集成
│   ├── database.js    # 数据库操作
│   └── constants/     # 配置常量
├── public/            # 静态资源
├── styles/            # 样式文件
├── vercel.json        # Vercel配置
└── DEPLOYMENT.md      # 部署指南
```

## 🔧 核心功能

### 智能对话
- 自然语言理解
- 上下文记忆
- 多轮对话支持
- 意图识别

### 职位推荐
- 4x4推荐矩阵
- 智能匹配算法
- 实时职位数据
- 个性化推荐

### 数据管理
- 候选人档案管理
- 聊天记录存储
- 简历上传处理
- 行为数据分析

## 🌐 部署选项

### 1. Vercel（推荐）
- ✅ 零配置部署
- ✅ 自动扩展
- ✅ 全球CDN
- ✅ 免费额度

### 2. 其他平台
- Netlify
- AWS Lambda
- Google Cloud Functions
- Azure Functions

## 📈 监控与分析

### Vercel Dashboard
- 请求量统计
- 响应时间监控
- 错误率追踪
- 函数执行分析

### 自定义监控
```bash
# 查看实时日志
vercel logs

# 性能分析
npm run test:concurrency
```

## 🛠️ 开发指南

### 添加新功能
1. 在 `lib/` 目录添加核心逻辑
2. 在 `pages/api/` 添加API接口
3. 更新前端页面
4. 添加测试用例

### 数据库操作
- 使用 `lib/database.js` 统一接口
- 遵循 `lib/constants/dbSchema.js` 结构
- 不直接修改数据库结构

### API开发
- 遵循RESTful规范
- 添加错误处理
- 实现速率限制
- 记录操作日志

## 🆘 故障排除

### 常见问题
1. **部署失败** - 检查环境变量配置
2. **API超时** - 检查DeepSeek API连接
3. **数据库错误** - 验证Supabase配置
4. **高并发问题** - 查看Vercel函数日志

### 获取帮助
- 查看 `DEPLOYMENT.md` 详细部署指南
- 运行 `npm run test` 检查系统状态
- 查看Vercel Dashboard监控数据

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**🚀 现在就开始使用Katrina AI，体验高并发智能招聘助手！**
