// 技术方向映射测试界面 JavaScript

class TechMappingTester {
    constructor() {
        this.apiBaseUrl = 'http://localhost:3000';
        this.testEmail = '<EMAIL>';
        this.testHistory = [];
        this.currentConversationId = null;
        
        this.init();
    }

    async init() {
        await this.checkServerConnection();
        this.setupEventListeners();
        this.loadTestHistory();
    }

    // 检查服务器连接状态
    async checkServerConnection() {
        const statusElement = document.getElementById('connection-status');
        const statusText = document.getElementById('status-text');
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            if (response.ok) {
                statusElement.className = 'w-3 h-3 rounded-full bg-green-500';
                statusText.textContent = '服务器已连接';
                statusText.className = 'text-green-600';
            } else {
                throw new Error('Server not responding');
            }
        } catch (error) {
            statusElement.className = 'w-3 h-3 rounded-full bg-red-500';
            statusText.textContent = '服务器未连接';
            statusText.className = 'text-red-600';
            console.error('Server connection failed:', error);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        sendButton.addEventListener('click', () => this.sendMessage());
    }

    // 发送消息
    async sendMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput.value.trim();
        
        if (!message) return;
        
        // 清空输入框并显示用户消息
        messageInput.value = '';
        this.addUserMessage(message);
        
        // 显示加载状态
        const loadingId = this.addLoadingMessage();
        
        try {
            // 调用API
            const response = await this.callChatAPI(message);
            
            // 移除加载消息并显示回复
            this.removeMessage(loadingId);
            this.addBotMessage(response);
            
            // 记录测试历史
            this.recordTest(message, response);
            
        } catch (error) {
            this.removeMessage(loadingId);
            this.addErrorMessage('API调用失败: ' + error.message);
            console.error('API call failed:', error);
        }
    }

    // 调用聊天API
    async callChatAPI(message) {
        const url = `${this.apiBaseUrl}/api/chat/message?email=${encodeURIComponent(this.testEmail)}`;
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                timestamp: new Date().toISOString()
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
    }

    // 添加用户消息
    addUserMessage(message) {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-end gap-3 p-4 message-bubble justify-end';
        
        messageDiv.innerHTML = `
            <div class="flex flex-1 flex-col gap-1 items-end">
                <p class="text-[#4d5a99] text-[13px] font-normal leading-normal">您</p>
                <div class="text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 bg-[#1435db] text-white">
                    ${this.escapeHtml(message)}
                </div>
            </div>
            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBIKS6O4QdwVt6lDXI60DKR5Xx1EqHNrus5Nw-6INrwVflCMUodrWubOl6M0xaYxhzH2Iudn10zzMe-kA_2gvx1PatmVmgR-2b2aOcnVlvX2-My6hQ0_LOJUhRyfNaHjPU0q2xm4E4Mew0qTZ86q2Tv64l4ylMoHYd-W9pE5EdOfguVQXyoTjb5q6h0EmlHLdaxkAegVYgqU134P74xptaxMr_hz_FcZGIl2Ihff5lpFwPH_dPzSiLvgTlfD_BE1utSeybquKu1nGU");'></div>
        `;
        
        chatContainer.appendChild(messageDiv);
        this.scrollToBottom();
        
        return messageDiv;
    }

    // 添加机器人消息
    addBotMessage(response) {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-end gap-3 p-4 message-bubble';
        
        // 格式化响应内容
        const formattedContent = this.formatBotResponse(response);
        
        messageDiv.innerHTML = `
            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws");'></div>
            <div class="flex flex-1 flex-col gap-1 items-start">
                <p class="text-[#4d5a99] text-[13px] font-normal leading-normal">AI招聘助手</p>
                <div class="text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 bg-[#e7e9f3] text-[#0e101b]">
                    ${formattedContent}
                </div>
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        this.scrollToBottom();
        
        return messageDiv;
    }

    // 格式化机器人响应
    formatBotResponse(response) {
        if (typeof response === 'string') {
            return this.escapeHtml(response);
        }
        
        let formatted = '';
        
        // 显示主要回复
        if (response.reply) {
            formatted += `<div class="mb-3">${this.escapeHtml(response.reply)}</div>`;
        }
        
        // 显示技术方向映射信息
        if (response.extractedInfo && response.extractedInfo.techDirection) {
            formatted += `<div class="bg-blue-50 p-3 rounded-lg mb-3">
                <div class="font-medium text-blue-800 mb-1">🔍 技术方向识别</div>
                <div class="text-blue-700">识别到：${this.escapeHtml(response.extractedInfo.techDirection)}</div>
                ${response.extractedInfo.techDirectionId ? 
                    `<div class="text-blue-600 text-sm">映射ID：${response.extractedInfo.techDirectionId}</div>` : ''}
            </div>`;
        }
        
        // 显示歧义信息
        if (response.ambiguityOptions) {
            formatted += `<div class="bg-yellow-50 p-3 rounded-lg mb-3">
                <div class="font-medium text-yellow-800 mb-2">⚠️ 检测到歧义</div>
                <div class="text-yellow-700 mb-2">请选择您的具体方向：</div>
                <ul class="list-disc list-inside text-yellow-600 text-sm space-y-1">`;
            
            response.ambiguityOptions.forEach((option, index) => {
                formatted += `<li>${index + 1}. ${this.escapeHtml(option.parentTech || option.name)}</li>`;
            });
            
            formatted += `</ul></div>`;
        }
        
        // 显示推荐信息
        if (response.recommendations && response.recommendations.length > 0) {
            formatted += `<div class="bg-green-50 p-3 rounded-lg">
                <div class="font-medium text-green-800 mb-2">💼 职位推荐</div>
                <div class="text-green-700 text-sm">为您推荐了 ${response.recommendations.length} 个职位</div>
            </div>`;
        }
        
        return formatted || this.escapeHtml(JSON.stringify(response, null, 2));
    }

    // 添加加载消息
    addLoadingMessage() {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        const messageId = 'loading-' + Date.now();
        messageDiv.id = messageId;
        messageDiv.className = 'flex items-end gap-3 p-4 message-bubble';
        
        messageDiv.innerHTML = `
            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws");'></div>
            <div class="flex flex-1 flex-col gap-1 items-start">
                <p class="text-[#4d5a99] text-[13px] font-normal leading-normal">AI招聘助手</p>
                <div class="text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 bg-[#e7e9f3] text-[#0e101b] typing-indicator">
                    <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-[#4d5a99] rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-[#4d5a99] rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-[#4d5a99] rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        <span class="ml-2 text-[#4d5a99]">正在处理...</span>
                    </div>
                </div>
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        this.scrollToBottom();
        
        return messageId;
    }

    // 添加错误消息
    addErrorMessage(error) {
        const chatContainer = document.getElementById('chat-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-end gap-3 p-4 message-bubble';
        
        messageDiv.innerHTML = `
            <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws");'></div>
            <div class="flex flex-1 flex-col gap-1 items-start">
                <p class="text-[#4d5a99] text-[13px] font-normal leading-normal">系统</p>
                <div class="text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 bg-red-50 text-red-800 border border-red-200">
                    <div class="flex items-center gap-2">
                        <span>❌</span>
                        <span>${this.escapeHtml(error)}</span>
                    </div>
                </div>
            </div>
        `;
        
        chatContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    // 移除消息
    removeMessage(messageId) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            messageElement.remove();
        }
    }

    // 滚动到底部
    scrollToBottom() {
        const chatContainer = document.getElementById('chat-container');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 记录测试历史
    recordTest(input, response) {
        const testRecord = {
            timestamp: new Date().toISOString(),
            input: input,
            response: response,
            techDirection: response.extractedInfo?.techDirection || null,
            techDirectionId: response.extractedInfo?.techDirectionId || null,
            hasAmbiguity: !!response.ambiguityOptions
        };
        
        this.testHistory.unshift(testRecord);
        this.updateTestHistoryDisplay();
        
        // 保存到localStorage
        localStorage.setItem('techMappingTestHistory', JSON.stringify(this.testHistory.slice(0, 20)));
    }

    // 更新测试历史显示
    updateTestHistoryDisplay() {
        const historyContainer = document.getElementById('test-history');
        historyContainer.innerHTML = '';
        
        this.testHistory.slice(0, 10).forEach((record, index) => {
            const historyItem = document.createElement('div');
            historyItem.className = 'p-2 bg-gray-50 rounded border text-xs';
            
            const time = new Date(record.timestamp).toLocaleTimeString();
            const status = record.techDirectionId ? '✅' : (record.hasAmbiguity ? '⚠️' : '❌');
            
            historyItem.innerHTML = `
                <div class="font-medium">${status} ${this.escapeHtml(record.input)}</div>
                <div class="text-gray-600">${time}</div>
                ${record.techDirection ? `<div class="text-blue-600">→ ${this.escapeHtml(record.techDirection)}</div>` : ''}
            `;
            
            historyContainer.appendChild(historyItem);
        });
    }

    // 加载测试历史
    loadTestHistory() {
        const saved = localStorage.getItem('techMappingTestHistory');
        if (saved) {
            try {
                this.testHistory = JSON.parse(saved);
                this.updateTestHistoryDisplay();
            } catch (error) {
                console.error('Failed to load test history:', error);
            }
        }
    }
}

// 全局函数
function runQuickTest(techDirection) {
    const messageInput = document.getElementById('message-input');
    messageInput.value = `我是做${techDirection}的`;
    tester.sendMessage();
}

function runConsistencyTest() {
    const tests = ['CV算法', '计算机视觉', '图像识别', 'cv'];
    let index = 0;
    
    function runNext() {
        if (index < tests.length) {
            const messageInput = document.getElementById('message-input');
            messageInput.value = `我是做${tests[index]}的`;
            tester.sendMessage();
            index++;
            setTimeout(runNext, 2000); // 2秒间隔
        }
    }
    
    runNext();
}

function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        tester.sendMessage();
    }
}

// 初始化
let tester;
document.addEventListener('DOMContentLoaded', () => {
    tester = new TechMappingTester();
});
