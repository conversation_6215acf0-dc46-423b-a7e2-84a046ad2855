<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术方向映射测试 - AI招聘助手</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        .message-bubble {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
        .test-scenario {
            transition: all 0.2s ease;
        }
        .test-scenario:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#f8f9fc] group/design-root overflow-x-hidden" style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <!-- Header -->
            <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7e9f3] px-10 py-3">
                <div class="flex items-center gap-4 text-[#0e101b]">
                    <div class="size-4">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.8261 30.5736C16.7203 29.8826 20.2244 29.4783 24 29.4783C27.7756 29.4783 31.2797 29.8826 34.1739 30.5736C36.9144 31.2278 39.9967 32.7669 41.3563 33.8352L24.8486 7.36089C24.4571 6.73303 23.5429 6.73303 23.1514 7.36089L6.64374 33.8352C8.00331 32.7669 11.0856 31.2278 13.8261 30.5736Z" fill="currentColor"></path>
                        </svg>
                    </div>
                    <h2 class="text-[#0e101b] text-lg font-bold leading-tight tracking-[-0.015em]">AI招聘助手 - 技术方向映射测试</h2>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-3 h-3 rounded-full bg-green-500" id="connection-status"></div>
                        <span id="status-text" class="text-[#4d5a99]">连接中...</span>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCN3XxVhCpw1nTtG0ujy8-qMwUrZDcD3LAyRibaZ7NnjIH8CwMMmFB2mr5Cclvk0CqFHaa77nVn4strlX0ZXYs2Qa-UZa_1370xLe6vMDwBdTOPSh9VWoWl7UHIV4XO9VlpqfDArHf8wEjGAE0JVupjzJJ5nhTTKUA3w0znzuSAtbBj14BvDjk1Z832FuEYwLMtP-VAgBn6n3v1OJvKJRBuXJ01ovgqbWZIwU_LvPc9dOFc8Yiny8tklYnNhZC8n4zdDhVSmEO-Fz8");'></div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="flex flex-1">
                <!-- Left Panel - Test Scenarios -->
                <div class="w-80 border-r border-[#e7e9f3] bg-white p-4 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-[#0e101b] mb-4">🧪 测试场景</h3>
                    
                    <!-- Quick Test Buttons -->
                    <div class="space-y-2 mb-6">
                        <button onclick="runQuickTest('CV算法')" class="test-scenario w-full p-3 bg-blue-50 hover:bg-blue-100 rounded-lg text-left border border-blue-200">
                            <div class="font-medium text-blue-800">CV算法测试</div>
                            <div class="text-sm text-blue-600">测试计算机视觉相关映射</div>
                        </button>
                        
                        <button onclick="runQuickTest('大模型算法')" class="test-scenario w-full p-3 bg-purple-50 hover:bg-purple-100 rounded-lg text-left border border-purple-200">
                            <div class="font-medium text-purple-800">大模型算法测试</div>
                            <div class="text-sm text-purple-600">测试LLM相关歧义处理</div>
                        </button>
                        
                        <button onclick="runQuickTest('RAG')" class="test-scenario w-full p-3 bg-green-50 hover:bg-green-100 rounded-lg text-left border border-green-200">
                            <div class="font-medium text-green-800">RAG歧义测试</div>
                            <div class="text-sm text-green-600">测试歧义检测和澄清</div>
                        </button>
                        
                        <button onclick="runConsistencyTest()" class="test-scenario w-full p-3 bg-orange-50 hover:bg-orange-100 rounded-lg text-left border border-orange-200">
                            <div class="font-medium text-orange-800">一致性测试</div>
                            <div class="text-sm text-orange-600">测试多次输入的一致性</div>
                        </button>
                    </div>

                    <!-- Test History -->
                    <div class="border-t border-[#e7e9f3] pt-4">
                        <h4 class="font-medium text-[#0e101b] mb-2">📊 测试历史</h4>
                        <div id="test-history" class="space-y-2 text-sm">
                            <!-- Test history will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Right Panel - Chat Interface -->
                <div class="flex-1 flex flex-col">
                    <!-- Chat Messages -->
                    <div class="flex-1 px-8 py-4 overflow-y-auto" id="chat-container">
                        <div class="max-w-4xl mx-auto">
                            <!-- Welcome Message -->
                            <div class="flex items-end gap-3 p-4 message-bubble">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws");'></div>
                                <div class="flex flex-1 flex-col gap-1 items-start">
                                    <p class="text-[#4d5a99] text-[13px] font-normal leading-normal">AI招聘助手</p>
                                    <div class="text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 bg-[#e7e9f3] text-[#0e101b]">
                                        <div>
                                            <p class="mb-2">👋 欢迎使用技术方向映射测试界面！</p>
                                            <p class="mb-2">🔧 <strong>重构完成</strong>：已将9套不同的映射逻辑统一为1套</p>
                                            <p class="mb-2">✨ <strong>测试目标</strong>：验证技术方向识别的一致性和准确性</p>
                                            <p>💡 您可以：</p>
                                            <ul class="list-disc list-inside mt-1 space-y-1">
                                                <li>直接输入技术方向进行测试</li>
                                                <li>使用左侧快速测试按钮</li>
                                                <li>观察系统的识别和推荐结果</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <footer class="border-t border-[#e7e9f3] bg-white">
                        <div class="max-w-4xl mx-auto">
                            <div class="flex items-center px-4 py-3 gap-3">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 shrink-0" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBIKS6O4QdwVt6lDXI60DKR5Xx1EqHNrus5Nw-6INrwVflCMUodrWubOl6M0xaYxhzH2Iudn10zzMe-kA_2gvx1PatmVmgR-2b2aOcnVlvX2-My6hQ0_LOJUhRyfNaHjPU0q2xm4E4Mew0qTZ86q2Tv64l4ylMoHYd-W9pE5EdOfguVQXyoTjb5q6h0EmlHLdaxkAegVYgqU134P74xptaxMr_hz_FcZGIl2Ihff5lpFwPH_dPzSiLvgTlfD_BE1utSeybquKu1nGU");'></div>
                                <div class="flex flex-1 items-stretch rounded-xl h-12">
                                    <input
                                        id="message-input"
                                        placeholder="输入您的技术方向，例如：CV算法、大模型、RAG等..."
                                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0e101b] focus:outline-0 focus:ring-0 border-none bg-[#e7e9f3] focus:border-none h-full placeholder:text-[#4d5a99] px-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                                        onkeypress="handleKeyPress(event)"
                                    />
                                    <div class="flex border-none bg-[#e7e9f3] items-center justify-center pr-4 rounded-r-xl border-l-0">
                                        <button
                                            id="send-button"
                                            onclick="sendMessage()"
                                            class="min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#1435db] text-[#f8f9fc] text-sm font-medium leading-normal hover:bg-[#0f2bb8] transition-colors"
                                        >
                                            <span class="truncate">发送</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            </div>
        </div>
    </div>

    <script src="test-interface.js"></script>
</body>
</html>
