<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Katrina AI 猎头测试网站</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .message.system .message-content {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            font-size: 12px;
            color: #856404;
        }
        
        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-form {
            display: flex;
            gap: 10px;
        }
        
        .email-input, .message-input {
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .email-input {
            width: 200px;
        }
        
        .message-input {
            flex: 1;
        }
        
        .send-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .send-button:hover {
            background: #5a6fd8;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }
        
        .test-scenarios {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .test-scenarios h3 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .scenario-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .scenario-btn {
            background: #e9ecef;
            border: none;
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .scenario-btn:hover {
            background: #dee2e6;
        }
        
        .info-display {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .recommendations {
            background: #f3e5f5;
            border: 1px solid #e1bee7;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Katrina AI 猎头测试</h1>
            <p>测试多轮对话记忆、技术方向识别、职位推荐功能</p>
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message system">
                    <div class="message-content">
                        欢迎使用Katrina AI测试网站！请输入邮箱和消息开始对话。
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                🤖 Katrina正在思考中...
            </div>
            
            <div class="test-scenarios">
                <h3>🧪 快速测试场景：</h3>
                <div class="scenario-buttons">
                    <button class="scenario-btn" onclick="testScenario('nlp')">NLP推荐测试</button>
                    <button class="scenario-btn" onclick="testScenario('aigc')">AIGC测试</button>
                    <button class="scenario-btn" onclick="testScenario('complex')">复杂对话测试</button>
                    <button class="scenario-btn" onclick="clearChat()">清空对话</button>
                </div>
            </div>
            
            <div class="input-container">
                <div class="input-form">
                    <input type="email" class="email-input" id="emailInput" placeholder="邮箱地址" value="<EMAIL>">
                    <input type="text" class="message-input" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                    <button class="send-button" id="sendButton" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let conversationHistory = [];
        
        function addMessage(type, content, data = null) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let messageContent = `<div class="message-content">${content}</div>`;
            
            if (data && data.candidateInfo) {
                const info = data.candidateInfo;
                messageContent += `
                    <div class="info-display">
                        <strong>📋 累积信息：</strong><br>
                        技术方向: ${info.techDirection || 'null'}<br>
                        公司: ${info.company || 'null'}<br>
                        职级: ${info.level || 'null'}<br>
                        期望薪资: ${info.expectedSalary || 'null'}
                    </div>
                `;
            }
            
            if (data && data.recommendations && data.recommendations.length > 0) {
                messageContent += `
                    <div class="recommendations">
                        <strong>🎯 推荐职位 (${data.recommendations.length}个)：</strong><br>
                        ${data.recommendations.map((job, index) => 
                            `${index + 1}. ${job.companies.company_name} - ${job.job_title} (job_id=${job.id})`
                        ).join('<br>')}
                    </div>
                `;
            }
            
            messageDiv.innerHTML = messageContent;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        async function sendMessage() {
            const emailInput = document.getElementById('emailInput');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const loading = document.getElementById('loading');
            
            const email = emailInput.value.trim();
            const message = messageInput.value.trim();
            
            if (!email || !message) {
                alert('请输入邮箱和消息');
                return;
            }
            
            // 添加用户消息
            addMessage('user', message);
            conversationHistory.push({type: 'user', content: message});
            
            // 清空输入框并禁用按钮
            messageInput.value = '';
            sendButton.disabled = true;
            loading.style.display = 'block';
            
            try {
                const response = await fetch('http://localhost:3002/api/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const reply = data.data.reply || '(无回复)';
                    addMessage('assistant', reply, data.data);
                    conversationHistory.push({type: 'assistant', content: reply, data: data.data});
                } else {
                    addMessage('system', `❌ 错误: ${data.error || '未知错误'}`);
                }
                
            } catch (error) {
                addMessage('system', `❌ 网络错误: ${error.message}`);
            } finally {
                sendButton.disabled = false;
                loading.style.display = 'none';
                messageInput.focus();
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function clearChat() {
            document.getElementById('messages').innerHTML = `
                <div class="message system">
                    <div class="message-content">
                        对话已清空，请开始新的测试。
                    </div>
                </div>
            `;
            conversationHistory = [];
        }
        
        async function testScenario(type) {
            const emailInput = document.getElementById('emailInput');
            const messageInput = document.getElementById('messageInput');
            
            clearChat();
            
            let scenarios = [];
            const baseEmail = `${type}_test_${Date.now()}@example.com`;
            emailInput.value = baseEmail;
            
            if (type === 'nlp') {
                scenarios = ['NLP', '字节跳动 2-2', '期望薪资250W'];
                addMessage('system', '🧪 开始NLP推荐测试场景');
            } else if (type === 'aigc') {
                scenarios = ['AIGC', '腾讯 11级', '期望薪资300W'];
                addMessage('system', '🧪 开始AIGC测试场景（预期：无职位匹配）');
            } else if (type === 'complex') {
                scenarios = ['你好', '我想找工作', '推荐算法', '字节跳动 2-2', '期望薪资180W'];
                addMessage('system', '🧪 开始复杂5轮对话测试场景');
            }
            
            for (let i = 0; i < scenarios.length; i++) {
                messageInput.value = scenarios[i];
                await new Promise(resolve => setTimeout(resolve, 1000));
                await sendMessage();
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
        
        // 页面加载完成后聚焦到消息输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
