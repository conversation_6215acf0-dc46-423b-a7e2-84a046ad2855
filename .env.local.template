# Katrina AI 猎头聊天机器人 - 环境变量配置文件
# 请将此文件重命名为 .env.local 并填入实际的配置值

# =====================================================
# Supabase 数据库配置
# =====================================================

# Supabase 项目 URL
# 从 Supabase 项目设置 > API > Project URL 获取
# 格式: https://your-project-id.supabase.co
SUPABASE_URL=your-supabase-project-url-here

# Supabase 匿名公钥 (用于客户端)
# 从 Supabase 项目设置 > API > Project API keys > anon public 获取
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Supabase 服务角色密钥 (用于服务端高权限操作)
# 从 Supabase 项目设置 > API > Project API keys > service_role 获取
# ⚠️ 此密钥具有完全数据库访问权限，请妥善保管，不要泄露
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =====================================================
# DeepSeek API 配置 (大模型服务)
# =====================================================

# DeepSeek API 密钥
# 从 DeepSeek 官网获取 API Key
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# DeepSeek API 端点
# 通常为官方端点，如有自定义端点可修改
LLM_API_ENDPOINT=https://api.deepseek.com/v1

# =====================================================
# RAG 服务配置 (后续阶段使用)
# =====================================================

# RAG 服务器地址
# 用于知识检索增强生成，当前阶段暂不使用
RAG_SERVER_URL=http://localhost:8000

# =====================================================
# 其他配置 (可选)
# =====================================================

# 应用环境
NODE_ENV=development

# 日志级别
LOG_LEVEL=info

# =====================================================
# 配置说明
# =====================================================

# 1. 将此文件重命名为 .env.local
# 2. 替换所有 "your-xxx-here" 为实际的配置值
# 3. 确保此文件位于项目根目录: ~/Desktop/gemini+augment开发网页机器人方案/.env.local
# 4. 不要将 .env.local 文件提交到版本控制系统
# 5. 如需验证配置是否正确，运行: npm run verify-env
