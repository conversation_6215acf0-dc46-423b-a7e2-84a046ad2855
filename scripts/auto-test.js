#!/usr/bin/env node

/**
 * 自动化测试验证脚本
 * 在每次文件修改后自动运行相关测试
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class AutoTestRunner {
  constructor() {
    this.projectRoot = process.cwd();
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行自动化测试验证...\n');
    
    try {
      // 1. 运行 ESLint 检查
      await this.runLintCheck();
      
      // 2. 运行 TypeScript 检查
      await this.runTypeCheck();
      
      // 3. 运行单元测试
      await this.runUnitTests();
      
      // 4. 运行集成测试
      await this.runIntegrationTests();
      
      // 5. 生成测试报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 运行 ESLint 检查
   */
  async runLintCheck() {
    console.log('📋 运行代码规范检查...');
    try {
      execSync('npm run lint', { stdio: 'inherit' });
      console.log('✅ 代码规范检查通过\n');
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ 代码规范检查失败\n');
      this.testResults.failed++;
      this.testResults.errors.push('ESLint 检查失败');
    }
  }

  /**
   * 运行 TypeScript 类型检查
   */
  async runTypeCheck() {
    console.log('🔍 运行 TypeScript 类型检查...');
    try {
      if (fs.existsSync(path.join(this.projectRoot, 'tsconfig.json'))) {
        execSync('npx tsc --noEmit', { stdio: 'inherit' });
        console.log('✅ TypeScript 类型检查通过\n');
        this.testResults.passed++;
      } else {
        console.log('⚠️  未找到 tsconfig.json，跳过 TypeScript 检查\n');
      }
    } catch (error) {
      console.log('❌ TypeScript 类型检查失败\n');
      this.testResults.failed++;
      this.testResults.errors.push('TypeScript 类型检查失败');
    }
  }

  /**
   * 运行单元测试
   */
  async runUnitTests() {
    console.log('🧪 运行单元测试...');
    try {
      execSync('npm run test:unit', { stdio: 'inherit' });
      console.log('✅ 单元测试通过\n');
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ 单元测试失败\n');
      this.testResults.failed++;
      this.testResults.errors.push('单元测试失败');
    }
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('🔗 运行集成测试...');
    try {
      execSync('npm run test:integration', { stdio: 'inherit' });
      console.log('✅ 集成测试通过\n');
      this.testResults.passed++;
    } catch (error) {
      console.log('❌ 集成测试失败\n');
      this.testResults.failed++;
      this.testResults.errors.push('集成测试失败');
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('📊 测试结果汇总:');
    console.log('==================');
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 失败详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    // 保存测试结果到记忆库
    this.saveTestResults();
    
    if (this.testResults.failed > 0) {
      console.log('\n🚨 存在测试失败，请检查并修复问题');
      process.exit(1);
    } else {
      console.log('\n🎉 所有测试通过！');
    }
  }

  /**
   * 保存测试结果到记忆库
   */
  saveTestResults() {
    const timestamp = new Date().toISOString();
    const testReport = {
      timestamp,
      passed: this.testResults.passed,
      failed: this.testResults.failed,
      errors: this.testResults.errors,
      status: this.testResults.failed === 0 ? 'PASSED' : 'FAILED'
    };

    const reportPath = path.join(this.projectRoot, 'memory-bank', 'storage', 'test-results.json');
    
    // 读取现有测试历史
    let testHistory = [];
    if (fs.existsSync(reportPath)) {
      try {
        testHistory = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      } catch (error) {
        console.warn('⚠️  无法读取测试历史，将创建新的记录');
      }
    }

    // 添加新的测试结果
    testHistory.push(testReport);
    
    // 只保留最近 50 次测试结果
    if (testHistory.length > 50) {
      testHistory = testHistory.slice(-50);
    }

    // 保存到文件
    try {
      fs.writeFileSync(reportPath, JSON.stringify(testHistory, null, 2));
      console.log(`📝 测试结果已保存到: ${reportPath}`);
    } catch (error) {
      console.warn('⚠️  无法保存测试结果:', error.message);
    }
  }

  /**
   * 运行特定文件的相关测试
   */
  async runTestsForFile(filePath) {
    console.log(`🎯 运行与 ${filePath} 相关的测试...`);
    
    // 根据文件路径确定相关测试
    const testPatterns = this.getTestPatternsForFile(filePath);
    
    for (const pattern of testPatterns) {
      try {
        execSync(`npm test -- --testPathPattern="${pattern}"`, { stdio: 'inherit' });
        console.log(`✅ ${pattern} 测试通过`);
      } catch (error) {
        console.log(`❌ ${pattern} 测试失败`);
        this.testResults.failed++;
      }
    }
  }

  /**
   * 根据文件路径获取相关测试模式
   */
  getTestPatternsForFile(filePath) {
    const patterns = [];
    
    // 基本规则：同名测试文件
    const baseName = path.basename(filePath, path.extname(filePath));
    patterns.push(`${baseName}.test`);
    patterns.push(`${baseName}.spec`);
    
    // 目录级测试
    const dirName = path.dirname(filePath);
    if (dirName !== '.') {
      patterns.push(dirName);
    }
    
    // API 路由测试
    if (filePath.includes('/api/')) {
      patterns.push('api');
    }
    
    // 组件测试
    if (filePath.includes('/components/')) {
      patterns.push('components');
    }
    
    return patterns;
  }
}

// 命令行接口
if (require.main === module) {
  const runner = new AutoTestRunner();
  
  const args = process.argv.slice(2);
  if (args.length > 0 && args[0] === '--file') {
    // 运行特定文件的测试
    const filePath = args[1];
    if (filePath) {
      runner.runTestsForFile(filePath);
    } else {
      console.error('请提供文件路径');
      process.exit(1);
    }
  } else {
    // 运行所有测试
    runner.runAllTests();
  }
}

module.exports = AutoTestRunner;
