# 项目简介 - Gemini+Augment开发网页机器人方案

## 项目概述
这是一个基于Next.js的AI助手项目，旨在开发一个智能的网页机器人系统。

## 核心目标
- 解决AI助手跨文件记忆问题
- 实现智能的项目上下文管理
- 提供可靠的AI监督和约束机制

## 技术栈
- **前端**: Next.js, React
- **后端**: Node.js, Express
- **AI集成**: 多种AI模型支持
- **测试**: Jest
- **开发工具**: ESLint, TypeScript

## 项目结构
```
├── pages/           # Next.js页面
├── lib/            # 核心库文件
├── test-files/     # 测试文件
├── guardrails-test/ # AI监督测试
├── 业务逻辑新开发/   # 业务开发模块
└── styles/         # 样式文件
```

## 当前挑战
1. **跨文件记忆问题**: AI助手在处理多文件项目时容易忘记上下文
2. **重复修改**: 经常修改已经正确的代码
3. **遗漏错误**: 忽略需要修复的问题
4. **缺乏监督**: 需要有效的AI行为约束机制

## 期望解决方案
- 实现持久化的项目上下文记忆
- 建立有效的AI行为监督机制
- 提供跨会话的项目状态追踪
- 确保代码修改的准确性和完整性

## 开发优先级
1. 设置项目记忆系统
2. 实现AI行为约束
3. 完善跨文件工作流程
4. 优化项目开发效率
