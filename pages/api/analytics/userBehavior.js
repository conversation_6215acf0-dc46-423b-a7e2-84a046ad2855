/**
 * 用户行为分析 API
 * 提供用户行为数据分析和报告生成功能
 * 
 * 更新日期：2025-07-13
 * 适配新的 katrina_recruitment_db v2.0 结构
 */

import { apiAdapter } from '../../../lib/services/apiAdapter.js';

export default async function handler(req, res) {
    // 只允许 GET 请求
    if (req.method !== 'GET') {
        return res.status(405).json({ 
            error: 'Method not allowed',
            message: '只支持 GET 请求' 
        });
    }
    
    try {
        // 获取查询参数
        const { 
            email, 
            type = 'summary', 
            dateFrom, 
            dateTo,
            includeDetails = 'false'
        } = req.query;
        
        if (!email) {
            return res.status(400).json({ 
                error: 'Missing email parameter',
                message: '缺少 email 参数' 
            });
        }
        
        // 生成追踪ID
        const traceId = `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        
        let result;
        
        switch (type) {
            case 'summary':
                // 获取用户行为摘要
                result = await apiAdapter.behaviorAnalysis.getUserInteractionHistory(email, {
                    limit: 10,
                    includeDetails: includeDetails === 'true'
                });
                break;
                
            case 'report':
                // 生成详细的行为分析报告
                result = await apiAdapter.behaviorAnalysis.generateBehaviorReport(email, {
                    dateFrom,
                    dateTo
                });
                break;
                
            case 'recommendations':
                // 获取推荐历史和效果分析
                const recommendationStats = await apiAdapter.jobRecommendation.getRecommendationStats({
                    email,
                    dateFrom,
                    dateTo
                });
                result = {
                    success: true,
                    data: recommendationStats
                };
                break;
                
            case 'resumes':
                // 获取简历上传历史
                result = await apiAdapter.resume.getResumeHistory(email, {
                    limit: 20,
                    includeContent: includeDetails === 'true'
                });
                break;
                
            default:
                return res.status(400).json({ 
                    error: 'Invalid type parameter',
                    message: '无效的 type 参数，支持: summary, report, recommendations, resumes'
                });
        }
        
        if (!result.success) {
            return res.status(500).json({ 
                error: 'Analysis failed',
                message: '行为分析失败',
                details: result.error
            });
        }
        
        // 记录分析请求日志
        console.log(JSON.stringify({
            timestamp: new Date().toISOString(),
            level: "INFO",
            traceId,
            component: "api/analytics/userBehavior",
            event: "analysis_completed",
            message: "用户行为分析完成",
            data: {
                email,
                analysisType: type,
                dateRange: { dateFrom, dateTo },
                includeDetails: includeDetails === 'true'
            }
        }));
        
        return res.status(200).json({
            success: true,
            data: result.data || result,
            metadata: {
                analysisType: type,
                email,
                dateRange: { dateFrom, dateTo },
                includeDetails: includeDetails === 'true',
                traceId,
                analysisTime: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('User behavior analysis API Error:', error);
        
        return res.status(500).json({ 
            error: 'Internal server error',
            message: '用户行为分析失败，请稍后重试',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}
