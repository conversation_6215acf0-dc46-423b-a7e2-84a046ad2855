// API健康检查端点
export default function handler(req, res) {
    if (req.method === 'GET') {
        res.status(200).json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'Katrina AI Chatbot',
            version: '1.0.0'
        });
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).json({ error: 'Method not allowed' });
    }
}
