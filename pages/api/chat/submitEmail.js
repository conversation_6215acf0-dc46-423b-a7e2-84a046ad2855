/**
 * 私人邮箱提交 API
 * 处理用户提交额外私人邮箱的请求，永久保存到候选人档案
 */

import { storageService } from '../../../lib/storageService';

// 邮箱验证正则表达式
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * 验证邮箱格式
 */
function validateEmail(email) {
    return EMAIL_REGEX.test(email);
}

/**
 * 检查邮箱是否为常见的企业邮箱
 */
function isCompanyEmail(email) {
    const domain = email.split('@')[1]?.toLowerCase();
    const commonCompanyDomains = [
        'tencent.com', 'alibaba-inc.com', 'baidu.com', 'bytedance.com',
        'meituan.com', 'didi.com', 'xiaomi.com', 'huawei.com',
        'jd.com', 'pinduoduo.com', 'netease.com', 'sina.com'
    ];

    return commonCompanyDomains.includes(domain);
}

/**
 * 从 notes 字段中提取私人邮箱
 */
function extractPersonalEmailFromNotes(notes) {
    if (!notes) return null;

    const match = notes.match(/\[PERSONAL_EMAIL\]\s*({.*?})/);
    if (match) {
        try {
            const data = JSON.parse(match[1]);
            return data.personalEmail;
        } catch (error) {
            console.error('Failed to parse personal email from notes:', error);
        }
    }
    return null;
}

export default async function handler(req, res) {
    // 只允许 POST 请求
    if (req.method !== 'POST') {
        return res.status(405).json({ 
            error: 'Method not allowed',
            message: '只支持 POST 请求' 
        });
    }
    
    try {
        // 获取 email 参数（会话主标识）
        const sessionEmail = req.query.email || req.body.sessionEmail;
        const personalEmail = req.body.personalEmail || req.body.email;
        
        if (!sessionEmail) {
            return res.status(400).json({ 
                error: 'Missing session email parameter',
                message: '缺少会话 email 参数' 
            });
        }
        
        if (!personalEmail) {
            return res.status(400).json({ 
                error: 'Missing personal email',
                message: '缺少私人邮箱' 
            });
        }
        
        // 验证私人邮箱格式
        if (!validateEmail(personalEmail)) {
            return res.status(400).json({ 
                error: 'Invalid email format',
                message: '私人邮箱格式不正确' 
            });
        }
        
        // 检查是否与会话邮箱相同
        if (personalEmail.toLowerCase() === sessionEmail.toLowerCase()) {
            return res.status(400).json({ 
                error: 'Same as session email',
                message: '私人邮箱不能与当前会话邮箱相同' 
            });
        }
        
        // 获取当前候选人档案，如果不存在则创建
        let currentProfile = await storageService.getCandidateProfile(sessionEmail);

        if (!currentProfile) {
            // 创建基础档案
            currentProfile = await storageService.upsertCandidateProfile(sessionEmail, {
                email: sessionEmail,
                created_at: new Date().toISOString()
            });
        }
        
        // 检查是否已经提交过私人邮箱（使用 notes 字段存储）
        const personalEmailFromNotes = currentProfile?.notes ?
            extractPersonalEmailFromNotes(currentProfile.notes) : null;

        if (personalEmailFromNotes) {
            return res.status(400).json({
                error: 'Personal email already exists',
                message: '已经提交过私人邮箱',
                existingEmail: personalEmailFromNotes
            });
        }
        
        // 分析邮箱类型
        const emailAnalysis = {
            isCompanyEmail: isCompanyEmail(personalEmail),
            domain: personalEmail.split('@')[1]?.toLowerCase(),
            isGmail: personalEmail.includes('@gmail.com'),
            isQQ: personalEmail.includes('@qq.com') || personalEmail.includes('@163.com'),
            isOutlook: personalEmail.includes('@outlook.com') || personalEmail.includes('@hotmail.com')
        };
        
        // 更新候选人档案（使用现有字段存储）
        const personalEmailData = {
            personalEmail: personalEmail,
            submittedAt: new Date().toISOString(),
            analysis: emailAnalysis
        };

        const currentNotes = currentProfile?.notes || '';
        const newNotes = currentNotes +
            (currentNotes ? '\n' : '') +
            `[PERSONAL_EMAIL] ${JSON.stringify(personalEmailData)}`;

        const updateData = {
            notes: newNotes,
            last_contact_date: new Date().toISOString()
        };
        
        await storageService.updateCandidateProfile(sessionEmail, updateData);
        
        // 记录提交事件到聊天日志
        await storageService.saveChatMessage(sessionEmail, {
            sender: 'system',
            message: `私人邮箱提交成功：${personalEmail}`,
            session_state: 'personal_email_submitted',
            metadata: {
                action: 'personal_email_submit',
                personalEmail: personalEmail,
                emailAnalysis: emailAnalysis,
                submittedAt: new Date().toISOString()
            }
        });
        
        return res.status(200).json({
            success: true,
            message: '私人邮箱提交成功',
            data: {
                sessionEmail: sessionEmail,
                personalEmail: personalEmail,
                submittedAt: new Date().toISOString(),
                emailAnalysis: {
                    type: emailAnalysis.isCompanyEmail ? 'company' : 'personal',
                    domain: emailAnalysis.domain,
                    provider: emailAnalysis.isGmail ? 'Gmail' : 
                             emailAnalysis.isQQ ? 'QQ/163' : 
                             emailAnalysis.isOutlook ? 'Outlook' : 'Other'
                }
            },
            metadata: {
                sessionState: 'personal_email_submitted',
                permanentStorage: true, // 标记为永久存储
                storageMethod: 'notes_field', // 使用 notes 字段存储
                note: '私人邮箱已永久保存到候选人档案，不会影响当前会话标识'
            }
        });
        
    } catch (error) {
        console.error('Submit Email API Error:', error);
        
        return res.status(500).json({ 
            error: 'Internal server error',
            message: '私人邮箱提交失败，请稍后重试',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}
