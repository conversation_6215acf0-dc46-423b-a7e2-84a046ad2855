/**
 * 简历上传 API - 适配新数据库结构
 * 处理简历文件上传，存储到 Supabase Storage，并集成追踪服务
 *
 * 更新日期：2025-07-13
 * 适配新的 katrina_recruitment_db v2.0 结构
 */

import { storageService } from '../../../lib/storageService';
import { unifiedTrackingService } from '../../../lib/services/unifiedTrackingService.js';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// 配置 Next.js 不解析请求体，让 formidable 处理
export const config = {
    api: {
        bodyParser: false,
    },
};

// 支持的文件类型和大小限制
const ALLOWED_FILE_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * 解析上传的文件
 */
function parseForm(req) {
    return new Promise((resolve, reject) => {
        const form = formidable({
            maxFileSize: MAX_FILE_SIZE,
            keepExtensions: true,
            filter: ({ mimetype }) => {
                return ALLOWED_FILE_TYPES.includes(mimetype);
            }
        });
        
        form.parse(req, (err, fields, files) => {
            if (err) {
                reject(err);
                return;
            }
            resolve({ fields, files });
        });
    });
}

/**
 * 生成安全的文件名
 */
function generateSafeFileName(originalName, email) {
    const timestamp = Date.now();
    const emailPrefix = email.split('@')[0].replace(/[^a-zA-Z0-9]/g, '');
    const ext = path.extname(originalName);
    return `resume_${emailPrefix}_${timestamp}${ext}`;
}

export default async function handler(req, res) {
    // 只允许 POST 请求
    if (req.method !== 'POST') {
        return res.status(405).json({ 
            error: 'Method not allowed',
            message: '只支持 POST 请求' 
        });
    }
    
    try {
        // 获取 email 参数
        const email = req.query.email;
        
        if (!email) {
            return res.status(400).json({ 
                error: 'Missing email parameter',
                message: '缺少 email 参数' 
            });
        }
        
        // 解析上传的文件
        const { fields, files } = await parseForm(req);
        
        if (!files.resume) {
            return res.status(400).json({ 
                error: 'No file uploaded',
                message: '未找到上传的简历文件' 
            });
        }
        
        const resumeFile = Array.isArray(files.resume) ? files.resume[0] : files.resume;
        
        // 验证文件类型
        if (!ALLOWED_FILE_TYPES.includes(resumeFile.mimetype)) {
            return res.status(400).json({ 
                error: 'Invalid file type',
                message: '不支持的文件类型，请上传 PDF、Word 或文本文件',
                allowedTypes: ['PDF', 'Word (.doc/.docx)', 'Text (.txt)']
            });
        }
        
        // 验证文件大小
        if (resumeFile.size > MAX_FILE_SIZE) {
            return res.status(400).json({ 
                error: 'File too large',
                message: `文件大小不能超过 ${MAX_FILE_SIZE / 1024 / 1024}MB`,
                maxSize: MAX_FILE_SIZE,
                currentSize: resumeFile.size
            });
        }
        
        // 读取文件内容
        const fileBuffer = fs.readFileSync(resumeFile.filepath);
        const fileName = generateSafeFileName(resumeFile.originalFilename, email);
        
        // 生成追踪ID
        const traceId = `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        // 上传到 Supabase Storage
        const uploadResult = await storageService.uploadResume(email, fileBuffer, fileName, {
            contentType: resumeFile.mimetype,
            originalName: resumeFile.originalFilename,
            size: resumeFile.size
        });

        // 清理临时文件
        try {
            fs.unlinkSync(resumeFile.filepath);
        } catch (cleanupError) {
            console.warn('Failed to cleanup temp file:', cleanupError);
        }

        // 使用新的API适配器记录简历上传交互
        const fileInfo = {
            fileName: fileName,
            originalFileName: resumeFile.originalFilename,
            fileUrl: uploadResult.publicUrl,
            fileSize: resumeFile.size,
            mimeType: resumeFile.mimetype
        };

        const parseResult = {
            success: true,
            data: {
                extractedInfo: null, // 可以在这里集成简历解析服务
                confidence: null,
                processingTime: null,
                parsingMethod: 'pending'
            }
        };

        const trackingResult = await unifiedTrackingService.recordResumeInteraction(
            email,
            fileInfo,
            parseResult,
            {
                traceId,
                uploadSource: 'api',
                userAgent: req.headers['user-agent'],
                ipAddress: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                uploadMethod: 'form_upload',
                sourceUrl: req.headers.referer || 'direct'
            }
        );

        // 更新候选人档案（保持向后兼容）
        await storageService.updateCandidateProfile(email, {
            resume_link: uploadResult.publicUrl,
            resume_filename: fileName,
            resume_uploaded_at: new Date().toISOString(),
            session_state: 'resume_uploaded'
        });
        
        return res.status(200).json({
            success: true,
            message: '简历上传成功',
            data: {
                fileName: fileName,
                originalName: resumeFile.originalFilename,
                fileSize: resumeFile.size,
                fileType: resumeFile.mimetype,
                uploadTime: new Date().toISOString(),
                publicUrl: uploadResult.publicUrl,
                resumeId: trackingResult.success ? trackingResult.data?.resumeId : null,
                sessionId: trackingResult.success ? trackingResult.data?.sessionId : null,
                linkedRecommendation: trackingResult.success ? trackingResult.data?.linkedRecommendation : null
            },
            metadata: {
                email,
                sessionState: 'resume_uploaded',
                permanentStorage: true,
                traceId,
                trackingSuccess: trackingResult.success
            }
        });
        
    } catch (error) {
        console.error('Resume Upload API Error:', error);
        
        // 根据错误类型返回不同的响应
        if (error.message.includes('File too large')) {
            return res.status(400).json({ 
                error: 'File too large',
                message: '文件大小超出限制'
            });
        }
        
        if (error.message.includes('Invalid file type')) {
            return res.status(400).json({ 
                error: 'Invalid file type',
                message: '不支持的文件类型'
            });
        }
        
        return res.status(500).json({ 
            error: 'Internal server error',
            message: '简历上传失败，请稍后重试',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}
