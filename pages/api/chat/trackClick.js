/**
 * 推荐点击追踪 API
 * 记录用户对职位推荐的点击行为，用于分析推荐效果
 * 
 * 更新日期：2025-07-13
 * 适配新的 katrina_recruitment_db v2.0 结构
 */

import { databaseAdapter } from '../../../lib/services/databaseAdapter.js';
import { recommendationTrackingService } from '../../../lib/services/recommendationTrackingService.js';

export default async function handler(req, res) {
    // 只允许 POST 请求
    if (req.method !== 'POST') {
        return res.status(405).json({ 
            error: 'Method not allowed',
            message: '只支持 POST 请求' 
        });
    }
    
    try {
        // 获取请求参数
        const { recommendationId, jobId, email } = req.body;
        
        if (!recommendationId && !jobId) {
            return res.status(400).json({ 
                error: 'Missing parameters',
                message: '缺少 recommendationId 或 jobId 参数' 
            });
        }
        
        // 生成追踪ID
        const traceId = `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        
        // 记录点击行为
        let clickResult;
        if (recommendationId) {
            // 如果有推荐ID，直接记录点击
            clickResult = await recommendationTrackingService.recordClick(
                email,
                recommendationId,
                {
                    traceId,
                    clickSource: 'chat_interface',
                    userAgent: req.headers['user-agent'],
                    ipAddress: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                    referer: req.headers.referer || 'direct'
                }
            );
        } else if (jobId) {
            // 如果只有jobId，直接记录点击
            clickResult = await recommendationTrackingService.recordClick(
                email,
                jobId,
                {
                    traceId,
                    clickSource: 'chat_interface',
                    userAgent: req.headers['user-agent'],
                    ipAddress: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                    referer: req.headers.referer || 'direct'
                }
            );
        } else {
            return res.status(400).json({
                error: 'Missing parameters',
                message: '缺少 recommendationId 或 jobId 参数'
            });
        }
        
        if (!clickResult.success) {
            return res.status(500).json({ 
                error: 'Tracking failed',
                message: '点击追踪失败',
                details: clickResult.error
            });
        }
        
        // 记录成功日志
        console.log(JSON.stringify({
            timestamp: new Date().toISOString(),
            level: "INFO",
            traceId,
            component: "api/chat/trackClick",
            event: "click_tracked",
            message: "推荐点击追踪成功",
            data: {
                recommendationId,
                jobId,
                email,
                jobTitle: clickResult.data?.job_title,
                companyName: clickResult.data?.company_name
            }
        }));
        
        return res.status(200).json({
            success: true,
            message: '点击追踪成功',
            data: {
                recommendationId: clickResult.data?.id,
                jobTitle: clickResult.data?.job_title,
                companyName: clickResult.data?.company_name,
                clickTime: clickResult.data?.click_timestamp,
                sessionId: clickResult.data?.session_id
            },
            metadata: {
                traceId,
                trackingTime: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Click tracking API Error:', error);
        
        return res.status(500).json({ 
            error: 'Internal server error',
            message: '点击追踪失败，请稍后重试',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}
