/**
 * 聊天历史获取 API - 简化版本
 * 用于前端测试，返回基本的聊天历史结构
 *
 * 更新日期：2025-07-15
 */

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    const { email, includeProfile } = req.query;

    if (!email) {
        return res.status(400).json({
            success: false,
            error: 'Email parameter is required'
        });
    }

    try {
        // 简化版本：返回空的聊天历史和候选人信息
        // 实际的聊天功能通过 /api/chat/message 端点处理
        return res.status(200).json({
            success: true,
            data: {
                chatHistory: [],
                candidateProfile: includeProfile === 'true' ? {
                    completeness: 0,
                    currentCompany: null,
                    currentLevel: null,
                    techDirection: null,
                    expectedSalary: null,
                    location: null
                } : null
            }
        });

    } catch (error) {
        console.error('获取聊天历史失败:', error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
}
