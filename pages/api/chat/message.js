/**
 * Katrina AI 聊天消息 API - 重建版本
 * 使用新的核心引擎，简化架构
 * 2025-07-15
 */

import { ChatEngine } from '../../../lib/chatEngine.js';

// 创建核心引擎实例
const chatEngine = new ChatEngine();

// 速率限制配置
const RATE_LIMITS = {
    MAX_REQUESTS_PER_MINUTE: 20,
    MAX_REQUESTS_PER_HOUR: 100,
    MAX_MESSAGE_LENGTH: 1000
};

// 内存中的速率限制存储 (生产环境应使用Redis)
const rateLimitStore = new Map();

/**
 * 检查速率限制
 */
function checkRateLimit(email) {
    const now = Date.now();
    const userKey = `rate_limit_${email}`;
    
    if (!rateLimitStore.has(userKey)) {
        rateLimitStore.set(userKey, {
            requests: [],
            lastCleanup: now
        });
    }
    
    const userData = rateLimitStore.get(userKey);
    
    // 清理过期记录
    if (now - userData.lastCleanup > 60000) { // 1分钟
        userData.requests = userData.requests.filter(time => now - time < 3600000); // 1小时
        userData.lastCleanup = now;
    }
    
    // 检查限制
    const recentRequests = userData.requests.filter(time => now - time < 60000); // 1分钟内
    const hourlyRequests = userData.requests.filter(time => now - time < 3600000); // 1小时内
    
    if (recentRequests.length >= RATE_LIMITS.MAX_REQUESTS_PER_MINUTE) {
        return { allowed: false, reason: '请求过于频繁，请稍后再试' };
    }
    
    if (hourlyRequests.length >= RATE_LIMITS.MAX_REQUESTS_PER_HOUR) {
        return { allowed: false, reason: '已达到小时请求限制' };
    }
    
    // 记录请求
    userData.requests.push(now);
    
    return { allowed: true };
}

/**
 * 验证输入参数
 */
function validateInput(message, email) {
    if (!message || typeof message !== 'string') {
        return { valid: false, error: '消息内容不能为空' };
    }
    
    if (!email || typeof email !== 'string' || !email.includes('@')) {
        return { valid: false, error: '邮箱格式不正确' };
    }
    
    if (message.length > RATE_LIMITS.MAX_MESSAGE_LENGTH) {
        return { valid: false, error: '消息内容过长' };
    }
    
    return { valid: true };
}

/**
 * 主处理函数
 */
export default async function handler(req, res) {
    // 只允许 POST 请求
    if (req.method !== 'POST') {
        return res.status(405).json({
            success: false,
            error: '只支持 POST 请求'
        });
    }
    
    const startTime = Date.now();
    const traceId = `trace_${startTime}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
        console.log(`🚀 [${traceId}] API请求开始`);
        
        // 1. 解析请求参数
        const { message, sessionId } = req.body;
        const email = req.query.email || req.body.email; // 支持从URL参数或请求体获取邮箱
        
        // 2. 验证输入
        const validation = validateInput(message, email);
        if (!validation.valid) {
            console.log(`❌ [${traceId}] 输入验证失败: ${validation.error}`);
            return res.status(400).json({
                success: false,
                error: validation.error
            });
        }
        
        // 3. 检查速率限制
        const rateLimit = checkRateLimit(email);
        if (!rateLimit.allowed) {
            console.log(`⚠️ [${traceId}] 速率限制: ${rateLimit.reason}`);
            return res.status(429).json({
                success: false,
                error: rateLimit.reason
            });
        }
        
        // 4. 处理消息
        console.log(`💬 [${traceId}] 处理消息: ${message.substring(0, 50)}...`);
        
        const result = await chatEngine.processMessage(message, email, sessionId);
        
        const responseTime = Date.now() - startTime;
        
        // 5. 返回结果
        const response = {
            success: true,
            data: {
                reply: result.reply,
                candidateInfo: result.candidateInfo,
                recommendations: result.recommendations,
                sessionId: result.sessionId,
                sessionState: 'completed'
            },
            metadata: {
                tokensUsed: result.tokensUsed,
                responseTime,
                traceId,
                timestamp: new Date().toISOString()
            }
        };
        
        console.log(`✅ [${traceId}] 处理完成，耗时 ${responseTime}ms，使用 ${result.tokensUsed} tokens`);
        
        res.status(200).json(response);
        
    } catch (error) {
        const responseTime = Date.now() - startTime;
        
        console.error(`❌ [${traceId}] 处理失败:`, error);
        
        // 返回错误响应
        res.status(500).json({
            success: false,
            error: '服务暂时不可用，请稍后再试',
            metadata: {
                responseTime,
                traceId,
                timestamp: new Date().toISOString()
            }
        });
    }
}

// 配置 API 路由 - 高并发优化
export const config = {
    api: {
        bodyParser: {
            sizeLimit: '1mb',
        },
        // 高并发优化配置
        externalResolver: true,
        responseLimit: false,
    },
    // Vercel 函数配置
    maxDuration: 30, // 最大执行时间30秒
    memory: 1024,    // 分配1GB内存
    regions: ['hkg1', 'sin1', 'nrt1'], // 亚洲区域优化
};
