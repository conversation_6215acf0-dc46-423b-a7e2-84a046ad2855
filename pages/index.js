/**
 * Katrina AI 猎头机器人 - 简易测试前端
 * 主聊天页面，支持消息发送、文件上传、邮箱提交等功能
 */

import { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function ChatPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [personalEmail, setPersonalEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState('');
  const [emailStatus, setEmailStatus] = useState('');
  const [candidateInfo, setCandidateInfo] = useState(null);
  const [error, setError] = useState('');
  const [sessionId, setSessionId] = useState('');

  const chatContainerRef = useRef(null);
  const fileInputRef = useRef(null);

  // 从 URL 参数获取 email
  useEffect(() => {
    if (router.isReady) {
      const emailParam = router.query.email;
      if (emailParam) {
        setEmail(emailParam);
        loadChatHistory(emailParam);
      } else {
        // 如果没有 email 参数，提示用户
        setError('请在 URL 中添加 email 参数，例如：?email=<EMAIL>');
      }
    }
  }, [router.isReady, router.query.email]);

  // 自动滚动到聊天底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory]);

  /**
   * 加载聊天历史
   */
  const loadChatHistory = async (emailParam) => {
    try {
      const response = await fetch(`/api/chat/history?email=${encodeURIComponent(emailParam)}&includeProfile=true`);
      const data = await response.json();
      
      if (data.success) {
        setChatHistory(data.data.chatHistory || []);
        setCandidateInfo(data.data.candidateProfile);
      } else {
        console.error('Failed to load chat history:', data.error);
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  /**
   * 发送消息
   */
  const sendMessage = async () => {
    if (!message.trim() || !email || isLoading) return;

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/chat/message?email=${encodeURIComponent(email)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message.trim(),
          sessionId: sessionId
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 保存 sessionId
        if (data.data.sessionId && !sessionId) {
          setSessionId(data.data.sessionId);
        }

        // 添加用户消息到聊天历史
        const userMessage = {
          id: Date.now(),
          sender: 'user',
          message: message.trim(),
          timestamp: new Date().toISOString()
        };

        // 更新聊天历史
        let newChatHistory = [...chatHistory, userMessage];

        // 只有当 Katrina 有回复时才添加回复消息
        if (data.data.reply && data.data.reply.trim()) {
          const katrinaMessage = {
            id: Date.now() + 1,
            sender: 'katrina',
            message: data.data.reply,
            timestamp: new Date().toISOString()
          };
          newChatHistory.push(katrinaMessage);
        } else {
          // 静默收集信息，在控制台记录但不显示回复
          console.log('Katrina 静默收集信息，不回复');
        }

        setChatHistory(newChatHistory);
        setCandidateInfo(data.data.candidateInfo);
        setMessage('');
      } else {
        setError(data.message || data.error || '发送消息失败');
      }
    } catch (error) {
      setError('网络错误，请检查连接');
      console.error('Send message error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 处理文件上传
   */
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !email) return;

    setUploadStatus('上传中...');
    setError('');

    const formData = new FormData();
    formData.append('resume', file);

    try {
      const response = await fetch(`/api/chat/uploadResume?email=${encodeURIComponent(email)}`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setUploadStatus(`✅ ${data.message}`);
        // 重新加载聊天历史以显示上传记录
        setTimeout(() => loadChatHistory(email), 1000);
      } else {
        setUploadStatus(`❌ ${data.message || data.error || '上传失败'}`);
      }
    } catch (error) {
      setUploadStatus('❌ 网络错误，上传失败');
      console.error('Upload error:', error);
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  /**
   * 提交私人邮箱
   */
  const submitPersonalEmail = async () => {
    if (!personalEmail.trim() || !email) return;

    setEmailStatus('提交中...');
    setError('');

    try {
      const response = await fetch(`/api/chat/submitEmail?email=${encodeURIComponent(email)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ personalEmail: personalEmail.trim() }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setEmailStatus(`✅ ${data.message}`);
        setPersonalEmail('');
        // 重新加载聊天历史以显示提交记录
        setTimeout(() => loadChatHistory(email), 1000);
      } else {
        setEmailStatus(`❌ ${data.message || data.error || '提交失败'}`);
      }
    } catch (error) {
      setEmailStatus('❌ 网络错误，提交失败');
      console.error('Submit email error:', error);
    }
  };

  /**
   * 处理回车键发送消息
   */
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  /**
   * 格式化时间
   */
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      <Head>
        <title>Katrina AI 猎头机器人 - 测试页面</title>
        <meta name="description" content="Katrina AI 猎头机器人后端 API 测试界面" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div style={styles.container}>
        {/* 头部信息 */}
        <div style={styles.header}>
          <h1 style={styles.title}>🤖 Katrina AI 猎头机器人</h1>
          <div style={styles.emailInfo}>
            <strong>当前会话邮箱：</strong>
            <span style={styles.emailText}>{email || '未设置'}</span>
          </div>
          {candidateInfo && (
            <div style={styles.candidateInfo}>
              <strong>候选人信息完整度：</strong>
              <span style={styles.completeness}>{candidateInfo.completeness || 0}%</span>
              {candidateInfo.currentLevel && (
                <span style={styles.infoItem}>职级：{candidateInfo.currentLevel}</span>
              )}
              {candidateInfo.currentCompany && (
                <span style={styles.infoItem}>公司：{candidateInfo.currentCompany}</span>
              )}
            </div>
          )}
        </div>

        {/* 错误提示 */}
        {error && (
          <div style={styles.errorMessage}>
            ❌ {error}
          </div>
        )}

        {/* 主要内容区域 */}
        <div style={styles.mainContent}>
          {/* 聊天区域 */}
          <div style={styles.chatSection}>
            <h2 style={styles.sectionTitle}>💬 聊天对话</h2>
            
            {/* 聊天历史 */}
            <div style={styles.chatContainer} ref={chatContainerRef}>
              {chatHistory.length === 0 ? (
                <div style={styles.emptyChat}>
                  暂无聊天记录，开始对话吧！
                </div>
              ) : (
                chatHistory.map((msg) => (
                  <div
                    key={msg.id}
                    style={{
                      ...styles.messageContainer,
                      ...(msg.sender === 'user' ? styles.userMessage : styles.katrinaMessage)
                    }}
                  >
                    <div style={styles.messageHeader}>
                      <span style={styles.sender}>
                        {msg.sender === 'user' ? '👤 您' : '🤖 Katrina'}
                      </span>
                      <span style={styles.timestamp}>
                        {formatTime(msg.timestamp)}
                      </span>
                    </div>
                    <div style={styles.messageContent}>
                      {msg.message}
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* 消息输入区域 */}
            <div style={styles.inputContainer}>
              <textarea
                style={styles.messageInput}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的消息..."
                disabled={isLoading || !email}
                rows={3}
              />
              <button
                style={{
                  ...styles.sendButton,
                  ...(isLoading ? styles.disabledButton : {})
                }}
                onClick={sendMessage}
                disabled={isLoading || !email || !message.trim()}
              >
                {isLoading ? '发送中...' : '发送消息'}
              </button>
            </div>
          </div>

          {/* 功能区域 */}
          <div style={styles.functionsSection}>
            {/* 简历上传 */}
            <div style={styles.functionCard}>
              <h3 style={styles.functionTitle}>📄 简历上传</h3>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".pdf,.doc,.docx,.txt"
                style={styles.fileInput}
                disabled={!email}
              />
              <button
                style={styles.functionButton}
                onClick={() => fileInputRef.current?.click()}
                disabled={!email}
              >
                选择简历文件
              </button>
              {uploadStatus && (
                <div style={styles.statusMessage}>
                  {uploadStatus}
                </div>
              )}
              <div style={styles.helpText}>
                支持 PDF、Word、TXT 格式，最大 10MB
              </div>
            </div>

            {/* 私人邮箱提交 */}
            <div style={styles.functionCard}>
              <h3 style={styles.functionTitle}>📧 私人邮箱</h3>
              <input
                type="email"
                style={styles.emailInput}
                value={personalEmail}
                onChange={(e) => setPersonalEmail(e.target.value)}
                placeholder="输入您的私人邮箱"
                disabled={!email}
              />
              <button
                style={styles.functionButton}
                onClick={submitPersonalEmail}
                disabled={!email || !personalEmail.trim()}
              >
                提交邮箱
              </button>
              {emailStatus && (
                <div style={styles.statusMessage}>
                  {emailStatus}
                </div>
              )}
              <div style={styles.helpText}>
                用于接收职位推荐，不会影响当前会话
              </div>
            </div>
          </div>
        </div>

        {/* 底部说明 */}
        <div style={styles.footer}>
          <p>💡 <strong>使用说明：</strong></p>
          <ul style={styles.instructions}>
            <li>在 URL 中添加 email 参数来设置会话标识，例如：<code>?email=<EMAIL></code></li>
            <li>支持电脑端和手机端使用相同 URL 实现双端同步</li>
            <li>聊天记录保存 15 天，候选人档案永久保存</li>
            <li>支持简历上传和私人邮箱提交功能</li>
          </ul>
        </div>
      </div>
    </>
  );
}

// 样式定义
const styles = {
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '20px',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#f5f5f5',
    minHeight: '100vh'
  },
  header: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '8px',
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  title: {
    margin: '0 0 15px 0',
    color: '#333',
    fontSize: '24px'
  },
  emailInfo: {
    marginBottom: '10px',
    fontSize: '16px'
  },
  emailText: {
    color: '#007bff',
    fontWeight: 'bold',
    marginLeft: '8px'
  },
  candidateInfo: {
    fontSize: '14px',
    color: '#666'
  },
  completeness: {
    color: '#28a745',
    fontWeight: 'bold',
    marginLeft: '8px',
    marginRight: '15px'
  },
  infoItem: {
    marginRight: '15px'
  },
  errorMessage: {
    backgroundColor: '#f8d7da',
    color: '#721c24',
    padding: '12px',
    borderRadius: '4px',
    marginBottom: '20px',
    border: '1px solid #f5c6cb'
  },
  mainContent: {
    display: 'grid',
    gridTemplateColumns: '2fr 1fr',
    gap: '20px',
    marginBottom: '20px'
  },
  chatSection: {
    backgroundColor: '#fff',
    borderRadius: '8px',
    padding: '20px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  sectionTitle: {
    margin: '0 0 15px 0',
    color: '#333',
    fontSize: '18px'
  },
  chatContainer: {
    height: '400px',
    overflowY: 'auto',
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '15px',
    marginBottom: '15px',
    backgroundColor: '#fafafa'
  },
  emptyChat: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    marginTop: '50px'
  },
  messageContainer: {
    marginBottom: '15px',
    padding: '10px',
    borderRadius: '8px'
  },
  userMessage: {
    backgroundColor: '#e3f2fd',
    marginLeft: '20px'
  },
  katrinaMessage: {
    backgroundColor: '#f1f8e9',
    marginRight: '20px'
  },
  messageHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '5px',
    fontSize: '12px'
  },
  sender: {
    fontWeight: 'bold'
  },
  timestamp: {
    color: '#666'
  },
  messageContent: {
    lineHeight: '1.4',
    whiteSpace: 'pre-wrap'
  },
  inputContainer: {
    display: 'flex',
    gap: '10px'
  },
  messageInput: {
    flex: 1,
    padding: '10px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    resize: 'vertical',
    fontFamily: 'inherit'
  },
  sendButton: {
    padding: '10px 20px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontWeight: 'bold'
  },
  disabledButton: {
    backgroundColor: '#6c757d',
    cursor: 'not-allowed'
  },
  functionsSection: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px'
  },
  functionCard: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  functionTitle: {
    margin: '0 0 15px 0',
    color: '#333',
    fontSize: '16px'
  },
  fileInput: {
    display: 'none'
  },
  functionButton: {
    width: '100%',
    padding: '10px',
    backgroundColor: '#28a745',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginBottom: '10px'
  },
  emailInput: {
    width: '100%',
    padding: '10px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    marginBottom: '10px',
    boxSizing: 'border-box'
  },
  statusMessage: {
    padding: '8px',
    borderRadius: '4px',
    fontSize: '14px',
    marginBottom: '10px',
    backgroundColor: '#f8f9fa',
    border: '1px solid #dee2e6'
  },
  helpText: {
    fontSize: '12px',
    color: '#666',
    fontStyle: 'italic'
  },
  footer: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  instructions: {
    margin: '10px 0',
    paddingLeft: '20px'
  }
};
