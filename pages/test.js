import { useState, useEffect, useRef } from 'react';
import Head from 'next/head';

export default function TestPage() {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [responseTime, setResponseTime] = useState(null);
  const [testScenarios, setTestScenarios] = useState([]);
  const [isRunningScenario, setIsRunningScenario] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    // 从URL参数获取email，或生成默认email
    const urlParams = new URLSearchParams(window.location.search);
    const emailParam = urlParams.get('email') || `test_${Date.now()}@example.com`;
    const sessionParam = urlParams.get('session') || `session_${Date.now()}`;
    
    setEmail(emailParam);
    setSessionId(sessionParam);
    
    // 添加初始消息
    setMessages([{
      type: 'ai',
      content: 'Hi there! I\'m an AI recruiter. I can help you find the perfect job. To get started, please upload your resume or enter your email address.',
      timestamp: new Date().toLocaleTimeString()
    }]);

    // 预设测试场景
    setTestScenarios([
      {
        name: '简单问候',
        message: '你好',
        expectedResponse: '开场白',
        expectedTime: '< 10秒'
      },
      {
        name: 'CV算法工程师完整信息',
        message: '我是做CV算法的，腾讯P7，期望薪资60万，想在北京找工作',
        expectedResponse: '4x4职位推荐',
        expectedTime: '30-60秒'
      },
      {
        name: '大模型算法工程师',
        message: '我是做大模型算法的，字节跳动高级工程师，期望薪资80万',
        expectedResponse: '4x4职位推荐',
        expectedTime: '30-60秒'
      },
      {
        name: '技术方向歧义测试',
        message: '我是做RAG的',
        expectedResponse: '歧义澄清',
        expectedTime: '30-60秒'
      }
    ]);
  }, []);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      type: 'user',
      content: inputValue,
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    const startTime = Date.now();

    try {
      // 🔧 添加请求超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const response = await fetch(`/api/chat/message?email=${encodeURIComponent(email)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue,
          sessionId: sessionId
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const endTime = Date.now();
      const responseTimeMs = endTime - startTime;
      setResponseTime(responseTimeMs);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // 🔧 修复：防止显示JSON调试信息
      let replyContent = '';
      if (data.success && data.data && data.data.reply) {
        replyContent = data.data.reply;

        // 检查是否意外包含了JSON数据
        if (replyContent.includes('{"success"') || replyContent.includes('"candidateInfo"')) {
          console.warn('⚠️ 检测到回复中包含JSON数据，使用默认错误消息');
          replyContent = '抱歉，系统出现了一些问题，请稍后再试。如果问题持续，请刷新页面重新开始对话。';
        }
      } else {
        replyContent = `Error: ${data.error || 'Unknown error'}`;
      }

      const aiMessage = {
        type: 'ai',
        content: replyContent,
        timestamp: new Date().toLocaleTimeString(),
        metadata: {
          success: data.success,
          tokensUsed: data.metadata?.tokensUsed || 0,
          responseTime: responseTimeMs,
          traceId: data.metadata?.traceId,
          recommendations: data.data?.recommendations,
          candidateInfo: data.data?.candidateInfo
        }
      };

      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      let errorContent = '';

      if (error.message.includes('timeout') || error.message.includes('timed out')) {
        errorContent = '请求超时，可能是因为您的输入触发了复杂的分析过程。请尝试更具体的描述，比如"我是做大模型算法的"或"我是做计算机视觉的"。';
      } else if (error.message.includes('Failed to fetch')) {
        errorContent = '网络连接失败，请检查网络连接后重试。';
      } else {
        errorContent = `连接错误: ${error.message}`;
      }

      const errorMessage = {
        type: 'ai',
        content: errorContent,
        timestamp: new Date().toLocaleTimeString(),
        metadata: {
          success: false,
          error: error.message,
          responseTime: Date.now() - startTime
        }
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const runTestScenario = async (scenario) => {
    if (isLoading || isRunningScenario) return;

    setIsRunningScenario(true);
    setInputValue(scenario.message);

    // 添加场景标识消息
    const scenarioMessage = {
      type: 'system',
      content: `🧪 Running Test Scenario: ${scenario.name}\nExpected: ${scenario.expectedResponse} (${scenario.expectedTime})`,
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages(prev => [...prev, scenarioMessage]);

    // 等待一秒后自动发送
    setTimeout(() => {
      sendMessage();
      setIsRunningScenario(false);
    }, 1000);
  };

  const clearChat = () => {
    setMessages([{
      type: 'ai',
      content: 'Chat cleared. Ready for new conversation.',
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  return (
    <>
      <Head>
        <title>Katrina AI Recruiter - Test Interface</title>
        <link rel="preconnect" href="https://fonts.gstatic.com/" crossOrigin="" />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?display=swap&family=Noto+Sans:wght@400;500;700;900&family=Plus+Jakarta+Sans:wght@400;500;700;800"
        />
        <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
      </Head>

      <div className="relative flex size-full min-h-screen flex-col bg-[#f8f9fc] group/design-root overflow-x-hidden" style={{fontFamily: '"Plus Jakarta Sans", "Noto Sans", sans-serif'}}>
        <div className="layout-container flex h-full grow flex-col">
          {/* Header */}
          <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7e9f3] px-10 py-3">
            <div className="flex items-center gap-4 text-[#0e101b]">
              <div className="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M13.8261 30.5736C16.7203 29.8826 20.2244 29.4783 24 29.4783C27.7756 29.4783 31.2797 29.8826 34.1739 30.5736C36.9144 31.2278 39.9967 32.7669 41.3563 33.8352L24.8486 7.36089C24.4571 6.73303 23.5429 6.73303 23.1514 7.36089L6.64374 33.8352C8.00331 32.7669 11.0856 31.2278 13.8261 30.5736Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <h2 className="text-[#0e101b] text-lg font-bold leading-tight tracking-[-0.015em]">AI Recruiter Test</h2>
            </div>
            <div className="flex items-center gap-4 text-sm text-[#4d5a99]">
              <span>Email: {email}</span>
              <span>Session: {sessionId.substring(0, 8)}...</span>
              {responseTime && <span>Last Response: {responseTime}ms</span>}
              <button
                onClick={clearChat}
                className="px-3 py-1 bg-[#e7e9f3] text-[#4d5a99] rounded hover:bg-[#d1d5db] text-xs"
              >
                Clear Chat
              </button>
            </div>
          </header>

          {/* Main Content */}
          <div className="px-10 flex flex-1 justify-center py-5">
            <div className="layout-content-container flex flex-row max-w-[1200px] flex-1 gap-6">

              {/* Test Scenarios Panel */}
              <div className="w-80 bg-white rounded-lg p-4 shadow-sm border border-[#e7e9f3]">
                <h3 className="text-lg font-semibold text-[#0e101b] mb-4">🧪 Test Scenarios</h3>
                <div className="space-y-3">
                  {testScenarios.map((scenario, index) => (
                    <div key={index} className="border border-[#e7e9f3] rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-[#0e101b] text-sm">{scenario.name}</h4>
                        <button
                          onClick={() => runTestScenario(scenario)}
                          disabled={isLoading || isRunningScenario}
                          className="px-2 py-1 bg-[#1435db] text-white text-xs rounded hover:bg-[#0f2bb8] disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Run
                        </button>
                      </div>
                      <p className="text-xs text-[#4d5a99] mb-2">{scenario.message}</p>
                      <div className="text-xs text-[#6b7280]">
                        <div>Expected: {scenario.expectedResponse}</div>
                        <div>Time: {scenario.expectedTime}</div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-3 bg-[#f8f9fc] rounded-lg">
                  <h4 className="font-medium text-[#0e101b] text-sm mb-2">💡 Testing Tips</h4>
                  <ul className="text-xs text-[#4d5a99] space-y-1">
                    <li>• Wait for full response (30-60s for complex queries)</li>
                    <li>• Check metadata for tokens used</li>
                    <li>• Verify recommendations structure</li>
                    <li>• Test error handling</li>
                  </ul>
                </div>
              </div>

              {/* Messages Container */}
              <div className="flex-1 flex flex-col">
                <div className="flex-1 overflow-y-auto mb-4" style={{maxHeight: 'calc(100vh - 200px)'}}>
                {messages.map((message, index) => (
                  <div key={index} className={`flex items-end gap-3 p-4 ${
                    message.type === 'user' ? 'justify-end' :
                    message.type === 'system' ? 'justify-center' : ''
                  }`}>
                    {message.type === 'ai' && (
                      <div
                        className="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
                        style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws")'}}
                      />
                    )}
                    
                    <div className={`flex flex-1 flex-col gap-1 ${
                      message.type === 'user' ? 'items-end' :
                      message.type === 'system' ? 'items-center' : 'items-start'
                    }`}>
                      <p className="text-[#4d5a99] text-[13px] font-normal leading-normal">
                        {message.type === 'ai' ? 'AI Recruiter' :
                         message.type === 'system' ? 'Test System' : 'You'} - {message.timestamp}
                      </p>
                      <div className={`text-base font-normal leading-normal flex max-w-[600px] rounded-xl px-4 py-3 ${
                        message.type === 'ai' ? 'bg-[#e7e9f3] text-[#0e101b]' :
                        message.type === 'system' ? 'bg-[#fef3c7] text-[#92400e] border border-[#fbbf24]' :
                        'bg-[#1435db] text-[#f8f9fc]'
                      }`}>
                        <pre className="whitespace-pre-wrap font-sans">{message.content}</pre>
                      </div>
                      
                      {/* Metadata Display */}
                      {message.metadata && (
                        <div className="text-xs text-[#4d5a99] mt-1 max-w-[600px]">
                          <div>Success: {message.metadata.success ? '✅' : '❌'}</div>
                          {message.metadata.tokensUsed > 0 && <div>Tokens: {message.metadata.tokensUsed}</div>}
                          <div>Response Time: {message.metadata.responseTime}ms</div>
                          {message.metadata.traceId && <div>Trace ID: {message.metadata.traceId}</div>}
                          {message.metadata.recommendations && (
                            <div>Recommendations: {Object.keys(message.metadata.recommendations).length} types</div>
                          )}
                          {message.metadata.candidateInfo && <div>Candidate Info: Updated</div>}
                        </div>
                      )}
                    </div>

                    {message.type === 'user' && (
                      <div
                        className="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
                        style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBIKS6O4QdwVt6lDXI60DKR5Xx1EqHNrus5Nw-6INrwVflCMUodrWubOl6M0xaYxhzH2Iudn10zzMe-kA_2gvx1PatmVmgR-2b2aOcnVlvX2-My6hQ0_LOJUhRyfNaHjPU0q2xm4E4Mew0qTZ86q2Tv64l4ylMoHYd-W9pE5EdOfguVQXyoTjb5q6h0EmlHLdaxkAegVYgqU134P74xptaxMr_hz_FcZGIl2Ihff5lpFwPH_dPzSiLvgTlfD_BE1utSeybquKu1nGU")'}}
                      />
                    )}
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex items-end gap-3 p-4">
                    <div
                      className="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
                      style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuAR9YTCONVPtk8rv6yi6wV5K9agLLBapw2-dwK4l5qXH0PXL2FD8wlMR0kgcdb9H1fbWoaVFX9GGftOSAvdvdSC1-I6ih4LOhQzJF62BSY4MohfTAwaQw156gSKZrK2dHTyWvGX9-J2pVDy85wQBDW7hfUnub0v30dJs3ZBSQUzX5FTQv6jGwtjt-6gn6moDikC3HsRLs8rPQFXRtLiRVqf9cOWTJlib9zLoklgGaStoItiDr943SlW7y0QdiEBOs9sDpvNAXTR_Ws")'}}
                    />
                    <div className="flex flex-1 flex-col gap-1 items-start">
                      <p className="text-[#4d5a99] text-[13px] font-normal leading-normal">AI Recruiter - Processing...</p>
                      <div className="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#e7e9f3] text-[#0e101b]">
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#1435db]"></div>
                          Thinking... (This may take 30-60 seconds)
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
                </div>
              </div>
            </div>
          </div>

          {/* Footer Input */}
          <footer className="flex justify-center border-t border-[#e7e9f3] bg-white">
            <div className="flex max-w-[960px] flex-1 flex-col">
              <div className="flex items-center px-4 py-3 gap-3">
                <div
                  className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 shrink-0"
                  style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBIKS6O4QdwVt6lDXI60DKR5Xx1EqHNrus5Nw-6INrwVflCMUodrWubOl6M0xaYxhzH2Iudn10zzMe-kA_2gvx1PatmVmgR-2b2aOcnVlvX2-My6hQ0_LOJUhRyfNaHjPU0q2xm4E4Mew0qTZ86q2Tv64l4ylMoHYd-W9pE5EdOfguVQXyoTjb5q6h0EmlHLdaxkAegVYgqU134P74xptaxMr_hz_FcZGIl2Ihff5lpFwPH_dPzSiLvgTlfD_BE1utSeybquKu1nGU")'}}
                />
                <div className="flex flex-1 items-stretch rounded-xl h-12">
                  <textarea
                    placeholder="Type a message (e.g., 我是做CV算法的，腾讯P7，期望薪资60万)"
                    className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0e101b] focus:outline-0 focus:ring-0 border-none bg-[#e7e9f3] focus:border-none h-full placeholder:text-[#4d5a99] px-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={isLoading}
                    rows={1}
                  />
                  <div className="flex border-none bg-[#e7e9f3] items-center justify-center pr-4 rounded-r-xl border-l-0">
                    <button
                      onClick={sendMessage}
                      disabled={isLoading || !inputValue.trim()}
                      className="min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#1435db] text-[#f8f9fc] text-sm font-medium leading-normal disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? 'Sending...' : 'Send'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </>
  );
}
