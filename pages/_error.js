/**
 * Next.js 错误页面
 * 处理应用程序中的错误情况
 */

import Head from 'next/head';

function Error({ statusCode, hasGetInitialPropsRun, err }) {
  return (
    <>
      <Head>
        <title>错误 - Katrina AI</title>
      </Head>
      <div style={styles.container}>
        <div style={styles.errorCard}>
          <h1 style={styles.title}>
            {statusCode ? `服务器错误 ${statusCode}` : '客户端错误'}
          </h1>
          
          <div style={styles.icon}>
            {statusCode >= 500 ? '🔧' : '⚠️'}
          </div>
          
          <p style={styles.message}>
            {statusCode === 404
              ? '页面未找到'
              : statusCode >= 500
              ? '服务器遇到了一些问题，请稍后再试'
              : '应用程序遇到了错误'}
          </p>
          
          <div style={styles.actions}>
            <button 
              style={styles.button}
              onClick={() => window.location.href = '/'}
            >
              返回首页
            </button>
            
            <button 
              style={styles.buttonSecondary}
              onClick={() => window.location.reload()}
            >
              刷新页面
            </button>
          </div>
          
          <div style={styles.help}>
            <p>如果问题持续存在，请检查：</p>
            <ul style={styles.helpList}>
              <li>URL 中是否包含正确的 email 参数</li>
              <li>网络连接是否正常</li>
              <li>后端服务器是否正在运行</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

const styles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    padding: '20px'
  },
  errorCard: {
    backgroundColor: 'white',
    borderRadius: '8px',
    padding: '40px',
    textAlign: 'center',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    maxWidth: '500px',
    width: '100%'
  },
  title: {
    fontSize: '24px',
    color: '#333',
    marginBottom: '20px'
  },
  icon: {
    fontSize: '48px',
    marginBottom: '20px'
  },
  message: {
    fontSize: '16px',
    color: '#666',
    marginBottom: '30px',
    lineHeight: '1.5'
  },
  actions: {
    display: 'flex',
    gap: '15px',
    justifyContent: 'center',
    marginBottom: '30px',
    flexWrap: 'wrap'
  },
  button: {
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold'
  },
  buttonSecondary: {
    backgroundColor: '#6c757d',
    color: 'white',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold'
  },
  help: {
    textAlign: 'left',
    backgroundColor: '#f8f9fa',
    padding: '20px',
    borderRadius: '4px',
    fontSize: '14px',
    color: '#666'
  },
  helpList: {
    margin: '10px 0 0 20px',
    lineHeight: '1.6'
  }
};

export default Error;
