/**
 * Katrina AI 全局样式
 * 包含响应式设计，支持电脑端和手机端
 */

/* 重置默认样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

/* 移动端适配 */
@media (max-width: 768px) {
  /* 主容器移动端样式 */
  .container {
    padding: 10px !important;
  }

  /* 主内容区域在移动端改为单列布局 */
  .main-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
  }

  /* 聊天容器在移动端调整高度 */
  .chat-container {
    height: 300px !important;
  }

  /* 输入容器在移动端改为垂直布局 */
  .input-container {
    flex-direction: column !important;
  }

  /* 消息输入框在移动端调整 */
  .message-input {
    margin-bottom: 10px !important;
  }

  /* 发送按钮在移动端全宽 */
  .send-button {
    width: 100% !important;
  }

  /* 功能按钮在移动端调整 */
  .function-button {
    font-size: 16px !important;
    padding: 12px !important;
  }

  /* 头部信息在移动端调整 */
  .header {
    padding: 15px !important;
  }

  .title {
    font-size: 20px !important;
  }

  .email-info {
    font-size: 14px !important;
  }

  /* 候选人信息在移动端换行显示 */
  .candidate-info {
    display: flex !important;
    flex-direction: column !important;
    gap: 5px !important;
  }

  .info-item {
    margin-right: 0 !important;
  }

  /* 消息容器在移动端调整边距 */
  .user-message {
    margin-left: 10px !important;
  }

  .katrina-message {
    margin-right: 10px !important;
  }

  /* 功能卡片在移动端调整 */
  .function-card {
    padding: 15px !important;
  }

  .function-title {
    font-size: 14px !important;
  }

  /* 底部说明在移动端调整 */
  .footer {
    padding: 15px !important;
  }

  .instructions {
    font-size: 12px !important;
    padding-left: 15px !important;
  }
}

/* 小屏幕设备（手机竖屏） */
@media (max-width: 480px) {
  .container {
    padding: 5px !important;
  }

  .header {
    padding: 10px !important;
  }

  .title {
    font-size: 18px !important;
  }

  .chat-container {
    height: 250px !important;
    padding: 10px !important;
  }

  .message-input {
    font-size: 16px !important; /* 防止 iOS 缩放 */
  }

  .function-card {
    padding: 10px !important;
  }
}

/* 滚动条样式 */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 按钮悬停效果 */
.send-button:hover:not(:disabled) {
  background-color: #0056b3 !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.function-button:hover:not(:disabled) {
  background-color: #218838 !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 禁用状态样式 */
.send-button:disabled,
.function-button:disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
  transform: none !important;
}

/* 输入框焦点样式 */
.message-input:focus,
.email-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 状态消息样式增强 */
.status-message {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 错误消息动画 */
.error-message {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 消息气泡动画 */
.message-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 代码样式 */
code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  color: #e83e8c;
}

/* 链接样式 */
a {
  color: #007bff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 表单元素通用样式 */
input, textarea, button {
  font-family: inherit;
}

/* 文件输入样式增强 */
.file-input-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
  width: 100%;
}

.file-input-wrapper input[type=file] {
  position: absolute;
  left: -9999px;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* 打印样式 */
@media print {
  .function-card,
  .footer {
    display: none;
  }
  
  .chat-container {
    height: auto !important;
    overflow: visible !important;
  }
}
