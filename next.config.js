/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // 环境变量配置
  env: {
    CUSTOM_KEY: 'katrina-ai-chat',
  },

  // API 路由配置
  async rewrites() {
    return [
      // 如果需要代理到其他服务，可以在这里配置
    ];
  },

  // 图片优化配置
  images: {
    domains: [],
  },

  // 实验性功能
  experimental: {
    // 启用 SWC 编译器的额外功能
  },

  // 构建配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 自定义 webpack 配置
    return config;
  },

  // 压缩配置
  compress: true,

  // 国际化配置（如果需要）
  i18n: {
    locales: ['zh-CN', 'en'],
    defaultLocale: 'zh-CN',
  },
};

export default nextConfig;
