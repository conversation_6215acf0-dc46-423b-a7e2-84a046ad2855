/**
 * 主API服务器 - 启动Next.js应用
 * 为技术方向映射测试提供API服务
 */

import { createServer } from 'http';
import { parse } from 'url';
import next from 'next';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3002;

// 创建Next.js应用实例
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

async function startServer() {
    try {
        console.log('🚀 准备启动Next.js应用...');
        
        // 准备Next.js应用
        await app.prepare();
        
        console.log('✅ Next.js应用准备完成');
        
        // 创建HTTP服务器
        const server = createServer(async (req, res) => {
            try {
                const parsedUrl = parse(req.url, true);
                
                // 添加健康检查端点
                if (parsedUrl.pathname === '/health') {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        status: 'ok',
                        message: '主API服务器运行正常',
                        timestamp: new Date().toISOString(),
                        port: port
                    }));
                    return;
                }
                
                // 处理所有其他请求
                await handle(req, res, parsedUrl);
                
            } catch (err) {
                console.error('请求处理错误:', err);
                res.statusCode = 500;
                res.end('Internal Server Error');
            }
        });
        
        // 启动服务器
        server.listen(port, (err) => {
            if (err) throw err;
            
            console.log('\n🎉 主API服务器启动成功！');
            console.log('=' .repeat(50));
            console.log(`📡 服务地址: http://${hostname}:${port}`);
            console.log(`🔗 聊天API: http://${hostname}:${port}/api/chat/message`);
            console.log(`💚 健康检查: http://${hostname}:${port}/health`);
            console.log('=' .repeat(50));
            console.log('\n📋 可用的API端点:');
            console.log('- POST /api/chat/message - 聊天消息处理');
            console.log('- GET  /api/chat/history - 聊天历史');
            console.log('- POST /api/chat/submitEmail - 提交邮箱');
            console.log('- POST /api/chat/uploadResume - 上传简历');
            console.log('- GET  /health - 健康检查');
            console.log('\n💡 技术方向映射重构已完成，可以开始测试！');
        });
        
        // 优雅关闭处理
        process.on('SIGTERM', () => {
            console.log('\n🛑 收到SIGTERM信号，正在关闭服务器...');
            server.close(() => {
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });
        
        process.on('SIGINT', () => {
            console.log('\n🛑 收到SIGINT信号，正在关闭服务器...');
            server.close(() => {
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });
        
    } catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}

// 启动服务器
startServer();
