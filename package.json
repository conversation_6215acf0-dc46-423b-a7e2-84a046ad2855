{"name": "katrina-ai-chatbot", "version": "1.0.0", "description": "Katrina AI 猎头聊天机器人 - 数据库基础构建与静态数据准备", "main": "import-data.js", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "import-data": "node import-data.js", "setup": "npm install && echo '请确保 .env.local 文件已正确配置'", "verify-env": "node -e \"require('dotenv').config({path:'.env.local'}); console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ 已配置' : '❌ 未配置'); console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ 已配置' : '❌ 未配置');\"", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --verbose --no-cache", "test:unit": "node --experimental-vm-modules node_modules/jest/bin/jest.js unit --verbose --no-cache", "test:integration": "node --experimental-vm-modules node_modules/jest/bin/jest.js integration --verbose --no-cache", "test:auto": "node scripts/auto-test.js", "test:file": "node scripts/auto-test.js --file", "test:watch": "node automated-tests/run-tests.js watch", "test:coverage": "node automated-tests/run-tests.js coverage", "test:report": "node automated-tests/run-tests.js report", "test:clean": "node automated-tests/run-tests.js clean", "test:mapping": "node start-test-environment.js", "test:ui": "node test-server.js", "server": "node server.js", "test:backend": "node test-backend-apis.js", "test:concurrency": "node test-concurrency.js", "frontend": "node start-frontend.js", "deploy": "./deploy-to-vercel.sh", "deploy:vercel": "vercel --prod", "lint": "next lint"}, "keywords": ["chatbot", "ai", "recruitment", "supabase", "database"], "author": "Katrina AI Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.4", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^5.1.0", "formidable": "^3.5.4", "next": "^15.3.5", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/katrina-ai-chatbot.git"}, "bugs": {"url": "https://github.com/your-org/katrina-ai-chatbot/issues"}, "homepage": "https://github.com/your-org/katrina-ai-chatbot#readme", "devDependencies": {"@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@eslint/js": "^9.32.0", "@jest/globals": "^30.0.4", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "jest": "^30.0.4", "supertest": "^7.1.3", "typescript": "^5.8.3"}}