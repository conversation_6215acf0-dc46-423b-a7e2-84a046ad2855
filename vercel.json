{"version": 2, "name": "katrina-ai-chatbot", "builds": [{"src": "package.json", "use": "@vercel/next"}], "env": {"SUPABASE_URL": "@supabase-url", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "DEEPSEEK_API_KEY": "@deepseek-api-key", "LLM_API_ENDPOINT": "https://api.deepseek.com/v1"}, "functions": {"pages/api/**/*.js": {"maxDuration": 30}}, "regions": ["hkg1", "sin1", "nrt1"], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Cache-Control", "value": "s-maxage=1, stale-while-revalidate"}]}]}