#!/usr/bin/env node

/**
 * AI招聘助手系统测试脚本
 * 用于验证系统各个模块的基本功能
 */

import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ==================== 测试配置 ====================

const TEST_CONFIG = {
  timeout: 10000, // 10秒超时
  verbose: process.argv.includes("--verbose"),
  skipAI: process.argv.includes("--skip-ai"),
  skipDB: process.argv.includes("--skip-db"),
};

// ==================== 测试工具函数 ====================

class TestRunner {
  constructor() {
    this.tests = [];
    this.results = {
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
    };
  }

  addTest(name, testFn, options = {}) {
    this.tests.push({
      name,
      testFn,
      skip: options.skip || false,
      timeout: options.timeout || TEST_CONFIG.timeout,
    });
  }

  async runTests() {
    console.log("🧪 开始运行系统测试...\n");

    for (const test of this.tests) {
      this.results.total++;

      if (test.skip) {
        console.log(`⏭️  跳过: ${test.name}`);
        this.results.skipped++;
        continue;
      }

      try {
        console.log(`🔄 运行: ${test.name}`);

        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("测试超时")), test.timeout);
        });

        await Promise.race([test.testFn(), timeoutPromise]);

        console.log(`✅ 通过: ${test.name}`);
        this.results.passed++;
      } catch (error) {
        console.log(`❌ 失败: ${test.name}`);
        if (TEST_CONFIG.verbose) {
          console.log(`   错误: ${error.message}`);
        }
        this.results.failed++;
      }
      console.log("");
    }

    this.printSummary();
  }

  printSummary() {
    console.log("📊 测试结果汇总:");
    console.log(`   总计: ${this.results.total}`);
    console.log(`   ✅ 通过: ${this.results.passed}`);
    console.log(`   ❌ 失败: ${this.results.failed}`);
    console.log(`   ⏭️  跳过: ${this.results.skipped}`);
    console.log("");

    const successRate =
      this.results.total > 0
        ? (
            (this.results.passed /
              (this.results.total - this.results.skipped)) *
            100
          ).toFixed(1)
        : 0;

    if (this.results.failed === 0) {
      console.log(`🎉 所有测试通过! 成功率: ${successRate}%`);
    } else {
      console.log(
        `⚠️  有 ${this.results.failed} 个测试失败，成功率: ${successRate}%`
      );
    }
  }
}

// ==================== 具体测试用例 ====================

const testRunner = new TestRunner();

// 1. 文件结构测试
testRunner.addTest("文件结构完整性检查", async () => {
  const requiredFiles = [
    "src/index.js",
    "src/core/message-processor.js",
    "src/core/database-manager.js",
    "src/core/ai-services.js",
    "src/core/passive-recommender.js",
    "src/core/active-recommender.js",
    "src/core/tech-mapper.js",
    "src/config/app-config.js",
    "src/config/mapping-tables.js",
    "src/config/utilities.js",
    "src/config/validators.js",
    "src/api/api-routes.js",
    "src/api/user-manager.js",
    "src/frontend/chat-interface.js",
    "src/frontend/ui-components.js",
    "package.json",
    "demo.html",
    "start.js",
  ];

  const missingFiles = requiredFiles.filter(
    (file) => !existsSync(join(__dirname, file))
  );

  if (missingFiles.length > 0) {
    throw new Error(`缺少文件: ${missingFiles.join(", ")}`);
  }
});

// 2. 模块导入测试
testRunner.addTest("核心模块导入测试", async () => {
  try {
    // 测试配置模块
    const appConfig = await import("./src/config/app-config.js");
    if (!appConfig.APP_CONFIG) throw new Error("APP_CONFIG未导出");

    const mappingTables = await import("./src/config/mapping-tables.js");
    if (!mappingTables.INTENT_TYPES) throw new Error("INTENT_TYPES未导出");

    const utilities = await import("./src/config/utilities.js");
    if (!utilities.normalizeInput) throw new Error("normalizeInput函数未导出");

    const validators = await import("./src/config/validators.js");
    if (!validators.validateUserMessage)
      throw new Error("validateUserMessage函数未导出");

    // 测试核心模块
    const databaseManager = await import("./src/core/database-manager.js");
    if (!databaseManager.databaseManager)
      throw new Error("databaseManager未导出");

    const aiServices = await import("./src/core/ai-services.js");
    if (!aiServices.aiServices) throw new Error("aiServices未导出");

    const messageProcessor = await import("./src/core/message-processor.js");
    if (!messageProcessor.messageProcessor)
      throw new Error("messageProcessor未导出");
  } catch (error) {
    throw new Error(`模块导入失败: ${error.message}`);
  }
});

// 3. 工具函数测试
testRunner.addTest("工具函数功能测试", async () => {
  const { normalizeInput, parseSalaryRange, validateEmail } = await import(
    "./src/config/utilities.js"
  );

  // 测试输入标准化
  const normalized = normalizeInput("  Hello World!  ");
  if (normalized !== "helloworld") {
    throw new Error(`输入标准化失败: 期望 'helloworld', 得到 '${normalized}'`);
  }

  // 测试薪资解析
  const salary = parseSalaryRange("25-30万");
  if (!salary || salary.min !== 25 || salary.max !== 30) {
    throw new Error("薪资解析失败");
  }

  // 测试邮箱验证
  if (!validateEmail("<EMAIL>")) {
    throw new Error("邮箱验证失败");
  }
});

// 4. 验证器测试
testRunner.addTest("数据验证器测试", async () => {
  const { validateUserMessage, validateSessionUuid } = await import(
    "./src/config/validators.js"
  );

  // 测试消息验证
  const validMessage = validateUserMessage("这是一条测试消息");
  if (!validMessage.valid) {
    throw new Error("有效消息验证失败");
  }

  const invalidMessage = validateUserMessage("");
  if (invalidMessage.valid) {
    throw new Error("无效消息应该验证失败");
  }

  // 测试UUID验证
  const validUuid = validateSessionUuid("123e4567-e89b-12d3-a456-************");
  if (!validUuid.valid) {
    throw new Error("有效UUID验证失败");
  }
});

// 5. 系统初始化测试
testRunner.addTest(
  "系统初始化测试",
  async () => {
    const systemModule = await import("./src/index.js");
    const aiSystem = systemModule.default;

    if (!aiSystem) {
      throw new Error("系统实例未导出");
    }

    // 检查系统状态
    const status = aiSystem.getSystemStatus();
    if (!status || typeof status.initialized !== "boolean") {
      throw new Error("系统状态格式错误");
    }

    // 测试系统初始化（跳过数据库连接）
    if (!TEST_CONFIG.skipDB) {
      try {
        const result = await aiSystem.initialize();
        if (!result || typeof result.success !== "boolean") {
          throw new Error("初始化返回格式错误");
        }
      } catch (error) {
        // 如果是数据库连接错误，这是预期的
        if (
          error.message.includes("数据库") ||
          error.message.includes("SUPABASE")
        ) {
          console.log("   ℹ️  数据库连接失败（预期，需要配置环境变量）");
        } else {
          throw error;
        }
      }
    }
  },
  { skip: TEST_CONFIG.skipDB }
);

// 6. API路由测试
testRunner.addTest("API路由模块测试", async () => {
  const apiRoutes = await import("./src/api/api-routes.js");

  if (!apiRoutes.API_ROUTES) {
    throw new Error("API_ROUTES未导出");
  }

  if (!apiRoutes.handleChatMessage) {
    throw new Error("handleChatMessage函数未导出");
  }

  if (!apiRoutes.corsMiddleware) {
    throw new Error("corsMiddleware函数未导出");
  }

  // 检查路由映射
  const routes = apiRoutes.API_ROUTES;
  const expectedRoutes = [
    "POST /api/chat",
    "GET /api/health",
    "GET /api/status",
  ];

  for (const route of expectedRoutes) {
    if (!routes[route]) {
      throw new Error(`缺少路由: ${route}`);
    }
  }
});

// 7. 前端组件测试（跳过，因为需要浏览器环境）
testRunner.addTest(
  "前端组件模块测试",
  async () => {
    const chatInterface = await import("./src/frontend/chat-interface.js");
    const uiComponents = await import("./src/frontend/ui-components.js");

    if (!chatInterface.default) {
      throw new Error("ChatInterface类未导出");
    }

    if (!uiComponents.createButton) {
      throw new Error("createButton函数未导出");
    }

    if (!uiComponents.getDefaultStyles) {
      throw new Error("getDefaultStyles函数未导出");
    }

    // 跳过DOM操作测试，因为在Node.js环境中没有document对象
    console.log("   ℹ️  跳过DOM操作测试（需要浏览器环境）");
  },
  { skip: true }
);

// 8. 消息处理测试（模拟）
testRunner.addTest("消息处理逻辑测试", async () => {
  const { messageProcessor } = await import("./src/core/message-processor.js");

  // 测试固定回复检查
  const fixedReply = messageProcessor.checkFixedResponses("你好");
  // 这个测试可能会失败，因为可能没有配置固定回复

  // 测试路由选择
  const mockContext = {
    intent: "general_chat",
    confidence: 0.8,
    entities: {},
    context: {},
  };

  const handler = messageProcessor.selectHandler(mockContext, "测试消息");
  if (typeof handler !== "string") {
    throw new Error("路由选择返回类型错误");
  }
});

// 9. 启动脚本测试
testRunner.addTest("启动脚本功能测试", async () => {
  // 检查启动脚本文件是否存在
  const startPath = join(__dirname, "start.js");
  if (!existsSync(startPath)) {
    throw new Error("start.js文件不存在");
  }

  // 启动脚本主要是执行逻辑，这里只检查文件存在性
  console.log("   ℹ️  启动脚本文件检查通过");
});

// 10. 演示页面测试
testRunner.addTest("演示页面文件检查", async () => {
  const demoPath = join(__dirname, "demo.html");
  if (!existsSync(demoPath)) {
    throw new Error("demo.html文件不存在");
  }

  // 可以添加更多HTML内容检查
});

// ==================== 运行测试 ====================

async function main() {
  console.log("🤖 AI招聘助手系统测试");
  console.log("========================\n");

  // 设置测试环境变量
  process.env.NODE_ENV = "test";
  process.env.SUPABASE_URL = "https://test.supabase.co";
  process.env.SUPABASE_ANON_KEY = "test_anon_key";
  process.env.ENABLE_AI_SERVICES = "false";

  if (TEST_CONFIG.skipAI) {
    console.log("ℹ️  跳过AI相关测试");
  }

  if (TEST_CONFIG.skipDB) {
    console.log("ℹ️  跳过数据库相关测试");
  }

  if (TEST_CONFIG.verbose) {
    console.log("ℹ️  详细模式已启用");
  }

  console.log("");

  try {
    await testRunner.runTests();

    if (testRunner.results.failed === 0) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ 测试运行失败:", error);
    process.exit(1);
  }
}

// 处理命令行参数
if (process.argv.includes("--help")) {
  console.log(`
AI招聘助手系统测试脚本

用法:
  node test-system.js [选项]

选项:
  --verbose     显示详细错误信息
  --skip-ai     跳过AI相关测试
  --skip-db     跳过数据库相关测试
  --help        显示帮助信息

示例:
  node test-system.js                # 运行所有测试
  node test-system.js --verbose      # 详细模式
  node test-system.js --skip-db      # 跳过数据库测试
`);
  process.exit(0);
}

// 运行测试
main().catch((error) => {
  console.error("❌ 测试失败:", error);
  process.exit(1);
});
